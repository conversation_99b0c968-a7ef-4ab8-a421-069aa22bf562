const httpStatus = require("http-status");
const { AppNotification } = require("../models");
const ApiError = require("../utils/ApiError");
const { sendToTopic } = require("../microservices/notification.service");
const { default: mongoose } = require("mongoose");

async function createNotification(notificationData) {
  const newNotification = new AppNotification(notificationData);
  return await newNotification.save();
}

async function sendNotification(notificationData, topic) {
  const response = await sendToTopic(
    topic,
    {
      title: notificationData.title,
      body: notificationData.description,
      image: notificationData.image ? notificationData.image.url : "", // ✅ Ensure `body` is a string
    },
    {
      type: notificationData.type,
    }
  );

  if (!response) {
    throw new Error(response.message);
  }
  return response;
}

async function createAndSendNotification(data, topic) {
  // ✅ Store in DB & Send to FCM
  const notificationData = {
    title: data.title,
    description: data.description,
    type: data.type,
    targetUser: data.targetUser,
    targetRole: data.targetRole || null,
    scheduledAt: Date.now(),
    isCreatedByAdmin: data.isCreatedByAdmin || false,
    image: data.image,
  };

  // ✅ Send to FCM (Only Required Fields)
  await sendNotification(notificationData, topic);

  // ✅ Save in MongoDB
  const createdNotification = await createNotification(notificationData);

  return createdNotification;
}

async function getNotifications(
  filters = {},
  options = {},
  userObjectId = null,
  role
) {
  // Handle deleted notifications
  if (role === "Admin") {
    if (filters.type === "deleted") {
      filters.isDeletedByAdmin = true;
      delete filters.type;
    } else {
      filters.isDeletedByAdmin = false;
    }
  }
  // Construct dynamic OR filter if isRead is passed
  if (filters.isRead !== undefined) {
    let isRead = filters.isRead;

    // Convert string 'true'/'false' to real boolean
    if (typeof isRead === "string") {
      isRead = isRead.trim().toLowerCase() === "true";
    }

    delete filters.isRead; // Remove from base filter to avoid conflict

    if (isRead === true) {
      // "isRead = true" scenario
      filters.$or = [
        {
          targetUser: new mongoose.Types.ObjectId(userObjectId),
          isRead: true,
        },
        {
          targetRole: { $in: [role, "All"] },
          readBy: { $in: [new mongoose.Types.ObjectId(userObjectId)] },
        },
      ];
    } else {
      // "isRead = false" scenario
      filters.$or = [
        {
          targetUser: new mongoose.Types.ObjectId(userObjectId),
          isRead: false,
        },
        {
          targetRole: { $in: [role, "All"] },
          readBy: { $nin: [new mongoose.Types.ObjectId(userObjectId)] },
        },
      ];
    }
  }
  // console.log(filters);
  const notifications = await AppNotification.paginate(filters, options);
  return notifications;
}

async function getRoleNotifications(targetRole, options) {
  try {
    const notifications = await AppNotification.paginate(
      { targetRole },
      options
    );

    if (!notifications || notifications.results.length === 0) {
      throw new ApiError(
        httpStatus.NOT_FOUND,
        "No notifications found for this role"
      );
    }

    return notifications;
  } catch (error) {
    throw error;
  }
}

async function updateNotification(id, updateData) {
  try {
    const updatedNotification = await AppNotification.findByIdAndUpdate(
      id,
      updateData,
      {
        new: true,
        runValidators: true,
      }
    );

    if (!updatedNotification) {
      throw new ApiError(httpStatus.NOT_FOUND, "Notification not found");
    }

    return updatedNotification;
  } catch (error) {
    throw error;
  }
}

async function deleteNotification(id) {
  const deletedNotification = await AppNotification.findByIdAndDelete(id);

  if (!deletedNotification) {
    throw new ApiError(httpStatus.NOT_FOUND, "Notification not found");
  }

  return deletedNotification;
}

async function markDeletedForAdmin(id) {
  const deletedNotification = await AppNotification.findByIdAndUpdate(
    id,
    { isDeletedByAdmin: true },
    { new: true }
  );

  if (!deletedNotification) {
    throw new ApiError(httpStatus.NOT_FOUND, "Notification not found");
  }

  return deletedNotification;
}

async function getNotificationById(id) {
  const notification = await AppNotification.findById(id);

  return notification;
}

async function getUnreadCount(userId, role) {
  const userObjectId = new mongoose.Types.ObjectId(userId); // ensure ObjectId

  const count = await AppNotification.countDocuments({
    $or: [
      // 🎯 Direct user-targeted notifications
      {
        targetUser: new mongoose.Types.ObjectId(userObjectId),
        isRead: false,
      },
      // 🌍 Role-based or global notifications
      {
        targetRole: { $in: [role, "All"] },
        readBy: { $nin: [new mongoose.Types.ObjectId(userObjectId)] },
      },
    ],
  });
  return count;
}

const markAllNotificationsService = async (userId, userRole, isRead) => {
  if (isRead === undefined) {
    throw new Error("'isRead' must be either true or false");
  }

  const [targetUserUpdate, targetRoleUpdate] = await Promise.all([
    // Case 1: Directly update `isRead` where targetUser matches the user
    AppNotification.updateMany({ targetUser: userId }, { $set: { isRead } }),

    // Case 2: Update `readBy` array based on isRead
    isRead
      ? AppNotification.updateMany(
          {
            targetUser: { $ne: userId },
            $or: [{ targetRole: "All" }, { targetRole: userRole }],
          },
          { $addToSet: { readBy: userId } }
        )
      : AppNotification.updateMany(
          {
            targetUser: { $ne: userId },
            $or: [{ targetRole: "All" }, { targetRole: userRole }],
          },
          { $pull: { readBy: userId } }
        ),
  ]);

  return {
    updatedTargetUserCount: targetUserUpdate.modifiedCount,
    updatedTargetRoleCount: targetRoleUpdate.modifiedCount,
  };
};

module.exports = {
  createNotification,
  sendNotification,
  createAndSendNotification,
  getNotifications,
  getRoleNotifications,
  updateNotification,
  deleteNotification,
  getNotificationById,
  markDeletedForAdmin,
  getUnreadCount,
  markAllNotificationsService,
};
