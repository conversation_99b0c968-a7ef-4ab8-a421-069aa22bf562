const config = require("../config/config");

const dbOptions = {
  page: 1,
  limit: 10,
  sortBy: "createdAt",
  sortOrder: "desc",
};

const imgTypeToExtension = {
  "image/jpeg": "jpeg",
  "image/png": "png",
  "image/jpg": "jpg",
  "image/svg": "svg",
  "image/svg+xml": "svg+xml",
  "image/heic": "heic",
  "image/heif": "heif",
};

const docTypeToExtension = {
  "text/plain": "txt",
  "application/pdf": "pdf",
  "application/msword": "doc",
  "application/vnd.ms-excel": "xls",
  "application/vnd.ms-powerpoint": "ppt",
  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": "xlsx",
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
    "docx",
  "application/vnd.openxmlformats-officedocument.presentationml.presentation":
    "pptx",
};

const videoTypeToExtension = {
  "video/mp4": "mp4",
  "video/avi": "avi",
  "video/mkv": "mkv",
};
const audioTypeToExtension = {
  "audio/mpeg": "mp3",
  "audio/wav": "wav",
};

const mimetypeToExtension = {
  ...imgTypeToExtension,
  ...docTypeToExtension,
};

const imageTypes = Object.keys(imgTypeToExtension);
const docTypes = Object.keys(docTypeToExtension);
const videoTypes = Object.keys(videoTypeToExtension);
const audioTypes = Object.keys(audioTypeToExtension);

const fileTypes = [...imageTypes, ...docTypes, ...videoTypes, ...audioTypes];

const userTypes = {
  ALL: "All",
  // Add all user discriminators here
  ADMIN: "Admin",
  USER: "User",
  ADVISER: "Adviser",
};
const notificationCategories = {
  CHAT: "chat",
  VIDEOCALL: "videocall",
  APPOINTMENTS: "appointments",
  GENERAL: "general",
  PROMOTIONAL: "promotional",
  QUERY: "query",
  CALLEND: "endcall",
  FOLLOWUP: "follow-up",
  SUBSCRIPTION: "subscription",
  VERIFICATION: "verification",
  BLOCKED: "blocked",
};

const emitterEventNames = {
  USER_SIGNED_UP: "user signed up",
  ADVISER_SIGNED_UP: "adviser signed up",
  APPOINTMENT_NOTIFICATIONS: "appointment notification",
  APPOINTMENT_REMINDER: "appointment reminder",
  APPOINTMENT_CANCELLED: "appointment cancelled",
  APPOINTMENT_RESCHEDULED: "appointment rescheduled",
  SUBSCRIPTION_CREATED: "subscription created",
  SUBSCRIPTION_RENEWED: "subscription renewed",
  SESSION_PAYMENT_CAPTURE: "payment capture",
  REFERRAL_REWARD: "referral reward",
  ADVISER_PAYOUT: "adviser payout",
  ADVISER_VERIFIED: "adviser verified",
  DOCUMENT_REJECTED: "document rejected",
  DOCUMENT_CLEANUP: "document cleanup",
  PROFILE_PIC_CLEANUP: "profile picture cleanup",
  VIDEO_CALL_NOTIFICATION: "video call notification",
  STRIPE_CONNECTED_ACCOUNT_VERIFIED: "stripe connected account verified",
  STRIPE_TRANSFER_CREATED: "stripe transfer created",
  STRIPE_CHECKOUT_SESSION_COMPLETED: "stripe checkout session completed",
  SUBSCRIPTION_REMINDER: "subscription reminder",
  APPOINTMENT_FEEDBACK_FOLLOWUP: "appointment feedback followup",
  QUERY_STATUS_UPDATED: "query status updated",
  APPOINTMENT_COMPLETED: "appointment completed",
  SCHEDULE_SUBSCRIPTION_CHECK: "schedule subscription check",
  REFUND_INITIATED: "refund initiated",
  REFUND_SUCCEEDED: "refund succeeded",
  SEND_LOGIN_CREDENTIALS: "send login credentials",
  APPOINTMENT_ACCEPTED: "appointment accepted",
  PAYMENT_SUCCEEDED: "payment succeeded",
  PAYMENT_FAILED: "payment failed",
  SUBSCRIPTION_PAYMENT_FAILED: "subscription payment failed",
  SYSTEM_CANCELLATION: "system appointment cancellation ",
  TRANSFER_COMPLETED: "transfer completed",
  REFUND_FAILED: "refund failed",
  TRANSFER_FAILED: "transfer failed",
  SUBSCRIPTION_SESSION_RETURNED: "subscription session returned",
  USER_MISSED_APPOINTMENT: "user missed appointment",
  STRIPE_PAYOUT_PAID: "Payout Success",
};

const appDefaults = {
  CURRENCY: "gbp",
  TIMEZONE: config.env === "development" ? "Asia/Kolkata" : "Europe/London",
};
const adviserFeedbackResponses = {
  QUESTION_ANSWERED: "Client’s Question Answered",
  COULD_NOT_ASSIST: "Couldn’t Assist Client",
  FOLLOW_UP_RECOMMENDED: "Recommended a Money Owls Follow-Up Call",
  NEEDS_REGULATED_GUIDANCE: "Client May Need Regulated Financial Guidance",
};

module.exports = {
  userTypes,
  dbOptions,
  imageTypes,
  docTypes,
  fileTypes,
  imgTypeToExtension,
  docTypeToExtension,
  mimetypeToExtension,
  notificationCategories,
  emitterEventNames,
  appDefaults,

  adviserFeedbackResponses,
};
