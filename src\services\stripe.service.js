const config = require("../config/config");
const { CustomerStripe } = require("../models");
const { appDefaults } = require("../constants");
const ApiError = require("../utils/ApiError");
const httpStatus = require("http-status");

const stripe = require("stripe")(config.stripe.secretKey);

const constructEvent = (requestRawBody, signatureHeader, secretKey) => {
  try {
    return stripe.webhooks.constructEvent(
      requestRawBody,
      signatureHeader,
      secretKey
    );
  } catch (err) {
    throw new ApiError(httpStatus.BAD_REQUEST, `Webhook Error: ${err.message}`);
  }
};

exports.addCard = async (user, stripeToken) => {
  try {
    if (!user || !stripeToken) {
      throw new Error("Missing required parameters: user or stripeToken");
    }

    let cusId = user.stripeCustomerId;
    let stripeCustomer;

    if (!cusId) {
      try {
        stripeCustomer = await stripe.customers.create({
          email: user.user.email,
        });

        cusId = stripeCustomer.id;

        await CustomerStripe.findOneAndUpdate(
          { _id: user.user._id },
          { stripeCustomerId: cusId },
          { new: true }
        );

        // console.log(
        //   "✅ Stripe customer created and user updated successfully."
        // );
      } catch (customerErr) {
        console.error("❌ Failed to create Stripe customer:", customerErr);
        throw customerErr; // 👈 Keep the original error
      }
    }

    // console.log("🔹 Adding card to Stripe customer...", stripeToken);

    let addedCard;
    try {
      addedCard = await stripe.customers.createSource(cusId, {
        source: stripeToken,
      });

      // console.log("✅ Card added to Stripe customer:", addedCard.id);
    } catch (cardErr) {
      console.error("❌ Failed to add card to Stripe customer:", cardErr);
      throw cardErr; // 👈 Keep the original error
    }

    try {
      const updatedCustomer = await CustomerStripe.findOneAndUpdate(
        { user: user.user._id },
        { $push: { stripeCardId: addedCard.id } },
        { new: true }
      );

      // console.log("✅ User's card list updated in DB:", updatedCustomer);
    } catch (dbErr) {
      console.error("❌ Failed to update user's card list in DB:", dbErr);
      throw dbErr; // 👈 Keep the original error
    }

    return {
      status: true,
      data: addedCard,
    };
  } catch (err) {
    const isStripeError = err?.raw || err?.type?.includes("Stripe");

    if (isStripeError) {
      const error = new ApiError(err.statusCode || 400, err.message);
      error.stripe = {
        message: err.message,
      };
      throw error;
    } else {
      throw new ApiError(400, err.message || "Unknown error occurred");
    }
  }
};

exports.refundPayment = async (
  paymentIntentId,
  appointmentId,
  amountInCents = null
) => {
  try {
    const refundParams = {
      payment_intent: paymentIntentId,
      metadata: {
        appointmentId: appointmentId.toString(),
      },
    };

    // If partial amount is specified, include it in the refund
    if (amountInCents) {
      refundParams.amount = amountInCents;
    }

    const refund = await stripe.refunds.create(refundParams);

    // console.log("Payment refunded:", refund);

    return {
      success: true,
      refundId: refund.id,
    };
  } catch (error) {
    console.error("Refund failed:", error);

    return {
      success: false,
      code: error.raw?.code || "stripe_error",
      message: error.raw?.message || "Refund failed",
    };
  }
};

// // subscription's methods
// exports.createStripeProductAndPrice = async (body) => {
//   try {
//     const product = await stripe.products.create({
//       name: body.title,
//     });
//     console.log("product", product);

//     const stripePrice = [];

//     await Promise.all(
//       body.stripePrice.map(async (element) => {
//         const price = await stripe.prices.create({
//           unit_amount: (element.price * 100).toFixed(),
//           currency: "mxn",
//           recurring: { interval: element.duration },
//           product: product.id,
//         });

//         stripePrice.push({
//           id: price.id,
//           amount: element.price,
//           duration: element.duration,
//         });
//       })
//     );

//     return {
//       status: true,
//       product: product,
//       stripePrice,
//     };
//   } catch (err) {
//     console.log(err);
//     return {
//       status: false,
//       message: err.message,
//     };
//   }
// };

// // subscription's methods
// exports.updateStripeProductAndPrice = async (body) => {
//   try {
//     const product = await stripe.products.update(body.stripeProductId, {
//       name: body.title,
//     });
//     console.log("product", product);

//     const stripePrice = [];

//     await Promise.all(
//       body.stripePrice.map(async (element) => {
//         console.log(element);
//         const price = await stripe.prices.update(element.id, {
//           unit_amount: (element.price * 10).toFixed(),
//           currency: appdefaults.currency,
//           recurring: { interval: element.duration },
//         });

//         stripePrice.push({
//           id: price.id,
//           amount: element.price,
//           duration: element.duration,
//         });
//       })
//     );

//     return {
//       status: true,
//       product: product,
//       stripePrice,
//     };
//   } catch (err) {
//     console.log(err);
//     return {
//       status: false,
//       message: err.message,
//     };
//   }
// };

// // add subscriptions
// exports.createSubscriptionForUser = async (
//   stripeCustomerId,
//   cardId,
//   stripePriceId,
//   time
// ) => {
//   try {
//     console.log(time);
//     // // for production
//     // const subscription = await stripe.subscriptions.create({
//     // 	customer: stripeCustomerId,
//     // 	default_payment_method: cardId,
//     // 	items: [{ price: stripePriceId}],
//     // 	trial_period_days: 180,
//     // });

//     // for dev test
//     let trial_period_days = 0;
//     if (time == "monthly") {
//       trial_period_days = 30;
//     } else if (time == "yearly") {
//       trial_period_days = 365;
//     }

//     // const trialEndDate = moment().add(3, 'minutes');

//     const subscription = await stripe.subscriptions.create({
//       customer: stripeCustomerId,
//       default_payment_method: cardId,
//       items: [{ price: stripePriceId }],
//       trial_period_days: trial_period_days,
//       // trial_end: trialEndDate.unix(), // Convert trial end time to Unix timestamp
//     });

//     return {
//       status: true,
//       subscription: subscription,
//     };
//   } catch (err) {
//     console.log(err);
//     return {
//       status: false,
//       message: err.message,
//     };
//   }
// };

// exports.createSubscriptionCheckout = async (user, lineItems, mode, host) => {
//   let customerId = user.stripeCustomerId;
//   if (!customerId) {
//     customerId = await customerService.createCustomer(user._id);
//   }
//   console.log(host);
//   try {
//     const data = await stripe.checkout.sessions.create({
//       customer: customerId,
//       payment_method_types: ["card"],
//       line_items: lineItems,
//       mode: mode,
//       success_url: `${host}/PaymentSuccess`,
//       cancel_url: `${host}/PaymentCancelled`,
//     });
//     console.log("data:", data);
//     return {
//       status: true,
//       data: data,
//     };
//   } catch (error) {
//     console.log("error:", error);
//     return { status: false, message: "Failed to create payment" };
//   }
// };

// // get upcoming invoice
// exports.getStripeUpcomingInvoice = async (customer) => {
//   try {
//     const invoice = await stripe.invoices.retrieveUpcoming({
//       customer: customer,
//     });
//     return {
//       status: true,
//       invoice: invoice,
//     };
//   } catch (err) {
//     return {
//       status: false,
//       message: err.message,
//     };
//   }
// };

// cancel subscription

exports.cancelSubscription = async (subcriptionId) => {
  try {
    const deleted = await stripe.subscriptions.cancel(subcriptionId);
    return {
      status: true,
      data: deleted,
    };
  } catch (err) {
    return {
      status: false,
      message: err.message,
    };
  }
};

// make card default

exports.makeCardDefault = async (customerId, paymentMethodId) => {
  try {
    const updatedCustomer = await stripe.customers.update(customerId, {
      invoice_settings: {
        default_payment_method: paymentMethodId,
      },
    });

    // console.log("✅ Default payment method set successfully.");
    return true;
  } catch (err) {
    console.error("❌ Error setting default payment method:", err.message);
    return false;
  }
};

// exports.makeCardDefault = async (customerId, newDefaultCardId) => {
//   try {
//     // Retrieve the customer from Stripe
//     // const customer = await stripe.customers.retrieve(customerId);
//     // customer.default_source = newDefaultCardId;
//     const updatedCustomer = {
//       default_source: newDefaultCardId,
//     };
//     await stripe.customers.update(customerId, updatedCustomer);

//     console.log("Default card updated successfully.");
//     return true;
//   } catch (err) {
//     console.error("Error setting default card:", err.message);
//     return false;
//   }
// };

exports.getCards = async (stripeCustomerId, limit = 10) => {
  const [cardsList, customer] = await Promise.all([
    stripe.customers.listSources(stripeCustomerId, {
      object: "card",
      limit: limit,
    }),
    stripe.customers.retrieve(stripeCustomerId),
  ]);

  const defaultCardId =
    customer.invoice_settings?.default_payment_method ||
    customer.default_source;

  return {
    cards: cardsList.data,
    defaultCardId,
  };
};

exports.getCardId = async (stripeCustomerId, limit) => {
  const cards = await stripe.customers.listSources(stripeCustomerId, {
    object: "card",
    limit: limit,
  });

  return cards.data[0].id;
};

// check a payment card is already added or not
exports.checkCardAlreadyAdded = async (customerId, cardToCheck) => {
  try {
    const paymentMethods = await stripe.paymentMethods.list({
      customer: customerId,
      type: "card",
    });

    const isCardAlreadyAdded = paymentMethods.data.find((paymentMethod) => {
      if (
        paymentMethod.card.last4 === cardToCheck.card_number.slice(-4) &&
        paymentMethod.card.exp_month.toString() ===
          cardToCheck.card_date.split("-")[0] &&
        paymentMethod.card.exp_year.toString().slice(-2) ===
          cardToCheck.card_date.split("-")[1]
      ) {
        return paymentMethod;
      }
    });
    return {
      status: true,
      data: isCardAlreadyAdded,
    };
  } catch (err) {
    return {
      status: false,
      message: err.message,
    };
  }
};

exports.deleteCardStripe = async (user, stripecardId) => {
  const customerSource = await stripe.customers.deleteSource(
    user,
    stripecardId
  );
  return customerSource;
};

exports.changeCard = async (subscriptionId, cardId) => {
  const subscription = await stripe.subscriptions.update(subscriptionId, {
    default_source: cardId,
  });
  return subscription;
};

exports.subcriptionDetail = async (subscriptionId, includeInvoice = false) => {
  try {
    const options = includeInvoice ? { expand: ["latest_invoice"] } : {};
    const subscription = await stripe.subscriptions.retrieve(
      subscriptionId,
      options
    );

    return {
      status: true,
      data: subscription,
    };
  } catch (error) {
    console.error(`❌ Error retrieving subscription details: ${error.message}`);
    return {
      status: false,
      error: error.message,
    };
  }
};

exports.transferMoney = async ({
  adviserId,
  stripeAccountId,
  amount,
  appointmentId,
}) => {
  try {
    const transfer = await stripe.transfers.create({
      amount: amount * 100,
      currency: appDefaults.CURRENCY,
      destination: stripeAccountId,
      metadata: {
        adviser: String(adviserId),
        appointmentId: appointmentId,
      },
    });

    return transfer;
  } catch (error) {
    // console.error("❌ Error while creating transfer:", error);

    throw new ApiError(
      httpStatus.BAD_REQUEST,
      `Transfer failed: ${error.message}`
    );
    // throw new Error(`Transfer failed: ${error.message}`);
  }
};

exports.createCustomer = (user) =>
  stripe.customers.create({
    email: user.email,
    name: `${user.name}`,
    phone: user.phone,
  });

exports.bookSingleSession = async (
  chargeAmount, // Amount the adviser should receive after all fees
  appointmentId,
  customerId,
  cardId
) => {
  try {
    // Step 5: Create a Payment Intent with Stripe
    const paymentIntent = await stripe.paymentIntents.create({
      amount: chargeAmount * 100,
      currency: "gbp",
      customer: customerId,
      payment_method: cardId,
      confirm: true,
      capture_method: "automatic",
      metadata: { appointmentId: appointmentId.toString() },
      setup_future_usage: "off_session",
      automatic_payment_methods: {
        enabled: true,
        allow_redirects: "never",
      },
    });

    return {
      status: true,
      paymentIntentId: paymentIntent.id,
      totalChargeAmount: chargeAmount.toFixed(2), // Amount charged to user
    };
  } catch (error) {
    console.error("Stripe Error:", error.message);
    return {
      status: false,
      message: error.message,
    };
  }
};

// exports.capturePayment = async (paymentIntentId) => {
//   try {
//     // ✅ Capture the authorized payment
//     const result = await stripe.paymentIntents.capture(paymentIntentId);

//     // ✅ Check if payment was successfully captured
//     if (result.status !== "succeeded") {
//       console.error(
//         `❌ Payment capture failed for ${paymentIntentId}, status: ${result.status}`
//       );
//       return {
//         status: false,
//         message: `Payment capture failed, status: ${result.status}`,
//       };
//     }

//     return {
//       status: true,
//       message: "Payment captured successfully",
//       paymentIntentId: result.id,
//       amountCaptured: result.amount_received / 100, // Convert cents to currency
//       currency: result.currency,
//       customer: result.customer,
//       latestCharge: result.latest_charge,
//       metadata: result.metadata,
//     };
//   } catch (error) {
//     console.error(`❌ Stripe Capture Error: ${error.message}`);
//     return {
//       status: false,
//       message: error.message,
//     };
//   }
// };

exports.cancelSession = async (paymentIntentId) => {
  try {
    await stripe.paymentIntents.cancel(paymentIntentId);
    return { status: true, message: "Payment canceled, funds released" };
  } catch (error) {
    return { status: false, message: error.message };
  }
};

// exports.createCustomerSubscription = async ({
//   stripeCustomerId,
//   priceId,
//   sourceCardId,
//   metadata = {},
// }) => {
//   return stripe.subscriptions.create({
//     customer: stripeCustomerId,
//     items: [
//       {
//         price: priceId,
//         quantity: 1,
//       },
//     ],
//     default_source: sourceCardId,
//     expand: ["latest_invoice.payment_intent"],
//     metadata: metadata, // Include metadata for webhook processing
//   });
// };

exports.createCustomerSubscription = async ({
  stripeCustomerId,
  priceId,
  metadata = {},
}) => {
  return stripe.subscriptions.create({
    customer: stripeCustomerId,
    items: [
      {
        price: priceId,
        quantity: 1,
      },
    ],
    collection_method: "charge_automatically",
    expand: ["latest_invoice.payment_intent"],
    metadata,
  });
};

//adviser connect account
exports.createStripeAccount = async (adviser) => {
  const date = new Date(adviser.dob);
  const day = date.getDate();
  const month = date.getMonth() + 1;
  const year = date.getFullYear();

  // Split name into first and last name
  const [firstName, ...lastNameParts] = adviser.name.split(" ");
  const lastName = lastNameParts.join(" ") || "N/A";

  const accountData = {
    type: "express",
    capabilities: {
      card_payments: { requested: true },
      transfers: { requested: true },
    },
    business_type: "individual",
    business_profile: {
      url: `https://moneyowls.com/advisers/${adviser._id}`,
      support_email: adviser.email,
      product_description: "I am an adviser at Money Owls",
      support_phone: adviser.phone,
      mcc: 6300,
    },
    country: "GB",
    email: adviser.email,
    individual: {
      first_name: firstName,
      last_name: lastName,
      dob: { day, month, year },
      email: adviser.email,
      phone: adviser.phone,
      address: {
        city: adviser.address?.city,
        line1: adviser.address?.line1,
        postal_code: adviser.address?.postalCode,
      },
    },
  };
  // Create Stripe account with the data
  return await stripe.accounts.create(accountData);
};

exports.createStripeAccountLink = async (connectedAccountId) => {
  return stripe.accountLinks.create({
    account: connectedAccountId,
    refresh_url: "https://moneyowls.co.uk/stripe-refresh",
    return_url: "https://moneyowls.co.uk/stripe-dashboard",
    type: "account_onboarding",
  });
};

exports.createSubscriptionProduct = async (
  name,
  amount,
  billingCycle,
  description
) => {
  try {
    const product = await stripe.products.create({
      name,
      description,
      images: [],
      default_price_data: {
        currency: appDefaults.CURRENCY,
        unit_amount: amount * 100,
        recurring: {
          interval: billingCycle,
        },
      },
    });

    return product;
  } catch (error) {
    throw new ApiError(
      httpStatus.INTERNAL_SERVER_ERROR,
      `failed to create stripe product : ${error.raw.message}`
    );
  }
};

exports.createStripePrice = async (price, billingCycle, stripeProductId) => {
  try {
    const stripePrice = await stripe.prices.create({
      unit_amount: price * 100, // Convert to smallest unit
      currency: appDefaults.CURRENCY,
      recurring: { interval: billingCycle },
      product: stripeProductId,
    });

    return stripePrice;
  } catch (error) {
    throw new ApiError(
      httpStatus.INTERNAL_SERVER_ERROR,
      `failed to create stripe price : ${error.raw.message}`
    );
  }
};

exports.updateSubscriptionProduct = async (
  stripeProductId,
  { isActive, name, description }
) => {
  try {
    const updatedProduct = await stripe.products.update(stripeProductId, {
      active: isActive,
      name,
      description,
    });

    return {
      status: true,
      updatedProduct,
    };
  } catch (error) {
    return {
      status: false,
      message: error.message,
    };
  }
};

exports.archiveStripePrice = async (stripePriceId) => {
  try {
    const archivedPrice = await stripe.prices.update(stripePriceId, {
      active: false, // This prevents new subscriptions on this price
    });

    return { status: true, archivedPrice };
  } catch (error) {
    return { status: false, message: error.message };
  }
};

// Export the constructEvent function
exports.constructEvent = constructEvent;

exports.deleteConnectedAccount = async (stripeAccountId) => {
  return await stripe.accounts.del(stripeAccountId);
};

exports.checkOnboardStatus = async (stripeAccountId) => {
  return await stripe.accounts.retrieve(stripeAccountId);
};

exports.createCheckoutSession = async ({
  customerId,
  amount,
  appointmentId,
  metadata = {},
}) => {
  try {
    const session = await stripe.checkout.sessions.create({
      customer: customerId,
      payment_method_types: ["card"],
      line_items: [
        {
          price_data: {
            currency: appDefaults.CURRENCY,
            product_data: {
              name: "Appointment Payment",
              description: `Payment for appointment #${appointmentId}`,
            },
            unit_amount: Math.round(amount * 100), // Convert to cents
          },
          quantity: 1,
        },
      ],
      mode: "payment", // One-time payment
      success_url:
        "https://web-moneyowl-admin-panel.vercel.app/stripe-dashboard",
      cancel_url: "https://web-moneyowl-admin-panel.vercel.app/stripe-refresh",
      metadata: {
        ...metadata,
        appointmentId: appointmentId.toString(),
      },
      payment_intent_data: {
        metadata: {
          appointmentId: appointmentId.toString(),
        },
      },
    });

    // console.log(`✅ Checkout session created for appointment ${appointmentId}`);
    return session;
  } catch (error) {
    console.error(`❌ Error creating checkout session: ${error.message}`);
    throw new ApiError(
      httpStatus.INTERNAL_SERVER_ERROR,
      `Failed to create checkout session: ${error.message}`
    );
  }
};

// const file = await stripe.files.create({
//   purpose: 'identity_document',
//   file: {
//     data: fs.createReadStream('/path/to/id_front.jpg'),
//     name: 'id_front.jpg',
//     type: 'image/jpeg',
//   },
// });const accountUpdate = await stripe.accounts.update('acct_1R1L6uQwbcGvYyCS', {
//   individual: {
//     verification: {
//       document: {
//         front: file.id, // The file ID returned from the file upload
//       },
//     },
//   },
// });

exports.balanceTransaction = async (balance_transaction) => {
  return await stripe.balanceTransactions.retrieve(balance_transaction);
};

exports.retrieveCharge = async (chargeId) => {
  return await stripe.charges.retrieve(chargeId);
};

exports.retrievePaymentIntent = async (paymentIntentId) => {
  return await stripe.paymentIntents.retrieve(paymentIntentId);
};

exports.retrieveTransfer = async (transferId) => {
  try {
    const transfer = await stripe.transfers.retrieve(transferId);
    return {
      status: true,
      data: transfer,
    };
  } catch (error) {
    console.error(`❌ Error retrieving transfer: ${error.message}`);
    return {
      status: false,
      error: error.message,
    };
  }
};

exports.getExpressDashboardLink = async (stripeAccountId) => {
  const loginLink = await stripe.accounts.createLoginLink(stripeAccountId);
  return {
    status: true,
    url: loginLink.url, // Send this URL to the frontend
  };
};

// exports.createCheckoutSession = async ({
//   stripeCustomerId,
//   price,
//   appointmentId,
//   userId,
// }) => {
//   return await stripe.checkout.sessions.create({
//     customer: stripeCustomerId,
//     line_items: [
//       {
//         price_data: {
//           currency: "gbp",
//           unit_amount: price * 100,
//           product_data: {
//             name: "Single Session Booking",
//           },
//         },
//         quantity: 1,
//       },
//     ],
//     mode: "payment",
//     success_url: "https://web-moneyowl-admin-panel.vercel.app/stripe-dashboard",
//     cancel_url: `https://web-moneyowl-admin-panel.vercel.app/stripe-refresh`,
//     metadata: {
//       appointmentId: appointmentId.toString(),
//       userId: userId.toString(),
//     },
//   });
// };
