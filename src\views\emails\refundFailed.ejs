<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Refund Failed - Money Owls</title>
  <style>
    body {
      font-family: 'Segoe UI', Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      margin: 0;
      padding: 0;
      background-color: #f9f9f9;
    }

    .container {
      max-width: 600px;
      margin: 20px auto;
      padding: 30px;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      background-color: #ffffff;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }

    .header {
      background-color: #ffffff;
      padding: 20px;
      text-align: center;
    }

    .logo {
      max-width: 150px;
      height: auto;
    }

    .title {
      color: #e74c3c;
      text-align: center;
      margin-bottom: 25px;
      font-size: 26px;
      font-weight: 600;
    }

    .refund-details {
      background-color: #f8f9fa;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 25px;
    }

    .refund-details p {
      margin: 8px 0;
    }



    .footer {
      margin-top: 40px;
      padding-top: 20px;
      border-top: 1px solid #f0f0f0;
      text-align: center;
      font-size: 14px;
      color: #666;
    }

    a {
      color: #00AA9D;
      text-decoration: none;
    }

    a:hover {
      text-decoration: underline;
    }

    @media only screen and (max-width: 600px) {
      .container {
        padding: 20px;
        margin: 0px;
      }

      .title {
        font-size: 22px;
      }

      .header,
      .footer {
        padding: 10px;
      }
    }
  </style>
</head>

<body>
  <div class="container">
    <div class="header">
      <img src="https://moneyowl.s3.eu-north-1.amazonaws.com/public/logos/final+logo+moneyowsl.png"
        alt="Money Owls Logo" class="logo">
    </div>

    <h2 class="title">Refund Failed</h2>


    <p>Hello <%= name %>,</p>

    <p>We regret to inform you that we encountered an issue while processing your refund for appointment <%= bookingId
        %>.</p>

    <div class="refund-details">
      <p><strong>Amount:</strong> £<%= amount %>
      </p>
      <p><strong>Booking ID:</strong>
        <%= bookingId %>
      </p>
      <p><strong>Status:</strong> Failed</p>
      <p><strong>Date:</strong>
        <%= new Date().toLocaleDateString() %>
      </p>
      <% if (typeof reason !=='undefined' && reason) { %>
        <p><strong>Reason:</strong>
          <%= reason %>
        </p>
        <% } %>
    </div>

    <p>Our team has been notified of this issue and is working to resolve it. We will attempt to process this refund
      again or contact you with further information.</p>

    <p>If you have any questions or concerns about this refund, please contact our support team at <a
        href="mailto:<EMAIL>"><EMAIL></a>.</p>

    <p>We apologize for any inconvenience this may cause.</p>

    <p>Best regards,<br>The Money Owls Team</p>

    <div class="footer">
      <p>© <%= new Date().getFullYear() %> Money Owls. All rights reserved.</p>
      <p>
        <a href="https://moneyowls.co.uk/privacy-policy">Privacy Policy</a> |
        <a href="https://moneyowls.co.uk/terms-of-service">Terms of Service</a>
      </p>
    </div>
  </div>
</body>

</html>