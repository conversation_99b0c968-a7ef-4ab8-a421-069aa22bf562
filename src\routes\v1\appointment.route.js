const express = require("express");
const { firebaseAuth } = require("../../middlewares/firebaseAuth");
const router = express.Router();
const {
  appointmentValidaton,
  adviserValidation,
} = require("../../validations");
const { appointmentController } = require("../../controllers");
const validate = require("../../middlewares/validate");
const { fileUploadService } = require("../../microservices");

router.post(
  "/create/:adviserId",
  firebaseAuth("User"),
  validate(appointmentValidaton.createAppointment),
  appointmentController.createAppointment
);

router.post(
  "/cancel/user/:appointmentId",
  firebaseAuth("User"),
  validate(appointmentValidaton.cancelAppointmentUser),
  appointmentController.cancelAppointmentUser
);

router.patch(
  "/reschedule/:appointmentId",
  firebaseAuth("User"),
  // validate(appointmentValidaton.rescheduleAppointment),
  appointmentController.rescheduleAppointment
);

//filter -status
router.get(
  "/all/:id?",
  firebase<PERSON>uth("All"),
  validate(appointmentValidaton.getAppointments),
  appointmentController.getAllAppointments
);

router.get(
  "/details/:id",
  firebaseAuth("All"),
  validate(appointmentValidaton.getAppointmentDetails),
  appointmentController.getAppointmentDetails
);

router.patch(
  "/accept/:id",
  firebaseAuth("Adviser"),
  validate(appointmentValidaton.acceptAppointment),
  appointmentController.acceptAppointment
); // to update status - accept

router.patch(
  "/cancel/adviser/:appointmentId",
  firebaseAuth("Adviser"),
  validate(adviserValidation.addCancelledReason),
  appointmentController.cancelAppointmentAdviser
); // cancels and adds reason

router.get(
  "/today",
  firebaseAuth("Admin"),
  appointmentController.getTodaysAppointments
);

router.patch(
  "/complete/:id",
  firebaseAuth("Adviser,User"),
  validate(appointmentValidaton.markAppointmentComplete),
  appointmentController.markAppointmentComplete
);

router.get(
  "/user/:id?",
  firebaseAuth("Admin"),
  validate(appointmentValidaton.getAppointments),
  appointmentController.getAllAppointmentUser
);

router.get(
  "/adviser/:id?",
  firebaseAuth("Admin"),
  validate(appointmentValidaton.getAppointments),
  appointmentController.getAllAppointmentAdviser
);

router.post(
  "/note/add/:id",
  firebaseAuth("Adviser"),
  validate(appointmentValidaton.addNotes),
  appointmentController.addNotes
);

router.patch(
  "/reminder/toggle/:id",
  firebaseAuth("User"),
  validate(appointmentValidaton.toggleAppointmentReminder),
  appointmentController.toggleAppointmentReminder
);

router.post(
  "/whiteboard/:appointmentId",
  firebaseAuth("Adviser"),
  fileUploadService.multerUpload.single("screenshot"),
  validate(appointmentValidaton.addWhiteboardScreenshot),
  appointmentController.addWhiteboardScreenshot
);

router.get(
  "/payout-status/:appointmentId",
  firebaseAuth("All"),
  validate(appointmentValidaton.getAppointmentPayoutStatus),
  appointmentController.getAppointmentPayoutStatus
);

module.exports = router;
