const Joi = require("joi");

const { dbOptionsSchema, objectId } = require("./custom.validation");

const getOptions = {
  query: Joi.object().keys({
    ...dbOptionsSchema,
    rating: Joi.number().min(0).max(5).optional(),
  }),
};

const addCancelledReason = {
  params: Joi.object({
    appointmentId: Joi.string().custom(objectId).required(),
  }),
  body: Joi.object({
    cancellationReason: Joi.string().min(3).max(500).required(), // Customize min/max length as needed
  }),
};

const getAdviserOptions = {
  query: Joi.object().keys({
    specialization: Joi.alternatives()
      .try(
        Joi.array().items(Joi.string().trim()), // Array of trimmed strings
        Joi.string().trim() // Single trimmed string
      )
      .optional(),
    experience: Joi.number().integer().min(0).optional(), // Minimum experience of 0 years
    averageRating: Joi.number().min(0).max(5).optional(), // Rating between 0 and 5
    dateAvailable: Joi.date().optional(), // ISO formatted date
    name: Joi.string().optional(), // Case-insensitive name search
    page: Joi.number().integer().min(1).optional(), // Pagination page
    limit: Joi.number().integer().min(1).optional(), // Pagination limit
    sortBy: Joi.string()
      .valid("name", "specialization", "experience", "averageRating")
      .optional(), // Sortable fields
    sortOrder: Joi.string().valid("asc", "desc").optional(), // Sort order
    status: Joi.string().valid("Inactive", "Active").optional(), // Sort order
    isVerified: Joi.boolean().optional(),
    isBlockedByAdmin: Joi.boolean().optional(),

    keyword: Joi.string().optional(),
  }),
};
module.exports = {
  getOptions,
  addCancelledReason,
  getAdviserOptions,
};
