const httpStatus = require("http-status");
const { User, FinancialHealth, Users } = require("../models");
const ApiError = require("../utils/ApiError");
const { userTypes } = require("../constants");

async function createUser(userData) {
  const user = new User(userData);
  return await user.save();
}

async function pushGoal(userId, goalId) {
  const user = await User.findByIdAndUpdate(
    userId,
    {
      $push: { goals: goalId },
      $set: { isGoaladded: true },
    },
    { new: true } // Return the updated document
  );
  return user;
}

async function getUserById(id) {
  return await User.findById(id);
}

async function getUserDetails(query, populateFields = []) {
  let userQuery = User.findOne(query).lean(); // Use lean() for better performance

  // Dynamically populate fields if provided
  populateFields.forEach((field) => {
    userQuery = userQuery.populate(field);
  });

  const user = await userQuery.exec();

  if (!user) {
    throw new ApiError(httpStatus.NOT_FOUND, "user not found");
  }

  return user;
}

async function getUsers(filters, options) {
  let query = {}; // Default query to find all non-deleted users // Default query to find all non-deleted users

  if (filters.status === "Active") {
    query.isDeleted = false;
  }
  if (filters.status === "Inactive") {
    query.isDeleted = true;
  }
  if (filters.name) {
    query.name = { $regex: filters.name, $options: "i" }; // Case-insensitive search
  }

  // Pass the constructed query to paginate
  return await User.paginate(query, options);
}

async function updateUser(id, updateData) {
  // Ensure name is lowercase if it's being updated

  if (updateData.name) {
    updateData.name = updateData.name.toLowerCase();
  }

  // Remove any undefined values to prevent accidental field deletion
  // Object.keys(updateData).forEach((key) => {
  //   if (
  //     updateData[key] === undefined ||
  //     updateData[key] === null ||
  //     updateData[key] === ""
  //   ) {
  //     delete updateData[key];
  //   }
  // });

  return await User.findByIdAndUpdate(id, updateData, { new: true });
}

async function deleteUser(id) {
  return await User.findByIdAndUpdate(id, { isDeleted: true }, { new: true });
}

async function activeUser(filter, data) {
  return await Users.findOneAndUpdate(filter, data, { new: true });
}

async function blockUser(filter, data) {
  return await User.findOneAndUpdate(filter, data, { new: true });
}

async function unBlockUser(filter, data) {
  return await Users.findOneAndUpdate(filter, data, { new: true });
}

async function createFinancialHealth(healthData) {
  const financialHealth = new FinancialHealth(healthData);
  return await financialHealth.save();
}

async function updateFinancialHealth(userId, healthData) {
  return await FinancialHealth.findOneAndUpdate(
    { userId }, // Find the document by userId
    { $set: healthData }, // Update fields within the healthData object
    {
      new: true, // Return the updated document
      runValidators: true, // Validate data against the schema
    }
  );
}

async function addFinancialHealthToUser(userId, healthDataId) {
  const user = await User.findByIdAndUpdate(
    userId,
    { financialHealth: healthDataId, $set: { isFinancialHealthadded: true } },
    { new: true }
  );
  return user;
}

async function getFinancialData(userId) {
  return FinancialHealth.findOne({ userId: userId });
}

async function toggleFavorite(userId, adviserId) {
  const user = await User.findById(userId);

  if (!user) {
    return null;
  }

  if (user.favorites.includes(adviserId)) {
    // Remove adviser if already in favorites
    user.favorites = user.favorites.filter((id) => id.toString() !== adviserId);
  } else {
    // Add adviser to favorites
    user.favorites.push(adviserId);
  }

  await user.save();
  return user;
}

async function getFavorites(userId) {
  const user = await User.findOne({ _id: userId, isDeleted: false })
    .select("favorites") // Select only the `favorites` field
    .populate({
      path: "favorites",
      select:
        "name specialization experience _id profilePic bio gender averageRating",
      match: { isDeleted: false }, // Exclude deleted advisers
    });
  return user ? user.favorites : [];
}

// async function removeFromFavorites(userId, itemId) {
//   const user = await User.findByIdAndUpdate(
//     userId,
//     { $pull: { favorites: itemId } },
//     { new: true }
//   );

//   return user;
// }

async function countUsers() {
  return await User.countDocuments({});
}

async function getAllWithoutAdmin() {
  const users = await Users.find({ __t: { $ne: "Admin" }, isDeleted: false });
  return users;
}

async function getAllUsers() {
  const users = await Users.find({ __t: { $eq: "User" }, isDeleted: false });
  return users;
}

async function countActiveUsers() {
  return await User.countDocuments({ isDeleted: false });
}

const reduceFreeSessions = async (userId) => {
  const user = await User.findByIdAndUpdate(
    userId,
    { $inc: { freeSessions: -1 } },
    { new: true } // return the updated document
  );

  if (!user) throw new ApiError(httpStatus.NOT_FOUND, "User not found");

  return user.freeSessions;
};

const increaseFreeSessions = async (userId) => {
  const user = await User.findByIdAndUpdate(
    userId,
    { $inc: { freeSessions: 1 } },
    { new: true } // return the updated document
  );

  if (!user) throw new ApiError(httpStatus.NOT_FOUND, "User not found");

  return user.freeSessions;
};

const getUsersWithDetails = async (filters, options) => {
  const matchStage = {};
  // Apply filters
  if (filters.status === "Active") {
    matchStage.isDeleted = false;
  }
  if (filters.status === "Inactive") {
    matchStage.isDeleted = true;
  }

  if (filters.keyword) {
    matchStage.$or = [
      { name: { $regex: filters.keyword, $options: "i" } },
      { email: { $regex: filters.keyword, $options: "i" } },
      { phone: { $regex: filters.keyword, $options: "i" } },
    ];
    matchStage.__t = userTypes.USER;
  }

  if (filters.isBlockedByAdmin) {
    matchStage.isBlockedByAdmin = true;
  }

  const limit = parseInt(options.limit) || 10;
  const skip = parseInt(options.page > 0 ? (options.page - 1) * limit : 0);
  const sortBy = options.sortBy || "createdAt"; // Default to sorting by name
  const sortOrder = options.sortOrder === "asc" ? 1 : -1;

  const aggregationPipeline = [
    { $match: matchStage },

    // Project only necessary fields from Users collection
    {
      $project: {
        name: 1, // Include user name
        email: 1, // Include user email
        phone: 1, // Include user phone
        role: 1, // Include user role
        isDeleted: 1, // Include user deletion status
        isBlockedByAdmin: 1,
        profilePic: 1, // Include user profile picture
        address: 1,
        createdAt: 1,
      },
    },

    // Lookup latest subscription with limited fields
    {
      $lookup: {
        from: "customersubscriptions",
        let: { userId: "$_id" },
        pipeline: [
          { $match: { $expr: { $eq: ["$user", "$$userId"] } } },
          { $sort: { createdAt: -1 } },
          { $limit: 1 },
          {
            $project: {
              _id: 0,
              isActive: 1, // Include only specific fields in the subscription
            },
          },
        ],
        as: "subscription",
      },
    },

    { $unwind: { path: "$subscription", preserveNullAndEmptyArrays: true } },

    // Lookup next appointment
    {
      $lookup: {
        from: "appointments",
        let: { userId: "$_id" },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ["$user", "$$userId"] }, // matching user id
                  { $eq: ["$status", "scheduled"] }, // filtering for scheduled appointments
                  { $eq: ["$paymentStatus", "paid"] }, // filtering for paid appointments
                  { $gte: ["$date", new Date()] }, // filtering for future appointments
                ],
              },
            },
          },
          { $sort: { date: 1 } },
          { $limit: 1 },
        ],
        as: "nextAppointment",
      },
    },

    {
      $addFields: {
        nextBookingDate: {
          $ifNull: [{ $arrayElemAt: ["$nextAppointment.date", 0] }, null],
        },
      },
    },

    { $project: { nextAppointment: 0 } },

    // Sorting by user-specified field and order
    { $sort: { [sortBy]: sortOrder } },

    // Pagination using facet
    {
      $facet: {
        results: [{ $skip: skip }, { $limit: limit }],
        totalCount: [{ $count: "count" }],
      },
    },
  ];

  const result = await User.aggregate(aggregationPipeline);

  const results = result[0].results;
  const totalResults = result[0].totalCount[0]?.count || 0;
  const totalPages = Math.ceil(totalResults / limit); // Calculate total pages

  return {
    results,
    page: parseInt(options.page) || 1,
    limit,
    totalResults,
    totalPages, // Include totalPages in the response
  };
};

// Perform aggregation with lookup to fetch corresponding subscriptions
async function searchUser(
  query,
  page = 1,
  limit = 10,
  sortBy = "createdAt",
  sortOrder = 1
) {
  // Match query for users
  const matchQuery = [
    { $match: query }, // Match the user query
  ];

  // Count total matching users
  const totalUsersCount = await Users.countDocuments(query);

  const usersWithSubscriptionsAndNextBooking = await Users.aggregate([
    ...matchQuery,

    // Lookup for the next subscription
    {
      $lookup: {
        from: "customersubscriptions", // The name of the subscription collection
        let: { userId: "$_id" }, // passing user _id to the lookup pipeline
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ["$user", "$$userId"] }, // matching user id
                ],
              },
            },
          },
          { $sort: { createdAt: -1 } }, // Sorting by createdAt in descending order (latest first)
          { $limit: 1 }, // Limiting to the most recent subscription (including renewals)
        ],
        as: "subscription", // The field that will contain the latest subscription
      },
    },

    // Lookup for the next booking (appointment)
    {
      $lookup: {
        from: "appointments", // The name of the appointments collection
        let: { userId: "$_id" }, // passing user _id to the lookup pipeline
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ["$user", "$$userId"] }, // matching user id
                  { $eq: ["$status", "scheduled"] }, // filtering for scheduled appointments
                  { $eq: ["$paymentStatus", "paid"] }, // filtering for paid appointments
                  { $gte: ["$date", new Date()] }, // filtering for future appointments
                ],
              },
            },
          },

          { $sort: { date: 1 } }, // sorting appointments by date ascending (earliest first)
          { $limit: 1 }, // limiting to 1 appointment (the next one)
        ],
        as: "nextAppointment", // output field that will contain the next appointment
      },
    },

    // Add fields for both next subscription and next appointment dates
    {
      $addFields: {
        nextBookingDate: {
          $ifNull: [{ $arrayElemAt: ["$nextAppointment.date", 0] }, null],
        },
        subscription: {
          $ifNull: [
            {
              $arrayElemAt: [
                [
                  {
                    isActive: { $arrayElemAt: ["$subscription.isActive", 0] },
                  },
                ],
                0,
              ],
            },
            null,
          ],
        },
      },
    },

    // Projecting out the unnecessary fields from the result
    {
      $project: {
        name: 1, // Include user name
        email: 1, // Include user email
        phone: 1, // Include user phone number
        __t: 1, // Include user role
        profilePic: 1, // Include user profile picture
        isDeleted: 1,
        isBlockedByAdmin: 1,

        address: 1, // Include user deletion status
        documents: 1,
        isVerified: 1,
        averageRating: 1,
        nextBookingDate: 1,
        subscription: 1,
      },
    },

    // Sorting by user-specified field and order
    { $sort: { [sortBy]: sortOrder } },

    // Pagination (skip and limit)
    { $skip: (page - 1) * limit },
    { $limit: limit },
  ]);

  // Calculate the total number of pages
  const totalPages = Math.ceil(totalUsersCount / limit);

  // Return paginated data with additional metadata
  return {
    results: usersWithSubscriptionsAndNextBooking,
    page: page,
    limit: limit,
    totalResults: totalUsersCount,
    totalPages,
  };
}

async function getANYUsersDetails(query, populateFields = []) {
  let userQuery = Users.findOne(query).lean(); // Use lean() for better performance

  // Dynamically populate fields if provided
  populateFields.forEach((field) => {
    userQuery = userQuery.populate(field);
  });

  const user = await userQuery.exec();

  if (!user) {
    throw new ApiError(httpStatus.NOT_FOUND, "user not found");
  }

  return user;
}

module.exports = {
  createUser,
  pushGoal,
  getUserById,
  getUsers,
  updateUser,
  deleteUser,
  createFinancialHealth,
  addFinancialHealthToUser,
  getFinancialData,
  // addToFavorites,
  // removeFromFavorites,
  countUsers,
  getAllWithoutAdmin,
  updateFinancialHealth,
  toggleFavorite,
  getFavorites,
  getAllUsers,
  countActiveUsers,
  activeUser,
  getUserDetails,
  searchUser,
  reduceFreeSessions,
  increaseFreeSessions,
  getUsersWithDetails,
  unBlockUser,
  getANYUsersDetails,
  blockUser,
};
