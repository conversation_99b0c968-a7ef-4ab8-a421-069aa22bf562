const express = require("express");
const router = express.Router();
const { firebaseAuth } = require("../../middlewares/firebaseAuth");
const { feedBackController } = require("../../controllers");
const { reviewValidation } = require("../../validations");
const validate = require("../../middlewares/validate");

router.post(
  "/add-review",
  firebaseAuth("Adviser,User"),
  validate(reviewValidation.appReview),
  feedBackController.reviewApp
);

router.get(
  "/get-reviews",
  firebaseAuth("Admin"),
  validate(reviewValidation.getAppFeedbacks),
  feedBackController.getReviews
);

module.exports = router;
