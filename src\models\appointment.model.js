const mongoose = require("mongoose");
const { paginate } = require("./plugins/paginate");

const appointmentSchema = new mongoose.Schema(
  {
    user: {
      type: mongoose.SchemaTypes.ObjectId,
      ref: "User",
      required: true,
    },
    adviser: {
      type: mongoose.SchemaTypes.ObjectId,
      ref: "Adviser",
      required: true,
    },
    date: {
      type: Date,
      required: true,
    },
    timeSlot: {
      start: {
        type: Date,
        required: true,
      },
      end: {
        type: Date,
        required: true,
      },
    },
    duration: {
      type: Number,
      required: true,
    },
    status: {
      type: String,
      enum: ["scheduled", "completed", "cancelled"],
      default: "scheduled",
    },
    adviserStatus: {
      type: String,
      enum: ["accepted", "rejected", "pending"],
      default: "pending",
    },
    paymentStatus: {
      type: String,
      enum: ["paid", "failed", "refunded", "processing", "free"], // should be paid if transaction is successful
      default: null,
    },
    paymentIntent: { type: String },
    mode: {
      type: String,
      enum: ["subscription", "payment", "free"],
    },
    adviserCancellationReason: { type: String, trim: true },
    systemCancellationReason: { type: String, trim: true },

    note: {
      type: String,
      trim: true,
      default: "",
    },
    bookingId: {
      type: String,
      trim: true,
      required: true,
      unique: true, // Ensures uniqueness
    },
    parentAppointment: {
      type: mongoose.SchemaTypes.ObjectId,
      ref: "Appointment",
      default: null,
    },
    recordingUrl: {
      type: String,
      default: null,
    },
    amount: {
      type: Number,
      default: null,
    },
    commissionPercentage: {
      type: Number,
      default: null,
    },
    whiteBoardImage: {
      key: {
        type: String,
        default: null,
      },
      url: {
        type: String,
        default: null,
      },
    },
    isReminderSet: {
      type: Boolean,
      default: true,
    },
    hasUserJoined: {
      type: Boolean,
      default: false,
    },
    hasAdviserJoined: {
      type: Boolean,
      default: false,
    },
    userJoinTime: {
      type: Date,
      default: null,
    },
    adviserJoinTime: {
      type: Date,
      default: null,
    },
  },
  { timestamps: true }
);

// Add pagination plugin
appointmentSchema.plugin(paginate);
appointmentSchema.index({ user: 1, adviser: 1, paymentIntent: 1 });
const Appointment = mongoose.model("Appointment", appointmentSchema);

module.exports = {
  Appointment,
};
