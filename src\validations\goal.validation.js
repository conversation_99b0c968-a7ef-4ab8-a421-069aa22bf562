const Joi = require("joi");

const { dbOptionsSchema, objectId } = require("./custom.validation");

const createGoal = {
  body: Joi.object().keys({
    name: Joi.string().trim().required(),
    targetAmount: Joi.number().required(),
    targetDate: Joi.date().required(),
    monthlyContribution: Joi.number().required(),
  }),
};

const updateGoal = {
  params: Joi.object().keys({
    goalId: Joi.string().custom(objectId).required(),
  }),
  body: Joi.object()
    .keys({
      name: Joi.string().trim().optional(),
      amount: Joi.number().optional(),
      targetAmount: Joi.number().optional(),
      targetDate: Joi.date().optional(),
      monthlyContribution: Joi.number().optional(),
      isCompleted: Joi.boolean().optional(),
    })
    .or(
      "name",
      "amount",
      "targetAmount",
      "targetDate",
      "monthlyContribution",
      "isCompleted"
    )
    .required(),
};

const getGoals = {
  query: Joi.object().keys({
    ...dbOptionsSchema,
  }),
};

const goalParams = {
  params: Joi.object().keys({
    goalId: Joi.string().custom(objectId).required(),
  }),
};

module.exports = {
  createGoal,
  updateGoal,
  getGoals,
  goalParams,
};
