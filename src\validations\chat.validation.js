const Joi = require("joi");
const { objectId } = require("./custom.validation");

/**
 * Validation schema for creating a chat room
 */
const createChatRoomValidation = {
  body: Joi.object().keys({
    participants: Joi.array()
      .items(Joi.string().custom(objectId).required())
      .min(2)
      .max(2)
      .required()
      .messages({
        "array.min": "Exactly 2 participants are required",
        "array.base": "Participants must be an array",
        "any.required": "Participants are required",
      }),
  }),
};

/**
 * Validation schema for sending a message
 */
const sendMessageValidation = {
  body: Joi.object().keys({
    content: Joi.string().allow("").optional(),
    replyTo: Joi.string().custom(objectId).allow(null),
    chatRoomId: Joi.string().custom(objectId).required().messages({
      "string.empty": "Chat room ID is required",
      "any.required": "Chat room ID is required",
    }),
  }),
};

/**
 * Validation schema for getting or creating a one-to-one chat room
 */
const getOrCreateChatRoomValidation = {
  body: Joi.object().keys({
    otherUserId: Joi.string().custom(objectId).required().messages({
      "string.empty": "Other user ID is required",
      "any.required": "Other user ID is required",
    }),
  }),
};

module.exports = {
  createChatRoomValidation,
  sendMessageValidation,
  getOrCreateChatRoomValidation,
};
