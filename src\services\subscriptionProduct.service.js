const { SubscriptionProduct } = require("../models");

async function createSubscriptionProduct(subscriptionproductData, userId) {
  const subscriptionproduct = new SubscriptionProduct({
    ...subscriptionproductData,
    userId,
  });
  const savedSubscriptionProduct = await subscriptionproduct.save();
  return savedSubscriptionProduct;
}

async function updateSubscriptionProduct(
  subscriptionproductId,
  subscriptionproductData
) {
  const updatedSubscriptionProduct =
    await SubscriptionProduct.findByIdAndUpdate(
      subscriptionproductId,
      subscriptionproductData,
      {
        new: true,
        runValidators: true,
      }
    );
  return updatedSubscriptionProduct;
}

async function deleteSubscriptionProduct(subscriptionproductId) {
  const subscriptionproduct = await SubscriptionProduct.findByIdAndDelete(
    subscriptionproductId
  );
  return true;
}

async function getAllSubscriptionProducts() {
  const subscriptionproducts = await SubscriptionProduct.find({});
  return subscriptionproducts;
}

async function getSubscriptionProductById(subscriptionproductId) {
  const subscriptionproduct = await SubscriptionProduct.findById(
    subscriptionproductId
  );
  if (!subscriptionproduct || !subscriptionproduct.stripePriceId) {
    throw new Error("Invalid product ID provided.");
  }
  return subscriptionproduct;
}

async function increaseSubscriberCount(id) {
  const updatedSubscriptionProduct =
    await SubscriptionProduct.findByIdAndUpdate(
      id,
      { $inc: { subscriberCount: 1 } }, // ✅ Increment subscriber count by 1
      { new: true, runValidators: true }
    );

  if (!updatedSubscriptionProduct) {
    throw new Error("Subscription product not found or update failed.");
  }

  return updatedSubscriptionProduct;
}

async function decreaseSubscriberCount(subscriptionproductId) {
  const updatedSubscriptionProduct =
    await SubscriptionProduct.findByIdAndUpdate(
      subscriptionproductId,
      { $inc: { subscriberCount: -1 } }, // ✅ decrease subscriber count by 1
      { new: true, runValidators: true }
    );

  if (!updatedSubscriptionProduct) {
    throw new Error("Subscription product not found or update failed.");
  }

  return updatedSubscriptionProduct;
}

module.exports = {
  createSubscriptionProduct,
  updateSubscriptionProduct,
  deleteSubscriptionProduct,
  getAllSubscriptionProducts,
  getSubscriptionProductById,
  increaseSubscriberCount,
  decreaseSubscriberCount,
};
