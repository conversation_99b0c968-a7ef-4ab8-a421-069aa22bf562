const httpStatus = require("http-status");
const { singleSessionProductService } = require("../services/index");
const ApiError = require("../utils/ApiError");
const catchAsync = require("../utils/catchAsync");

const setSessionPricing = catchAsync(async (req, res) => {
  const product = await singleSessionProductService.createSingleSessionProduct(
    req.body
  );
  return res.status(201).json({ message: "Product created", data: product });
});

const getSingleSessionProducts = catchAsync(async (req, res) => {
  const products = await singleSessionProductService.getAllActiveSessions();
  return res.status(httpStatus.OK).json({
    message: "Single Session Products fetched",
    data: products,
  });
});

const updateSession = catchAsync(async (req, res) => {
  const { id } = req.params;

  const updateData = {
    ...req.body,
  };

  const updatedSession = await singleSessionProductService.updateSession(
    id,
    updateData
  );

  if (!updatedSession) {
    throw new ApiError(httpStatus.NOT_FOUND, "Session not found");
  }

  return res.status(httpStatus.OK).json({
    message: "Session updated successfully",
    updatedSession,
  });
});

const deleteSession = catchAsync(async (req, res) => {
  const { id } = req.params;

  const deletedSession =
    await singleSessionProductService.deleteSessionProduct(id);

  if (!deletedSession) {
    throw new ApiError(httpStatus.NOT_FOUND, "Session not found");
  }

  return res.status(httpStatus.OK).json({
    message: "Session deleted successfully",
    deletedSession,
  });
});

module.exports = {
  setSessionPricing,
  getSingleSessionProducts,
  updateSession,
  deleteSession,
};
