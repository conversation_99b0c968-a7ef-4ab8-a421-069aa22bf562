<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Payment Confirmation</title>
  <style>
    body {
      font-family: 'Segoe UI', Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      margin: 0;
      padding: 0;
      background-color: #f9f9f9;
    }

    .container {
      max-width: 600px;
      margin: 20px auto;
      padding: 30px;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      background-color: #ffffff;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }

    .header {
      background-color: #ffffff;
      padding: 20px;
      text-align: center;
    }

    .logo {
      max-width: 150px;
      height: auto;
    }

    .title {
      color: #00AA9D;
      text-align: center;
      margin-bottom: 25px;
      font-size: 26px;
      font-weight: 600;
    }

    .content {
      margin-bottom: 30px;
      color: #444;
    }

    .payment-details {
      background-color: #f0f7f7;
      padding: 20px;
      border-radius: 6px;
      margin: 25px 0;
      border-left: 4px solid #00AA9D;
    }

    .payment-details h3 {
      color: #00AA9D;
      margin-top: 0;
    }

    .payment-details p {
      margin: 10px 0;
    }

    .payment-details strong {
      color: #00AA9D;
      display: inline-block;
      width: 150px;
    }

    .amount {
      font-size: 24px;
      font-weight: bold;
      color: #00AA9D;
      text-align: center;
      margin: 20px 0;
    }

    .footer {
      text-align: center;
      margin-top: 35px;
      padding-top: 25px;
      border-top: 1px solid #e0e0e0;
      font-size: 13px;
      color: #888;
    }

    a {
      color: #00AA9D;
      text-decoration: none;
    }

    a:hover {
      text-decoration: underline;
    }

    @media only screen and (max-width: 600px) {
      .container {
        padding: 20px;
        margin: 0px;
      }

      .title {
        font-size: 22px;
      }

      .header,
      .footer {
        padding: 10px;
      }
    }
  </style>
</head>

<body>
  <div class="container">
    <div class="header">
      <img src="https://moneyowl.s3.eu-north-1.amazonaws.com/public/logos/final+logo+moneyowsl.png"
        alt="Money Owls Logo" class="logo">
    </div>

    <h1 class="title">Payment Confirmation</h1>

    <div class="content">
      <p>Hello <%= name %>,</p>

      <p>Thank you for your payment. We're confirming that your payment has been successfully processed.</p>

      <div class="amount">£<%= amount %>
      </div>

      <div class="payment-details">
        <h3>Payment Details:</h3>
        <% if (typeof bookingId !=='undefined' && bookingId) { %>
          <p><strong>Booking ID:</strong>
            <%= bookingId %>
          </p>
          <% } %>
            <% if (typeof adviserName !=='undefined' && adviserName) { %>
              <p><strong>Adviser:</strong>
                <%= adviserName %>
              </p>
              <% } %>
                <% if (typeof amount !=='undefined' && amount) { %>
                  <p><strong>Amount Paid:</strong> £<%= amount %>
                  </p>
                  <% } %>
      </div>

      <p>If you have any questions or need assistance, please don't hesitate to contact us at <a
          href="mailto:<EMAIL>"><EMAIL></a>.</p>

      <p>Best regards,<br>The Money Owls Team</p>
    </div>

    <div class="footer">
      <p>&copy; <%= new Date().getFullYear() %> Money Owls. All rights reserved.</p>
    </div>
  </div>
</body>

</html>