const { AdviserStripe } = require("../models");

async function createStripeAccount(adviserId, stripeAccountId) {
  const stripeAccountDetails = new AdviserStripe({
    adviser: adviserId,
    stripeAccountId,
  });
  const savedStripeAccountDetails = await stripeAccountDetails.save();
  return savedStripeAccountDetails;
}

async function deleteStripeAccount(adviserId) {
  await AdviserStripe.findOneAndDelete({ adviser: adviserId });
  return true;
}

async function getAllStripeAccounts(filter, options = {}) {
  const accounts = await AdviserStripe.paginate(filter, options);
  return accounts;
}

async function getOne(filter) {
  const account = await AdviserStripe.findOne(filter).populate("adviser");
  return account;
}

async function update(filter, data) {
  const account = await AdviserStripe.findOneAndUpdate(filter, data, {
    new: true, // Return the updated document
    runValidators: true, // Enforce schema validation
  }).populate("adviser");

  return account;
}

module.exports = {
  createStripeAccount,
  deleteStripeAccount,
  getAllStripeAccounts,
  getOne,
  update,
};
