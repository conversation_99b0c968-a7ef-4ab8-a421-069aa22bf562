const { chatService, appointmentService } = require("../services");
const catchAsync = require("../utils/catchAsync");
const ApiError = require("../utils/ApiError");
const httpStatus = require("http-status");
const { fileUploadService } = require("../microservices");
const { determineFileType } = require("../utils/helper");
const { userTypes } = require("../constants");

const createChatRoom = catchAsync(async (req, res) => {
  const chatRoom = await chatService.createChatRoom(req.body.participants);
  return res.status(201).json(chatRoom);
});

const sendMessage = catchAsync(async (req, res) => {
  const {
    content,

    replyTo = null,
    chatRoomId = null,
  } = req.body;

  const senderId = req.user._id.toString();
  if (!content && !req.file) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      "Either attachment or content are required."
    );
  }

  if (!chatRoomId) {
    throw new ApiError(httpStatus.BAD_REQUEST, "chatRoomId is required.");
  }
  let attachments = [];

  if (req.file) {
    const { fileType } = await determineFileType(req.file);

    const uploadedFile = await fileUploadService
      .s3Upload([req.file], "chat_attachments")
      .catch((e) => {
        throw new ApiError(
          httpStatus.INTERNAL_SERVER_ERROR,
          `Failed to upload documents : ${e.message}`
        );
      });
    attachments = { file: uploadedFile[0], fileType };
  }

  const message = await chatService.sendMessage(
    senderId,
    content,
    attachments,
    replyTo,
    chatRoomId
  );

  return res.status(httpStatus.OK).json({
    message: "Message sent successfully",
    data: message,
  });
});

const getMessages = catchAsync(async (req, res) => {
  const { chatRoomId } = req.params;
  const { page = 1, limit = 50 } = req.query;
  const userId = req.user._id;

  const messages = await chatService.getMessages(
    chatRoomId,
    userId,
    parseInt(page, 10),
    parseInt(limit, 10)
  );

  return res.status(200).json(messages);
});

const getChatRooms = catchAsync(async (req, res) => {
  const userId = req.user._id.toString();

  const chatRooms = await chatService.getChatRoomsForUser(userId);

  if (!chatRooms) {
    throw new ApiError(
      httpStatus.NOT_FOUND,
      "No chat rooms found for the user."
    );
  }

  return res.status(httpStatus.OK).json({
    message: "Chat rooms retrieved successfully",
    data: chatRooms,
  });
});

const getOrCreateChatRoomIdForOneToOne = catchAsync(async (req, res, next) => {
  try {
    const userId = req.user._id;
    const { otherUserId } = req.body;

    if (!otherUserId) {
      throw new ApiError(httpStatus.BAD_REQUEST, "Other user ID is required.");
    }

    const chatRoomId = await chatService.getOrCreateOneToOneChatRoom(
      userId,
      otherUserId
    );

    return res.status(httpStatus.OK).json({
      chatRoomId,
    });
  } catch (error) {
    next(error);
  }
});

const deleteChatRoom = catchAsync(async (req, res) => {
  const { chatRoomId } = req.params;
  const userId = req.user._id;

  if (!chatRoomId) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Chat room ID is required");
  }

  const result = await chatService.deleteChatRoom(chatRoomId, userId);

  return res.status(httpStatus.OK).json(result);
});

const deleteMessage = catchAsync(async (req, res) => {
  const { messageId } = req.params;
  const userId = req.user._id;

  if (!messageId) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Message ID is required");
  }

  const result = await chatService.deleteMessage(messageId, userId);

  return res.status(httpStatus.OK).json(result);
});

const getUserstoChat = catchAsync(async (req, res) => {
  const userId = req.user._id;
  const userType = req.user.__t;

  // Fetch all appointments excluding rejected or cancelled ones
  const allAppointments =
    await appointmentService.getAppointmentsExcludingRejectedOrCancelled(
      userId
    );
  let userOrAdviserInfo;

  if (userType === userTypes.USER) {
    // ✅ Ensure unique adviser IDs
    userOrAdviserInfo = new Set(
      allAppointments.map((appointment) => appointment.adviser._id.toString())
    );

    // ✅ Ensure unique adviser objects using Map()
    const advisers = Array.from(
      new Map(
        allAppointments
          .filter((appointment) =>
            userOrAdviserInfo.has(appointment.adviser._id.toString())
          )
          .map((appointment) => [
            appointment.adviser._id.toString(),
            {
              _id: appointment.adviser._id,
              name: appointment.adviser.name,
              profilePic: appointment.adviser.profilePic,
            },
          ])
      ).values()
    );

    return res.status(200).json({
      message: "Advisers retrieved successfully",
      data: advisers,
    });
  } else if (userType === userTypes.ADVISER) {
    // ✅ Ensure unique user IDs
    userOrAdviserInfo = new Set(
      allAppointments.map((appointment) => appointment.user._id.toString())
    );

    // ✅ Ensure unique user objects using Map()
    const users = Array.from(
      new Map(
        allAppointments
          .filter((appointment) =>
            userOrAdviserInfo.has(appointment.user._id.toString())
          )
          .map((appointment) => [
            appointment.user._id.toString(),
            {
              _id: appointment.user._id,
              name: appointment.user.name,
              profilePic: appointment.user.profilePic,
            },
          ])
      ).values()
    );

    return res.status(200).json({
      message: "Users retrieved successfully",
      data: users,
    });
  } else {
    throw new ApiError(httpStatus.BAD_REQUEST, "Invalid user type");
  }
});

const getAppointmentChatRoom = catchAsync(async (req, res) => {
  const appointmentId = req.params.id;

  const appointment =
    await appointmentService.getAppointmentDetails(appointmentId);
  if (!appointment) {
    throw new ApiError(httpStatus.BAD_REQUEST, "appointment not found");
  }

  const chatRoom = await chatService.getChatRoomForUserAndAdviser(
    appointment.user,
    appointment.adviser
  );

  return res.status(httpStatus.OK).json({
    message: "Chat rooms retrieved successfully",
    data: chatRoom,
  });
});

const getMessagesForAdmin = catchAsync(async (req, res) => {
  const chatRoomId = req.params.id;
  const { page = 1, limit = 50 } = req.query;

  const messages = await chatService.getMessagesForAdmin(
    chatRoomId,
    page,
    limit
  );

  return res.status(httpStatus.OK).json({
    message: "Chat rooms retrieved successfully",
    data: messages,
  });
});

module.exports = {
  createChatRoom,
  sendMessage,
  getMessages,
  getChatRooms,
  deleteChatRoom,
  deleteMessage,
  getUserstoChat,
  getAppointmentChatRoom,
  getMessagesForAdmin,
  getOrCreateChatRoomIdForOneToOne,
};
