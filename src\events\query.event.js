const { emitterEventNames, notificationCategories } = require("../constants");
const { appNotificationService, userService } = require("../services");
const catchEventHandler = require("../utils/catchEventHandler");
const { mailService } = require("../microservices");
const ejs = require("ejs");
const path = require("path");

/**
 * Handle query status update event
 * Triggered when an admin resolves a query
 */
const handleQueryStatusUpdate = catchEventHandler(async (data) => {
  const { queryId, userId, ticketId, status, adminMessage } = data;

  // console.log(`🔔 Processing query status update for ticket ${ticketId}`);

  // Create notification title and description
  const title = `Your Query ${ticketId} has been ${status}`;
  let description = `Reply : ${adminMessage}`;

  // Create and send in-app notification
  await appNotificationService.createAndSendNotification(
    {
      title,
      description,
      type: notificationCategories.QUERY,
      targetUser: userId,
      isCreatedByAdmin: false,
      metadata: {
        queryId,
        ticketId,
      },
    },

    userId.toString()
  );

  // console.log(`✅ Query status update notification sent to user ${userId}`);

  // Send email notification
  try {
    // Get user details
    const user = await userService.getUserById(userId);

    if (user && user.email) {
      // console.log(`📧 Sending query status update email to ${user.email}`);

      // Render email template
      const emailHtml = await ejs.renderFile(
        path.join(__dirname, "../views/emails/queryStatusUpdate.ejs"),
        {
          name: user.name || "",
          ticketId,
          status,
          adminMessage: adminMessage || "No additional message provided.",
        }
      );

      // Send email
      await mailService.sendEmail({
        to: user.email,
        subject: `Money Owls - Query ${ticketId} Status Update`,
        html: emailHtml,
      });

      // console.log(`✅ Query status update email sent to ${user.email}`);
    }
  } catch (error) {
    console.error(
      `❌ Error sending query status update email: ${error.message}`
    );
  }
});

// Register event handlers
module.exports = (emitter) => {
  emitter.on(emitterEventNames.QUERY_STATUS_UPDATED, handleQueryStatusUpdate);
};
