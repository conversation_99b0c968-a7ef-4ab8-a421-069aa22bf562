const { AdviserAppointmentFeedBack } = require("../models");
const mongoose = require("mongoose");

const createFeedback = async (feedbackData) => {
  const feedback = new AdviserAppointmentFeedBack(feedbackData);
  return await feedback.save();
};

const getFeedbackByAppointmentId = async (appointmentId) => {
  return await AdviserAppointmentFeedBack.findOne({ appointmentId })
    .populate("appointmentId", "date timeSlot")
    .populate("adviserId", "name profilePic")
    .populate("userId", "name profilePic");
};

const getFeedbackById = async (id) => {
  return await AdviserAppointmentFeedBack.findById(id)
    .populate("appointmentId", "date timeSlot duration status")
    .populate("adviserId", "name email")
    .populate("userId", "name email");
};

const updateAdviserFeedback = async (appointmentId, updateData) => {
  return await AdviserAppointmentFeedBack.findOneAndUpdate(
    { appointmentId },
    { adviserResponse: updateData.adviserResponse },
    { new: true, runValidators: true }
  );
};

const updateUserFeedback = async (appointmentId, updateData) => {
  return await AdviserAppointmentFeedBack.findOneAndUpdate(
    { appointmentId },
    { userResponse: updateData.userResponse },
    { new: true, runValidators: true }
  );
};

const getAllFeedback = async (filter = {}, options = {}) => {
  const {
    page = 1,
    limit = 10,
    sortBy = "createdAt",
    sortOrder = "desc",
  } = options;

  const skip = (page - 1) * limit;
  const sort = { [sortBy]: sortOrder === "desc" ? -1 : 1 };

  const feedback = await AdviserAppointmentFeedBack.find(filter)
    .populate("appointmentId", "date timeSlot duration status")
    .populate("adviserId", "name email")
    .populate("userId", "name email")
    .sort(sort)
    .skip(skip)
    .limit(limit);

  const total = await AdviserAppointmentFeedBack.countDocuments(filter);

  return {
    results: feedback,
    page: parseInt(page),
    limit: parseInt(limit),
    totalPages: Math.ceil(total / limit),
    total,
  };
};

const feedbackExists = async (appointmentId) => {
  const count = await AdviserAppointmentFeedBack.countDocuments({
    appointmentId,
  });
  return count > 0;
};

module.exports = {
  createFeedback,
  getFeedbackByAppointmentId,
  getFeedbackById,
  updateAdviserFeedback,
  updateUserFeedback,
  getAllFeedback,
  feedbackExists,
};
