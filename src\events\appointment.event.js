const {
  emitterEventNames,
  appDefaults,
  notificationCategories,
  userTypes,
} = require("../constants/index");
const moment = require("moment-timezone");
const { appNotificationService, agendaService } = require("../services");
const catchEventHandler = require("../utils/catchEventHandler");
const { mailService, notificationService } = require("../microservices");
const ejs = require("ejs");
const path = require("path");
const { agenda } = require("../services/agenda.service");

// ✅ Handle appointment notifications booked only to adviser
const handleAppointmentNotifications = catchEventHandler(
  async ({ user, adviser, appointment, timezone = appDefaults.TIMEZONE }) => {
    // Format date and time if appointment data is available
    let formattedDate = "";
    let formattedTime = "";
    let bookingId = "";

    if (appointment) {
      // Only format date if it exists
      if (appointment.date) {
        try {
          formattedDate = moment(appointment.date)
            .tz(timezone)
            .format("Do MMMM YYYY");
        } catch (error) {
          console.error(
            `❌ Error formatting appointment date: ${error.message}`
          );
        }
      }

      // Only format time if timeSlot.start exists
      if (appointment.timeSlot && appointment.timeSlot.start) {
        try {
          formattedTime = moment(appointment.timeSlot.start)
            .tz(timezone)
            .format("h:mm a");
        } catch (error) {
          console.error(
            `❌ Error formatting appointment time: ${error.message}`
          );
        }
      }

      // Set bookingId if it exists
      bookingId = appointment.bookingId || "";
    }

    // ✅ Adviser notification
    const adviserNotificationData = {
      title: "New Appointment Request",
      description: `A new appointment is requested from ${user.name}, please accept it to confirm.`,
      type: notificationCategories.APPOINTMENTS,
      targetUser: adviser._id.toString(),
      image: "",
    };

    // ✅ Send notifications
    await appNotificationService.createAndSendNotification(
      adviserNotificationData,
      adviser._id.toString()
    );

    // ✅ Send email to adviser with the appropriate template for a request
    const adviserEmailHtml = await ejs.renderFile(
      path.join(__dirname, "../views/emails/appointmentRequest.ejs"),
      {
        name: adviser.name,
        counterpart: user.name,
        date: formattedDate,
        time: formattedTime,
        bookingId: bookingId,
      }
    );

    await mailService.sendEmail({
      to: adviser.email,
      subject: "Money Owls - New Appointment Request",
      html: adviserEmailHtml,
    });

    // console.log(
    //   `✅ Appointment request notification and email sent to adviser ${adviser.email}`
    // );
  }
);

// ✅ Handle appointment reminder notifications
const handleAppointmentReminder = catchEventHandler(
  async ({
    appointment,
    user,
    adviser,
    notificationTriggerTime,
    appointmentStartTime,
  }) => {
    try {
      // Format time safely
      let formattedTime = "the scheduled time";
      try {
        formattedTime = appointmentStartTime.format("h:mm a");
      } catch (error) {
        console.error(`❌ Error formatting reminder time: ${error.message}`);
      }

      // Schedule reminder notifications
      await agendaService.scheduleNotification(
        notificationTriggerTime.toDate(),
        {
          topic: user._id.toString(),
          title: "Upcoming Appointment",
          type: notificationCategories.APPOINTMENTS,
          description: `You have an upcoming appointment with ${
            adviser.name || "your adviser"
          } at ${formattedTime}`,
          appointmentId: appointment._id.toString(),
          targetUser: user._id,
        }
      );

      await agendaService.scheduleNotification(
        notificationTriggerTime.toDate(),
        {
          topic: adviser._id.toString(),
          title: "Upcoming Appointment",
          type: notificationCategories.APPOINTMENTS,
          description: `You have an upcoming appointment with ${
            user.name || "a client"
          } at ${formattedTime}`,
          appointmentId: appointment._id.toString(),
          targetUser: adviser._id,
        }
      );
    } catch (error) {
      console.error(
        `❌ Error scheduling reminder notifications: ${error.message}`
      );
    }

    // Schedule auto-cancel job for appointment end time
    if (appointment.timeSlot && appointment.timeSlot.end) {
      const endTime = new Date(appointment.timeSlot.end);
      const now = new Date();

      if (endTime > now) {
        // console.log(
        //   `📅 Scheduling auto-cancel job for new appointment ${appointment._id} at end time ${endTime}`
        // );

        // Schedule the auto-cancel job
        await agendaService.scheduleAppointmentAutoCancel(
          appointment._id,
          endTime
        );

        // console.log(
        //   `✅ Auto-cancel job scheduled for new appointment ${appointment._id}`
        // );
      }
    }
  }
);

const handleAppointmentCancellation = catchEventHandler(
  async ({ appointment, targetUserId, name, triggeredBy }) => {
    let formattedDate = "";
    let formattedTime = "";

    // Only format date if it exists
    if (appointment.date) {
      try {
        formattedDate = moment(appointment.date)
          .tz(appDefaults.TIMEZONE)
          .format("Do MMMM YYYY");
      } catch (error) {
        console.error(
          `❌ Error formatting cancellation date: ${error.message}`
        );
      }
    }

    // Only format time if timeSlot.start exists
    if (appointment.timeSlot && appointment.timeSlot.start) {
      try {
        formattedTime = moment(appointment.timeSlot.start)
          .tz(appDefaults.TIMEZONE)
          .format("h:mm a");
      } catch (error) {
        console.error(
          `❌ Error formatting cancellation time: ${error.message}`
        );
      }
    }

    let description = "";
    let recipientEmail = "";
    let recipientName = "";
    let counterpartName = "";

    // Determine who is being notified and set variables accordingly
    if (triggeredBy === userTypes.ADVISER) {
      description = `Your appointment with ${name} has been cancelled.`;

      const user = appointment.user;
      recipientEmail = user.email;
      recipientName = user.name;
      counterpartName = name;
    } else if (triggeredBy === userTypes.USER) {
      description = `Your appointment with ${name} on ${formattedDate} at ${formattedTime} has been cancelled.`;

      const adviser = appointment.adviser;
      recipientEmail = adviser.email;
      recipientName = adviser.name;
      counterpartName = name; // user name
    }

    const notificationData = {
      title: "Appointment Cancelled",
      description,
      type: notificationCategories.APPOINTMENTS,
      targetUser: targetUserId,
      image: "",
    };

    await appNotificationService.createAndSendNotification(
      notificationData,
      targetUserId
    );

    try {
      // Render email template
      const emailHtml = await ejs.renderFile(
        path.join(__dirname, "../views/emails/appointmentCancellation.ejs"),
        {
          name: recipientName || "",
          counterpartName: counterpartName,
          date: formattedDate,
          time: formattedTime,
          bookingId: appointment.bookingId,
          triggeredBy: triggeredBy,
        }
      );

      await mailService.sendEmail({
        to: recipientEmail,
        subject: "Money Owls - Appointment Cancelled",
        html: emailHtml,
      });
    } catch (error) {
      console.error(
        `❌ Error sending appointment cancellation email: ${error.message}`
      );
    }

    await agenda._collection.deleteMany({
      name: "schedule notification",
      "data.appointmentId": appointment._id.toString(),
    });

    const autoCancelJobCancelResult = await agenda._collection.deleteMany({
      name: "appointment-auto-cancel",
      "data.appointmentId": appointment._id.toString(),
    });
    // console.log(
    //   `🗑️ Deleted ${autoCancelJobCancelResult.deletedCount} auto-cancel job(s) for cancelled appointment`
    // );
  }
);

// ✅ Handle appointment rescheduling
const handleAppointmentRescheduled = catchEventHandler(
  async ({ appointment, user, timezone }) => {
    try {
      // Cancel existing reminder jobs
      const reminderJobCancelResult = agenda._collection.deleteMany({
        name: "schedule notification",
        "data.appointmentId": appointment._id.toString(),
      });

      // Format date and time for notifications
      let formattedDate = "the scheduled date";
      let formattedTime = "the scheduled time";

      // Only format date if it exists
      if (appointment.date) {
        try {
          formattedDate = moment(appointment.date)
            .tz(timezone)
            .format("MMMM Do YYYY");
        } catch (error) {
          console.error(
            `❌ Error formatting rescheduled date: ${error.message}`
          );
        }
      }

      // Only format time if timeSlot.start exists
      if (appointment.timeSlot && appointment.timeSlot.start) {
        try {
          formattedTime = moment(appointment.timeSlot.start)
            .tz(timezone)
            .format("h:mm a");
        } catch (error) {
          console.error(
            `❌ Error formatting rescheduled time: ${error.message}`
          );
        }
      }

      // Prepare notification data for user and adviser
      const userNotificationData = {
        title: "Appointment Rescheduled",
        description: `Your appointment with ${appointment.adviser.name} has been rescheduled to ${formattedDate} at ${formattedTime}`,
        type: notificationCategories.APPOINTMENTS,
        targetUser: user._id.toString(),
        image: "",
      };

      const adviserNotificationData = {
        title: "Appointment Rescheduled",
        description: `Your appointment with ${user.name} has been rescheduled to ${formattedDate} at ${formattedTime}`,
        type: notificationCategories.APPOINTMENTS,
        targetUser: appointment.adviser._id.toString(),
        image: "",
      };

      // Send notifications and emails in parallel using Promise.all
      const sendNotifications = [
        appNotificationService.createAndSendNotification(
          userNotificationData,
          user._id.toString()
        ),
        appNotificationService.createAndSendNotification(
          adviserNotificationData,
          appointment.adviser._id.toString()
        ),
      ];

      const sendEmails = [
        // Send email to user
        (async () => {
          // console.log(`📧 Sending rescheduling email to user: ${user.email}`);
          const userEmailHtml = await ejs.renderFile(
            path.join(__dirname, "../views/emails/appointmentRescheduled.ejs"),
            {
              name: user.name || "",
              counterpartName: appointment.adviser.name,
              date: formattedDate,
              time: formattedTime,
              bookingId: appointment.bookingId,
            }
          );
          await mailService.sendEmail({
            to: appointment.user.email,
            subject: "Money Owls - Appointment Rescheduled",
            html: userEmailHtml,
          });
          // console.log(`✅ Rescheduling email sent to user: ${user.email}`);
        })(),

        // Send email to adviser
        (async () => {
          // console.log(
          //   `📧 Sending rescheduling email to adviser: ${appointment.adviser.email}`
          // );
          const adviserEmailHtml = await ejs.renderFile(
            path.join(__dirname, "../views/emails/appointmentRescheduled.ejs"),
            {
              name: appointment.adviser.name || "",
              counterpartName: user.name,
              date: formattedDate,
              time: formattedTime,
              bookingId: appointment.bookingId,
            }
          );
          await mailService.sendEmail({
            to: appointment.adviser.email,
            subject: "Money Owls - Appointment Rescheduled",
            html: adviserEmailHtml,
          });
          // console.log(
          //   `✅ Rescheduling email sent to adviser: ${appointment.adviser.email}`
          // );
        })(),
      ];

      // Wait for both notifications and emails to complete
      await Promise.all([...sendNotifications, ...sendEmails]);

      // ✅ THIRD: Schedule new reminders
      // console.log(
      //   `⏰ Scheduling new reminders for appointment: ${appointment._id}`
      // );
      const appointmentStartTime = moment(appointment.timeSlot.start).tz(
        timezone
      );
      const notificationTriggerTime = appointmentStartTime
        .clone()
        .subtract(10, "minutes");

      const scheduleReminders = [
        agendaService.scheduleNotification(notificationTriggerTime.toDate(), {
          topic: user._id.toString(),
          title: "Upcoming Appointment Reminder",
          description: `You have an upcoming appointment with ${appointment.adviser.name} at ${appointmentStartTime.format("h:mm a")}`,
          type: notificationCategories.APPOINTMENTS,
          targetUser: user._id.toString(),
          appointmentId: appointment._id.toString(),
        }),

        agendaService.scheduleNotification(notificationTriggerTime.toDate(), {
          topic: appointment.adviser._id.toString(),
          title: "Upcoming Appointment Reminder",
          description: `You have an upcoming appointment with ${user.name} at ${appointmentStartTime.format("h:mm a")}`,
          type: notificationCategories.APPOINTMENTS,
          targetUser: appointment.adviser._id.toString(),
          appointmentId: appointment._id.toString(),
        }),
      ];

      // Schedule the auto-cancel job for appointment end time if applicable
      if (appointment.timeSlot && appointment.timeSlot.end) {
        const endTime = new Date(appointment.timeSlot.end);
        const now = new Date();

        if (endTime > now) {
          const scheduleAutoCancel =
            agendaService.scheduleAppointmentAutoCancel(
              appointment._id,
              endTime
            );
          scheduleReminders.push(scheduleAutoCancel);
        }
      }

      // Wait for all reminder-related tasks to finish
      await Promise.all(scheduleReminders);
    } catch (error) {
      console.error(
        `❌ Error handling rescheduled appointment: ${error.message}`
      );
    }
  }
);

// Handle appointment acceptance event
const handleAppointmentAccepted = catchEventHandler(async (data) => {
  const { appointment, targetUserId, adviserName } = data;

  // console.log(
  //   `🔔 Processing appointment acceptance notification for user ${targetUserId}`
  // );

  try {
    // Format the appointment date and time for the notification
    let appointmentDate = "";
    let formattedTime = "";

    // Only format date if it exists
    if (appointment.date) {
      try {
        appointmentDate = moment(appointment.date)
          .tz(appDefaults.TIMEZONE)
          .format("Do MMMM YYYY");
      } catch (error) {
        console.error(`❌ Error formatting acceptance date: ${error.message}`);
        appointmentDate = "the scheduled date";
      }
    } else {
      appointmentDate = "the scheduled date";
    }

    // Only format time if timeSlot.start exists
    if (appointment.timeSlot && appointment.timeSlot.start) {
      try {
        formattedTime = moment(appointment.timeSlot.start)
          .tz(appDefaults.TIMEZONE)
          .format("h:mm a");
      } catch (error) {
        console.error(`❌ Error formatting acceptance time: ${error.message}`);
        formattedTime = "the scheduled time";
      }
    } else {
      formattedTime = "the scheduled time";
    }
    // Create notification title and description
    const title = "Appointment Confirmed";
    const description = `Your appointment with ${adviserName} on ${appointmentDate} at ${formattedTime} has been confirmed.`;

    // Send push notification
    await notificationService.sendToTopic(
      targetUserId.toString(),
      {
        title,
        body: description,
        image: "",
      },
      {
        type: notificationCategories.APPOINTMENTS,
        appointmentId: appointment._id.toString(),
      }
    );

    // Create in-app notification
    await appNotificationService.createNotification({
      title,
      description,
      type: notificationCategories.APPOINTMENTS,
      targetUser: targetUserId,
      scheduledAt: Date.now(),
      targetRole: null,
      isCreatedByAdmin: false,
      data: {
        appointmentId: appointment._id.toString(),
        adviserId: appointment.adviser._id.toString(),
      },
    });

    // Get user details
    const user = appointment.user;
    if (user && user.email) {
      // Send email notification
      try {
        // console.log(`📧 Sending appointment acceptance email to ${user.email}`);

        // Render email template
        const emailHtml = await ejs.renderFile(
          path.join(__dirname, "../views/emails/appointmentAccepted.ejs"),
          {
            name: user.name || "",
            adviserName: adviserName,
            date: appointmentDate,
            time: formattedTime,
            bookingId: appointment.bookingId,
          }
        );

        // Send email
        await mailService.sendEmail({
          to: user.email,
          subject: "Money Owls - Appointment Confirmed",
          html: emailHtml,
        });

        // console.log(`✅ Appointment acceptance email sent to ${user.email}`);
      } catch (error) {
        console.error(
          `❌ Error sending appointment acceptance email: ${error.message}`
        );
      }
    }

    // console.log(
    //   `✅ Appointment acceptance notification sent to user ${targetUserId}`
    // );
  } catch (error) {
    console.error(
      `❌ Error sending appointment acceptance notification: ${error.message}`
    );
  }
});

// Handle user missed appointment event
const handleUserMissedAppointment = catchEventHandler(async (data) => {
  const { appointment, userId, adviserId, adviserName } = data;

  // console.log(
  //   `🔔 Processing user missed appointment notification for user ${userId}`
  // );

  try {
    // Format the appointment date and time for the notification
    let appointmentDate = "";
    let formattedTime = "";

    // Only format date if it exists
    if (appointment.date) {
      try {
        appointmentDate = moment(appointment.date)
          .tz(appDefaults.TIMEZONE)
          .format("Do MMMM YYYY");
      } catch (error) {
        console.error(`❌ Error formatting date: ${error.message}`);
        appointmentDate = "the scheduled date";
      }
    } else {
      appointmentDate = "the scheduled date";
    }

    // Only format time if timeSlot.start exists
    if (appointment.timeSlot && appointment.timeSlot.start) {
      try {
        formattedTime = moment(appointment.timeSlot.start)
          .tz(appDefaults.TIMEZONE)
          .format("h:mm a");
      } catch (error) {
        console.error(`❌ Error formatting time: ${error.message}`);
        formattedTime = "the scheduled time";
      }
    } else {
      formattedTime = "the scheduled time";
    }

    // Create notification title and description for user
    const userTitle = "Missed Appointment";
    const userDescription = `You missed your appointment with ${adviserName} on ${appointmentDate} at ${formattedTime}.`;

    // Send push notification to user
    await notificationService.sendToTopic(
      userId.toString(),
      {
        title: userTitle,
        body: userDescription,
        image: "",
      },
      {
        type: notificationCategories.APPOINTMENTS,
        appointmentId: appointment._id.toString(),
      }
    );

    // Create in-app notification for user
    await appNotificationService.createNotification({
      title: userTitle,
      description: userDescription,
      type: notificationCategories.APPOINTMENTS,
      targetUser: userId,
      scheduledAt: Date.now(),
      targetRole: null,
      isCreatedByAdmin: false,
      data: {
        appointmentId: appointment._id.toString(),
        adviserId: adviserId,
      },
    });

    // console.log(
    //   `✅ User missed appointment notification sent to user ${userId}`
    // );
  } catch (error) {
    console.error(
      `❌ Error sending user missed appointment notification: ${error.message}`
    );
  }
});

// ✅ Register event listeners
module.exports = (emitter) => {
  emitter.on(
    emitterEventNames.APPOINTMENT_NOTIFICATIONS,
    handleAppointmentNotifications
  );
  emitter.on(emitterEventNames.APPOINTMENT_REMINDER, handleAppointmentReminder);
  emitter.on(
    emitterEventNames.APPOINTMENT_CANCELLED,
    handleAppointmentCancellation
  );
  emitter.on(
    emitterEventNames.APPOINTMENT_RESCHEDULED,
    handleAppointmentRescheduled
  );
  emitter.on(emitterEventNames.APPOINTMENT_ACCEPTED, handleAppointmentAccepted);
  emitter.on(
    emitterEventNames.USER_MISSED_APPOINTMENT,
    handleUserMissedAppointment
  );
};
