const { emitterEventNames, notificationCategories } = require("../constants");
const { appNotificationService } = require("../services");
const { sendToTopic } = require("../microservices/notification.service");
const catchEventHandler = require("../utils/catchEventHandler");

/**
 * Handle video call notification event
 * This event is triggered when an adviser initiates a video call
 * It sends notifications to the user
 */
const handleVideoCallNotification = catchEventHandler(async (data) => {
  const {
    initiator,
    recipient,
    recipientId,
    token,
    channelName,
    appointmentId,
    appointment,
    initiatorType,
    profilePicUrl,
  } = data;

  if (!recipientId || !token || !channelName || !appointmentId) {
    console.error("❌ Missing required data for video call notification");
    return;
  }

  // console.log(`🔔 Sending video call notification to user ${recipientId}`);

  try {
    // Create notification data
    const notificationData = {
      title: `Incoming video call from ${initiator}!`,
      body: `${initiator} is calling you for your scheduled appointment`,
      type: notificationCategories.VIDEOCALL,
      targetUser: recipientId,
      image: profilePicUrl || "example.com",
    };

    // Send push notification
    await sendToTopic(recipientId, notificationData, {
      type: notificationCategories.VIDEOCALL,
      token: token.toString(),
      channelName: channelName.toString(),
      targetUser: recipientId,
      appointmentTiming: appointment.timeSlot.start.toISOString(),
      appointmentStartTime: appointment.timeSlot.start.toISOString(),
      appointmentEndTime: appointment.timeSlot.end.toISOString(),
      appointmentId: appointmentId.toString(),
      image: profilePicUrl || "",
    });

    //Create in-app notification
    await appNotificationService.createNotification({
      ...notificationData,
      data: {
        appointmentId: appointmentId.toString(),
      },
      type: notificationCategories.VIDEOCALL,
    });

    // console.log(
    //   `✅ Video call notification sent successfully to ${recipientId}`
    // );
  } catch (error) {
    console.error(`❌ Error sending video call notification: ${error.message}`);
  }
});

// Register event listeners
module.exports = (emitter) => {
  emitter.on(
    emitterEventNames.VIDEO_CALL_NOTIFICATION,
    handleVideoCallNotification
  );
};
