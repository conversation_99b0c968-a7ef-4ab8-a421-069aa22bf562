const express = require("express");
const { firebaseAuth } = require("../../middlewares/firebaseAuth");
const validate = require("../../middlewares/validate");
const { singleSessionProductValidation } = require("../../validations");
const {
  subscriptionProductController,
  singleSessionProductController,
} = require("../../controllers");

const router = express.Router();

router.post(
  "/subscription",
  firebaseAuth("Admin"),
  subscriptionProductController.createSubscriptionProduct
);

router.get(
  "/subscription",
  subscriptionProductController.getAllSubscriptionProducts
);

router.get(
  "/subscription/:id",
  firebaseAuth("All"),
  subscriptionProductController.getSubscriptionProductById
);

router.patch(
  "/subscription/:id",
  firebaseAuth("Admin"),
  subscriptionProductController.updateSubscriptionProduct
);

router.delete(
  "/subscription/:id",
  firebaseAuth("Admin"),
  subscriptionProductController.deleteSubscriptionProduct
);

router.get(
  "/sessions/",
  singleSessionProductController.getSingleSessionProducts
);
router.patch(
  "/sessions/edit/:id",
  firebaseAuth("Admin"),
  validate(singleSessionProductValidation.updateSession),
  singleSessionProductController.updateSession
);
router.post(
  "/sessions/",
  firebaseAuth("Admin"),
  validate(singleSessionProductValidation.createSession),
  singleSessionProductController.setSessionPricing
);
router.delete(
  "/sessions/delete/:id",
  firebaseAuth("Admin"),
  validate(singleSessionProductValidation.deleteSession),
  singleSessionProductController.deleteSession
);

// router.delete("/stripeconnect/:id", firebaseAuth("All"));
module.exports = router;
