const httpStatus = require("http-status");
const {
  adviserService,
  appointmentService,
  userService,
  customerSubscriptionService,
  appNotificationService,
  authService,
} = require("../services/index");
const ApiError = require("../utils/ApiError");
const catchAsync = require("../utils/catchAsync");
const { getPaginateConfig } = require("../utils/queryPHandler");
const { fileUploadService } = require("../microservices");
const moment = require("moment");
const { Users, Admin, Adviser, CustomerSubscription } = require("../models");
const { getAuth } = require("firebase-admin/auth");
const { userTypes } = require("../constants");
const { emitterEventNames, appDefaults } = require("../constants");
const eventEmitter = require("../events/eventEmitter");
const { default: mongoose } = require("mongoose");

const getUserSubscriptiondata = catchAsync(async (req, res) => {
  let userId = req.user.id;
  if (req.user.__t === userTypes.ADMIN) {
    userId = req.params.id;
  }

  const subscriptionData =
    await customerSubscriptionService.getCustomerSubscription({
      user: userId,
      status: { $ne: "cancelled" },
    });

  if (!subscriptionData) {
    return res.status(httpStatus.OK).json({
      message: "no subscription found",
      status: false,
      data: {},
    });
  }

  return res.status(httpStatus.OK).json({
    message: "subscription found",
    status: true,
    data: { subscriptionData },
  });
});

const getSubscriptionHistoryData = catchAsync(async (req, res) => {
  let userId = req.user._id;
  if (req.user.__t === userTypes.ADMIN) {
    userId = req.params.id;
  }
  const subscriptionData =
    await customerSubscriptionService.getAllCustomerSubscription({
      user: userId,
    });

  if (!subscriptionData) {
    return res.status(httpStatus.OK).json({
      message: "no subscription found",
      status: false,
      data: {},
    });
  }

  return res.status(httpStatus.OK).json({
    message: "subscription found",
    status: true,
    data: { subscriptionData },
  });
});

const checkSubscriptionUsage = catchAsync(async (req, res) => {
  const adviserId = req.params.adviserId;
  const userId = req.user._id;

  const userSubscription =
    await customerSubscriptionService.getCustomerSubscription({ user: userId });
  if (!userSubscription) {
    throw new ApiError(httpStatus.BAD_REQUEST, "No active subscription found.");
  }

  const { isActive, status, remainingSessions, startDate, endDate } =
    userSubscription;

  // Case: Cancelled subscription but with remaining session
  // if (!isActive && status === "cancelled" && remainingSessions > 0) {
  //   return res.status(202).json({
  //     status: false,
  //     message: "Subscription is cancelled but you still have 1 session left.",
  //   });
  // }

  // Case: Inactive subscription with no session left
  if (!isActive) {
    throw new ApiError(httpStatus.FORBIDDEN, "Your subscription is inactive.");
  }

  if (remainingSessions <= 0) {
    throw new ApiError(
      httpStatus.FORBIDDEN,
      "You have used all your subscription sessions."
    );
  }

  // Check if a subscription session is already booked
  const sessionStart = moment(startDate).startOf("day");
  const sessionEnd = moment(endDate).endOf("day");

  const existingAppointments = await appointmentService.findAppointmentWhere({
    user: userId,
    adviser: adviserId,
    mode: "subscription",
    status: { $ne: "cancelled" },
    createdAt: {
      $gte: sessionStart.toDate(),
      $lte: sessionEnd.toDate(),
    },
  });

  const alreadyBooked = existingAppointments?.length > 0;

  return res.status(200).json({
    alreadyBooked,
    message: alreadyBooked
      ? "You have already booked a session in this subscription period."
      : "You are eligible to book a session using your subscription.",
  });
});

const getSubscriptionStats = catchAsync(async (req, res) => {
  const currentYear = moment().year();
  const lastYear = currentYear - 1;

  // 1. Total active unique subscribers
  const totalSubscribers = await CustomerSubscription.distinct("user", {
    isActive: true,
  }).then((users) => users.length);

  // 2. Unique user subscriptions in current year
  const currentYearUsers = await CustomerSubscription.distinct("user", {
    createdAt: {
      $gte: moment().startOf("year").toDate(),
      $lte: moment().endOf("year").toDate(),
    },
  });

  // 3. Unique user subscriptions in last year
  const lastYearUsers = await CustomerSubscription.distinct("user", {
    createdAt: {
      $gte: moment().year(lastYear).startOf("year").toDate(),
      $lte: moment().year(lastYear).endOf("year").toDate(),
    },
  });

  // 4. Monthly breakdown using `$group` and `$addToSet` to get unique users
  const monthlyCounts = await CustomerSubscription.aggregate([
    {
      $match: {
        createdAt: {
          $gte: moment().year(lastYear).startOf("year").toDate(),
          $lte: moment().endOf("year").toDate(),
        },
      },
    },
    {
      $group: {
        _id: {
          year: { $year: "$createdAt" },
          month: { $month: "$createdAt" },
        },
        users: { $addToSet: "$user" }, // collect unique userIds
      },
    },
    {
      $project: {
        _id: 1,
        count: { $size: "$users" }, // count unique users
      },
    },
    { $sort: { "_id.year": 1, "_id.month": 1 } },
  ]);

  // 5. Initialize structure
  const monthlyData = {
    [currentYear]: Array(12).fill(0),
    [lastYear]: Array(12).fill(0),
  };

  // 6. Populate monthly data
  monthlyCounts.forEach((entry) => {
    const { year, month } = entry._id;
    if (monthlyData[year]) {
      monthlyData[year][month - 1] = entry.count;
    }
  });

  return res.status(httpStatus.OK).json({
    totalSubscribers,
    yearlyComparison: {
      currentYear: currentYearUsers.length,
      lastYear: lastYearUsers.length,
    },
    monthlyBreakdown: {
      [currentYear]: monthlyData[currentYear],
      [lastYear]: monthlyData[lastYear],
    },
  });
});

const getAllSubscriptions = catchAsync(async (req, res) => {
  const { filters, options } = getPaginateConfig(req.query);
  const subscriptionData =
    await customerSubscriptionService.getAllCustomerSubscriptions(
      filters,
      options
    );

  if (!subscriptionData) {
    return res.status(httpStatus.OK).json({
      data: {},
    });
  }

  return res.status(httpStatus.OK).json({
    data: subscriptionData,
  });
});

const checkSubscriptionAfterAppointment = catchAsync(async (req, res) => {
  const userId = req.user._id;

  // Check if user already has an active subscription
  const hasActiveSubscription =
    await customerSubscriptionService.hasActiveSubscription(userId);

  if (!hasActiveSubscription) {
    // Schedule a job to check subscription status after 24 hours
    const triggerDate = moment().add(24, "hours").toDate();

    // Use the agenda service to schedule the job
    eventEmitter.emit(emitterEventNames.SUBSCRIPTION_REMINDER, {
      userId,
      triggerDate,
    });

    return res.status(httpStatus.OK).json({
      success: true,
      message: "Subscription check scheduled for 24 hours from now",
      data: {
        userId,

        triggerDate,
        hasActiveSubscription: false,
      },
    });
  }

  return res.status(httpStatus.OK).json({
    success: true,
    message: "User already has an active subscription",
    data: {
      userId,
      hasActiveSubscription: true,
    },
  });
});

const getSubscribersGeoStats = catchAsync(async (req, res) => {
  const results =
    await customerSubscriptionService.getActiveSubscribersGroupedByState();

  return res.status(httpStatus.OK).json({
    success: true,
    message: "Active subscribers grouped by state",
    data: results,
  });
});

module.exports = {
  getUserSubscriptiondata,
  checkSubscriptionUsage,
  getSubscriptionStats,
  getAllSubscriptions,
  checkSubscriptionAfterAppointment,
  getSubscribersGeoStats,
  getSubscriptionHistoryData,
};
