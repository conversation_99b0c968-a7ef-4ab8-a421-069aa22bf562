const httpStatus = require("http-status");
const { Review } = require("../models");
const ApiError = require("../utils/ApiError");

async function createReview(reviewData) {
  const review = new Review(reviewData);
  return await review.save();
}

async function getAllReviews(filters = {}, options = {}) {
  const query = { ...filters };

  if (filters.rating) {
    const minRating = parseFloat(filters.rating);
    if (!isNaN(minRating)) {
      query.rating = { $gte: minRating };
    }
  }

  options.populate = [
    "user::name,profilePic",
    "adviser::name,profilePic,experience",
  ];
  const reviews = await Review.paginate(query, options);

  if (!reviews) {
    throw new ApiError(
      httpStatus.NOT_FOUND,
      "No reviews found matching the criteria"
    );
  }

  return reviews;
}

async function getReviewDetails(id) {
  const review = await Review.findById(id);

  if (!review) {
    throw new ApiError(httpStatus.NOT_FOUND, "No Review found");
  }

  return review;
}

async function updateReview(id, updateData) {
  const review = await Review.findByIdAndUpdate(id, updateData, {
    new: true,
    runValidators: true,
  });

  return review;
}

async function deleteReview(id) {
  const review = await Review.findByIdAndDelete(id);
  return review;
}

async function findReview(query) {
  const review = await Review.find({ query });

  if (!review) {
    throw new ApiError(httpStatus.NOT_FOUND, "No Review found");
  }

  return review;
}

async function countReviews(query) {
  return Review.countDocuments(query);
}
module.exports = {
  createReview,
  getAllReviews,
  getReviewDetails,
  updateReview,
  deleteReview,
  findReview,
  countReviews,
};
