const express = require("express");
const { firebaseAuth } = require("../../middlewares/firebaseAuth");
const { userController } = require("../../controllers");
const validate = require("../../middlewares/validate");
const {
  goalVali<PERSON>ton,
  appointmentValidaton,
  financialHealthValidation,
  reviewValidation,
} = require("../../validations");

const { goalService } = require("../../services");

const { fileUploadService } = require("../../microservices");

const router = express.Router();

router.patch(
  "/update-profile/:id?",
  fileUploadService.multerUpload.single("profilePic"),
  firebaseAuth("User,Admin"),
  userController.updateProfile
);

router.get("/details/:id?", firebaseAuth("All"), userController.getUserdetails);

router.post(
  "/create-goal",
  firebaseAuth("User"),
  validate(goalValidaton.createGoal),
  userController.createGoal
);

router.patch(
  "/goal/:goalId",
  firebase<PERSON>uth("User"),
  validate(goalValidaton.updateGoal),
  userController.updateGoal
);

router.get(
  "/goals/:userId?",
  firebaseAuth("User,Admin"),
  userController.getAllGoals
);

router.delete(
  "/deleteGoal/:goalId",
  firebaseAuth("User"),
  validate(goalService.goalParams),
  userController.deleteGoal
);

router.get(
  "/goals/:goalId",
  firebaseAuth("User"),
  validate(goalValidaton.goalParams),
  userController.getGoal
);

router.post(
  "/add-review/:adviserId",
  firebaseAuth("User"),
  validate(reviewValidation.addReview),
  userController.addReview
); //done validation

router.post(
  "/financial-health-create",
  firebaseAuth("User"),
  validate(financialHealthValidation.financialHealthValidationSchema),
  userController.createFinancialHealth
); //done validation

router.patch(
  "/financial-health-update",
  firebaseAuth("User"),
  userController.updateFinancialHealth
);

router.get(
  "/financial-health-chart",
  firebaseAuth("User"),
  userController.getPieChartData
); //done validation

router.get(
  "/financial-health-data/:id?",
  firebaseAuth("User,Admin"),
  userController.getFinancialHealth
); //done validation

router.get(
  "/notifications",
  firebaseAuth("User"),
  userController.getAllNotifications
); //done validation

router.post(
  "/fav-toggle/:adviserId",
  firebaseAuth("User"),
  userController.toggleFavouriteAdviser
); //done validation

//get favs

router.get("/favs", firebaseAuth("User"), userController.getAllFavourites); //done validation

router.delete(
  "/delete/:id?",
  firebaseAuth("User,Admin"),
  userController.deleteUser
);

router.patch(
  "/activate/:id?",
  firebaseAuth("Admin"),
  userController.activeUserAdminONly
);

router.post(
  "/avaliability/:adviserId",
  firebaseAuth("User"),
  validate(appointmentValidaton.getAvailableSlots),
  userController.getAvailableSlots
); //done validation

router.post("/code/check/", userController.checkReferralCode);

// Route for declining a video call
router.post(
  "/call-declined/:appointmentId",
  firebaseAuth("User,Adviser"),
  userController.callDeclined
);

router.get(
  "/get-reviews/:id?",
  firebaseAuth("User,Admin"),
  userController.getReviews
);

router.patch(
  "/call-entered/:appointmentId",
  firebaseAuth("User"),
  userController.callEntered
);

module.exports = router;
