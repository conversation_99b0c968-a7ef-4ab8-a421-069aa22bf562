const mongoose = require("mongoose");

const adviserStripeSchema = new mongoose.Schema(
  {
    adviser: {
      type: mongoose.SchemaTypes.ObjectId,
      ref: "Adviser",
      required: [true, "must have an adviser"],
    },

    stripeAccountId: String,
    hasCompletedOnboarding: {
      type: Boolean,
      default: false,
    },
  },
  { timestamps: true }
);

adviserStripeSchema.index({ adviser: 1 }, { unique: true });
adviserStripeSchema.methods.hasStripeAccount = function () {
  return Boolean(this.stripeAccountId);
};
const AdviserStripe = mongoose.model("AdviserStripe", adviserStripeSchema);

module.exports = { AdviserStripe };
