const Joi = require("joi");
const { mongoose } = require("mongoose");

const dbOptionsSchema = {
  limit: Joi.number().default(10),
  page: Joi.number().default(1),
  sortBy: Joi.string().default("createdAt"),
  sortOrder: Joi.string().valid("desc", "asc").default("desc"),
};

const objectId = (value, helpers) => {
  if (!value.match(/^[0-9a-fA-F]{24}$/)) {
    return helpers.message('"{{#label}}" must be a valid id');
  }
  return new mongoose.Types.ObjectId(value);
};
module.exports = { dbOptionsSchema, objectId };
