{"name": "money-owl", "version": "1.0.0", "description": "This is the backend for moneyowl", "main": "index.js", "scripts": {"dev": "nodemon src/index.js", "start": "node src/index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "<PERSON><PERSON>", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.678.0", "@aws-sdk/s3-request-presigner": "^3.678.0", "agenda": "^5.0.0", "agora-access-token": "^2.0.4", "axios": "^1.7.7", "body-parser": "^1.20.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.4.5", "ejs": "^3.1.10", "express": "^4.21.1", "firebase-admin": "^12.6.0", "helmet": "^8.0.0", "http-status": "^1.8.1", "joi": "^17.13.3", "moment": "^2.30.1", "mongoose": "^8.7.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "resend": "^4.4.1", "stripe": "^17.7.0", "uuid": "^10.0.0", "winston": "^3.15.0"}, "devDependencies": {"@eslint/js": "^9.21.0", "eslint": "^9.21.0", "eslint-plugin-security": "^3.0.1", "eslint-plugin-unicorn": "^59.0.1", "globals": "^16.0.0"}}