const mongoose = require("mongoose");
const { paginate } = require("./plugins/paginate");

const reviewSchema = new mongoose.Schema(
  {
    adviser: {
      type: mongoose.SchemaTypes.ObjectId,
      ref: "Adviser",
      required: true,
    },
    user: {
      type: mongoose.SchemaTypes.ObjectId,
      ref: "User",
      required: true,
    },

    comment: {
      type: String,
      required: false,
      trim: true,
    },
  },
  { timestamps: true }
);

reviewSchema.plugin(paginate);
const Review = mongoose.model("Review", reviewSchema);

module.exports = {
  Review,
};
