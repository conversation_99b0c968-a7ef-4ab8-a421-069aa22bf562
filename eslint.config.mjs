import globals from "globals";
import pluginJs from "@eslint/js";

export default [
  {
    files: ["**/*.js"],
    languageOptions: {
      sourceType: "commonjs",
      globals: globals.node,
    },
    ...pluginJs.configs.recommended, // Includes only bug-prevention rules
    rules: {
      /*** ✅ Bug-Prone / Logical Errors ***/
      eqeqeq: ["error", "always"], // Always use ===
      "no-console": "off", // Allow console.log

      "no-unused-vars": [
        "warn",
        { args: "after-used", ignoreRestSiblings: true },
      ],
      "no-undef": "error",
      "no-var": "error",
      "no-empty-function": "error",
      "no-useless-return": "error",
      "no-duplicate-imports": "error",
      "handle-callback-err": ["error", "^err"],

      /*** 🔴 Turn OFF all formatting/style rules ***/
      quotes: "off",
      semi: "off",
      "object-curly-spacing": "off",
      "array-bracket-spacing": "off",
      "comma-dangle": "off",
      indent: "off",
      "space-before-function-paren": "off",
      "keyword-spacing": "off",
    },
  },
];
