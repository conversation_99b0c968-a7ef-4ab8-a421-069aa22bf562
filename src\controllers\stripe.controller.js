const {
  appointmentService,
  refundService,
  payoutService,
} = require("../services");
const ApiError = require("../utils/ApiError");
const catchAsync = require("../utils/catchAsync");
const httpStatus = require("http-status");
const {
  userStripeService,
  stripeService,
  adviserStripeService,
  subscriptionProductService,
  customerSubscriptionService,
  revenueService,
} = require("../services/index");
const { Refund } = require("../models");
const { emitterEventNames, appDefaults } = require("../constants");
const { default: mongoose } = require("mongoose");
const eventEmitter = require("../events/eventEmitter");
const { generateFeeId } = require("../utils/helper");
const config = require("../config/config");

const getAllSavedCards = catchAsync(async (req, res) => {
  const user = await userStripeService.getOne({ user: req.user._id });

  if (!user) {
    throw new ApiError(httpStatus.NOT_FOUND, "No Stripe customer found");
  }

  const { cards, defaultCardId } = await stripeService.getCards(
    user.stripeCustomerId
  );

  const formattedResponse = cards.map((card) => ({
    id: card.id,
    brand: card.brand,
    last4: card.last4,
    exp_month: card.exp_month,
    exp_year: card.exp_year,
    country: card.country,
    funding: card.funding,
    isDefault: card.id === defaultCardId,
  }));

  return res.json({ data: formattedResponse });
});

const saveCard = catchAsync(async (req, res) => {
  const user = await userStripeService.getOne({ user: req.user._id });
  if (!user) {
    throw new ApiError(httpStatus.NOT_FOUND, "no stripe customer found");
  }
  const card = await stripeService.addCard(user, req.body.token);
  return res.json({ data: card });
});

const makeCardDefault = catchAsync(async (req, res) => {
  const user = await userStripeService.getOne({ user: req.user._id });

  if (!user) {
    throw new ApiError(httpStatus.NOT_FOUND, "No Stripe customer found");
  }

  const result = await stripeService.makeCardDefault(
    user.stripeCustomerId,
    req.body.cardId
  );

  return res.status(200).json({
    success: true,
    message: "Card set as default successfully.",
    data: result,
  });
});

const changeDefaultCard = catchAsync(async (req, res) => {
  const { cardId } = req.body;

  const userSubscription =
    await customerSubscriptionService.getCustomerSubscription({
      user: req.user._id,
      isActive: true,
      status: { $ne: "cancelled" },
      endDate: { $gte: new Date() },
    });

  if (!userSubscription || !userSubscription.stripeSubscriptionId) {
    throw new ApiError(httpStatus.NOT_FOUND, "Active subscription not found.");
  }

  const updatedSubscription = await stripeService.changeCard(
    userSubscription.stripeSubscriptionId,
    cardId
  );

  // console.log(
  //   "✅ Default payment method updated:",
  //   updatedSubscription.default_payment_method
  // );

  return res.status(httpStatus.OK).json({
    message: "Default payment method updated successfully.",
    data: updatedSubscription,
  });
});

const deleteAdviserStripeConnectAccount = catchAsync(async (req, res) => {
  const user = await adviserStripeService.getOne({ adviser: req.user._id });
  const deleteAccount = await stripeService.deleteConnectedAccount(
    user.stripeAccountId
  );
  const deleteFromDb = await adviserStripeService.deleteStripeAccount(
    req.user._id
  );
  return res.json({ data: deleteAccount });
});

const createAdviserConnectedAccount = catchAsync(async (req, res) => {
  const adviser = req.user;

  // Check if the adviser already has a Stripe account
  const accountExists = await adviserStripeService.getOne({
    adviser: req.user._id,
  });

  if (accountExists) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Account already exists");
  }

  // Create a new Stripe Connected Account
  const stripeAdviser = await stripeService.createStripeAccount(adviser);

  // Generate the Stripe onboarding link
  const accountOnboardingLink = await stripeService.createStripeAccountLink(
    stripeAdviser.id
  );

  // Save the Stripe account details in the database
  const adviserStripeDetails = await adviserStripeService.createStripeAccount(
    adviser._id,
    stripeAdviser.id
  );

  // Send response back to the frontend
  return res.status(201).json({
    success: true,
    message: "Stripe account created successfully",
    stripeAccountId: stripeAdviser.id,
    onboardingUrl: accountOnboardingLink.url, // Send onboarding link to frontend
  });
});

const checkAdviserOnboardingStatus = catchAsync(async (req, res) => {
  // Fetch adviser details from the database
  const adviser = await adviserStripeService.getOne({
    adviser: req.user._id,
  });
  if (!adviser) {
    return res.status(httpStatus.NOT_FOUND).json({
      message: "User not onboarded at all",
    });
  }

  // Retrieve Stripe Account Details
  const stripeAccount = await stripeService.checkOnboardStatus(
    adviser.stripeAccountId
  );
  const {
    payouts_enabled,
    charges_enabled,
    details_submitted,
    requirements,
    external_accounts,
    individual,
  } = stripeAccount;

  // Check if external account exists
  const hasExternalAccount =
    external_accounts?.total_count && external_accounts.total_count > 0;

  // Extract missing requirements
  const missingRequirements = {
    identityVerificationFailed:
      individual?.verification?.status === "unverified"
        ? individual.verification.details_code
        : null,
    missingExternalAccount: !hasExternalAccount,
    missingVerificationDocument: requirements?.eventually_due?.includes(
      "individual.verification.document"
    ),
  };

  // Determine if onboarding is complete
  const hasCompletedOnboarding =
    payouts_enabled === true &&
    charges_enabled === true &&
    details_submitted === true &&
    !missingRequirements.identityVerificationFailed &&
    !missingRequirements.missingExternalAccount &&
    !missingRequirements.missingVerificationDocument;

  // Update onboarding status in DB
  await adviserStripeService.update(
    { adviser: req.user._id },
    { hasCompletedOnboarding }
  );

  // Build response message
  let message = "Account onboarding completed successfully.";

  if (missingRequirements.identityVerificationFailed) {
    message = "Identity verification failed. Please upload a valid ID.";
  } else if (missingRequirements.missingExternalAccount) {
    message = "Please link a bank account to receive payouts.";
  } else if (missingRequirements.missingVerificationDocument) {
    message = "Upload your ID document to complete verification.";
  }

  return res.json({
    message,
    onboardingStatus: {
      payouts_enabled,
      charges_enabled,
      details_submitted,
      missingRequirements,
      hasCompletedOnboarding,
    },
  });
});

const createAccountOnboardingLink = catchAsync(async (req, res) => {
  const adviser = await adviserStripeService.getOne({
    adviser: req.user._id,
  });

  const accountOnboardingLink = await stripeService.createStripeAccountLink(
    adviser.stripeAccountId
  );
  return res.status(201).json({
    success: true,
    message: "please complete the onboarding",
    stripeAccountId: adviser.stripeAccountId,
    onboardingUrl: accountOnboardingLink.url, // Send onboarding link to frontend
  });
});

const processDirectPayment = catchAsync(async (req, res) => {
  try {
    let { cardId } = req.body;
    const appointmentId = req.params.id;
    const appointment = await appointmentService.getAppointmentDetails(
      appointmentId,
      ["adviser", "user"]
    );
    if (
      !appointment ||
      (appointment.paymentStatus !== "processing" &&
        appointment.paymentStatus !== "failed")
    ) {
      throw new ApiError(
        httpStatus.BAD_REQUEST,
        "Invalid or already paid appointment"
      );
    }

    const stripeUser = await userStripeService.getOne({ user: req.user._id });

    if (!stripeUser) {
      throw new ApiError(httpStatus.NOT_FOUND, "Payment details not found");
    }

    // const sessionProduct =
    //   await singleSessionProductService.getSessionByDuration(
    //     appointment.duration
    //   );

    const sessionPrice = appointment.amount;

    if (!sessionPrice) {
      throw new ApiError(httpStatus.BAD_REQUEST, "Cant process payment");
    }
    // if no card the use the default one
    cardId = cardId; // this is for declining payment in test mode || stripeUser.stripeCardId[0] || "pm_card_chargeCustomerFail";

    const paymentResult = await stripeService.bookSingleSession(
      sessionPrice,
      appointment._id,
      stripeUser.stripeCustomerId,
      cardId
    );

    if (!paymentResult.status) {
      console.error("❌ Direct Payment Failed:", paymentResult.message);
      appointment.paymentStatus = "failed";
      appointment.paymentIntent = paymentResult?.paymentIntentId || null;
      await appointment.save();
      throw new ApiError(
        httpStatus.PAYMENT_REQUIRED,
        `${paymentResult.message}`
      );
    }

    appointment.paymentStatus = "paid";
    appointment.paymentIntent = paymentResult.paymentIntentId;
    await appointment.save();

    return res.status(201).json({
      success: true,
      message: "payment completed",
      paymentIntentId: paymentResult.paymentIntentId,
    });
  } catch (error) {
    console.error("❌ Direct Payment Failed:", error.message);
    throw new ApiError(
      httpStatus.INTERNAL_SERVER_ERROR,
      `Payment failed: ${error.message}`
    );
  }
});

const createSubscription = catchAsync(async (req, res) => {
  const { productId, cardId } = req.body;

  // make the card default before buying the subscription

  if (!productId) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      "Missing subscription details. Please provide both productId and cardId."
    );
  }

  // ✅ Check if user already has an active subscription
  const existingSubscription =
    await customerSubscriptionService.getCustomerSubscription({
      user: req.user._id,
      isActive: true,
      status: "active",
      endDate: { $gte: new Date() },
    });

  if (existingSubscription) {
    throw new ApiError(
      httpStatus.CONFLICT,
      "You already have an active subscription."
    );
  }

  // ✅ Fetch product details
  const subscriptionProductDetails =
    await subscriptionProductService.getSubscriptionProductById(productId);

  if (
    !subscriptionProductDetails ||
    !subscriptionProductDetails.stripePriceId
  ) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Invalid product ID provided.");
  }

  // ✅ Get stripe customer for this user
  const stripeUser = await userStripeService.getOne({ user: req.user._id });
  if (!stripeUser?.stripeCustomerId) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      "Stripe customer not found for this user."
    );
  }

  //make the card default before creating subscription
  if (cardId) {
    const makeCardDefault = await stripeService.makeCardDefault(
      stripeUser.stripeCustomerId,
      cardId
    );
  }

  // ✅ Create subscription on Stripe
  const subscription = await stripeService.createCustomerSubscription({
    stripeCustomerId: stripeUser.stripeCustomerId,
    priceId: subscriptionProductDetails.stripePriceId,
    sourceCardId: cardId, // use the default card or make the card default before continuing
    metadata: {
      userId: req.user._id.toString(),
      subscriptionProductId: productId.toString(),
    },
  });

  if (!subscription?.id) {
    throw new ApiError(
      httpStatus.PAYMENT_REQUIRED,
      "Failed to create subscription with Stripe."
    );
  }

  // ✅ Extract subscription data

  const startDate = new Date(subscription.current_period_start * 1000);
  const endDate = new Date(subscription.current_period_end * 1000);

  const latestInvoice = subscription.latest_invoice;
  const paymentStatus = latestInvoice?.payment_intent?.status || "unknown";
  const paymentIntent = latestInvoice?.payment_intent?.id || null;
  const amount = subscriptionProductDetails.price;
  const status = subscription.status;

  // trigger event to schedule subscription expiry and increase subscriber count

  //moved to webhook
  if (paymentStatus !== "succeeded") {
    // await stripeService.cancelSubscription(subscription.id); // optional cleanup
    throw new ApiError(httpStatus.BAD_REQUEST, "Subscription payment failed");
  }

  // ✅ Save subscription record in DB
  const userSubscription =
    await customerSubscriptionService.createCustomerSubscription(
      req.user._id,
      new mongoose.Types.ObjectId(subscriptionProductDetails._id),
      subscription.id,
      startDate,
      endDate,
      paymentStatus,
      paymentIntent,
      true,
      subscriptionProductDetails.maxSessions,
      amount,
      status
    );

  // ✅ Send response
  return res.status(httpStatus.CREATED).json({
    message: "Subscription successfully created.",
    data: {
      stripeSubscription: subscription,
      dbRecord: userSubscription,
    },
  });
});

const cancelSubscription = catchAsync(async (req, res) => {
  // ✅ Check if user has an active subscription

  const existingSubscription =
    await customerSubscriptionService.getCustomerSubscription({
      user: req.user._id,
      isActive: true,
      endDate: { $gte: new Date() },
    });

  if (!existingSubscription) {
    throw new ApiError(
      httpStatus.NOT_FOUND,
      "No active subscription found to cancel."
    );
  }

  const stripeCancellation = await stripeService.cancelSubscription(
    existingSubscription.stripeSubscriptionId
  );

  // ✅ Update local DB subscription status
  const updateSubscription =
    await customerSubscriptionService.updateCustomerSubscription(
      existingSubscription._id,
      {
        isActive: false,
        status: "cancelled",
      }
    );

  // ✅ Decrease subscriber count

  await subscriptionProductService.decreaseSubscriberCount(
    existingSubscription.subscriptionProduct
  );

  return res.status(httpStatus.OK).json({
    message: "Subscription has been cancelled",
    data: {
      stripeSubscription: stripeCancellation,
      dbRecordUpdated: true,
    },
  });
});

const refundPayment = catchAsync(async (req, res) => {
  const { amount, bookingId, reason } = req.body;
  if (!reason) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Reason is required");
  }

  if (!bookingId) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Booking ID is required");
  }
  if (amount && amount <= 0) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Invalid refund amount");
  }
  const appointments = await appointmentService.findAppointment({
    bookingId: bookingId,
  });

  const appointment = appointments[0];

  if (!appointment) {
    throw new ApiError(httpStatus.NOT_FOUND, "Appointment not found");
  }

  const alreadyRefunded = await refundService.getOne({
    appointmentId: appointment._id,
  });

  if (alreadyRefunded) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Payment already refunded");
  }

  const isRefundable =
    appointment.mode === "payment" &&
    appointment.status === "completed" &&
    appointment.paymentStatus === "paid" &&
    appointment.paymentIntent;

  if (!isRefundable) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      "Appointment is not eligible for refund"
    );
  }

  // 🚀 Attempt refund

  const refundResult = await stripeService.refundPayment(
    appointment.paymentIntent,
    appointment._id.toString(),
    amount ? amount * 100 : null
  );

  if (!refundResult.success) {
    if (refundResult.code === "charge_already_refunded") {
      throw new ApiError(
        httpStatus.BAD_REQUEST,
        "This charge has already been refunded."
      );
    }

    // Default or unknown Stripe error
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      refundResult.message || "Refund failed. Please try again later."
    );
  }

  // can add refund reason

  const refund = await refundService.createRefund({
    appointmentId: appointment._id,
    paymentIntentId: appointment.paymentIntent,
    refundId: refundResult.refundId,
    amount: amount,
    isIntiatedByAdmin: true,
    reason: reason,
  });

  return res.status(httpStatus.OK).json({
    message: "Payment refunded successfully",
    data: {
      refundId: refundResult.refundId,
      appointmentId: appointment._id,
    },
  });
});

const createCustomerAccount = catchAsync(async (req, res) => {
  const user = await userStripeService.getOne({ user: req.user._id });
  if (user) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Account already exists");
  }
  const stripeCustomer = await stripeService.createCustomer({
    email: req.user.email,
    name: req.user.name,
    phone: req.user.phone,
  });
  const customer = await userStripeService.createStripeCustomer(
    stripeCustomer.id,
    req.user._id
  );
  return res.json({ data: customer });
});

const createCheckoutSession = catchAsync(async (req, res) => {
  const { appointmentId } = req.params;
  // const { successUrl, cancelUrl } = req.body;

  // // Validate required parameters
  // if (!successUrl || !cancelUrl) {
  //   throw new ApiError(
  //     httpStatus.BAD_REQUEST,
  //     "Missing required parameters. Please provide successUrl and cancelUrl."
  //   );
  // }

  // Get appointment details
  const appointment = await appointmentService.getAppointmentDetails(
    appointmentId,
    ["adviser", "user"]
  );

  if (!appointment) {
    throw new ApiError(httpStatus.NOT_FOUND, "Appointment not found");
  }
  if (appointment.mode !== "payment") {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      "Subscription appointments cannot be paid for directly"
    );
  }
  if (appointment.paymentStatus === "paid") {
    throw new ApiError(httpStatus.BAD_REQUEST, "Appointment is already paid");
  }

  if (appointment.user._id.toString() !== req.user._id.toString()) {
    throw new ApiError(
      httpStatus.FORBIDDEN,
      "You can only pay for your own appointments"
    );
  }

  // Get the user's stripe customer ID
  const stripeUser = await userStripeService.getOne({ user: req.user._id });
  if (!stripeUser?.stripeCustomerId) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      "Stripe customer not found for this user."
    );
  }

  // Get the amount from the appointment
  const amount = appointment.amount;
  if (!amount) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Appointment amount is not set");
  }

  // Create checkout session
  const session = await stripeService.createCheckoutSession({
    customerId: stripeUser.stripeCustomerId,
    amount: amount,
    appointmentId: appointmentId,

    metadata: {
      userId: req.user._id.toString(),
      appointmentId: appointmentId.toString(),
      adviserId: appointment.adviser._id.toString(),
    },
  });

  if (!session?.id) {
    throw new ApiError(
      httpStatus.INTERNAL_SERVER_ERROR,
      "Failed to create checkout session with Stripe."
    );
  }

  // Update appointment payment status to processing
  await appointmentService.updateAppointment(appointmentId, {
    paymentStatus: "processing",
  });

  return res.status(httpStatus.CREATED).json({
    message: "Checkout session created successfully.",
    data: {
      sessionId: session.id,
      url: session.url,
    },
  });
});

//to pay for free appointments
const payForFreeSession = catchAsync(async (req, res) => {
  const { appointmentId } = req.params;
  const { amount } = req.body;

  const isAlreadyPaid = await payoutService.getOnePayout({
    appointmentId: appointmentId,
    status: "completed",
  });

  if (isAlreadyPaid) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Payment already done");
  }

  const appointment = await appointmentService.findOneAppointment({
    _id: appointmentId,
  });
  if (!appointment) {
    throw new ApiError(httpStatus.NOT_FOUND, "Appointment not found");
  }

  if (appointment.paymentStatus !== "free") {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      "Appointment is not in free status"
    );
  }

  if (appointment.status !== "completed") {
    throw new ApiError(httpStatus.BAD_REQUEST, "Appointment is not completed");
  }
  const adviserStripeDetails = await adviserStripeService.getOne({
    adviser: appointment.adviser._id,
  });
  const transferMoney = await stripeService.transferMoney({
    adviserId: appointment.adviser._id.toString(),
    stripeAccountId: adviserStripeDetails.stripeAccountId,
    amount: Number(amount),
    appointmentId: appointment._id.toString(),
  });

  // const transferRecord = await payoutService.createPayout({
  //   adviserId: appointment.adviser._id,
  //   appointmentId: appointment._id,
  //   amount: amount,
  //   stripeTransferId: transferMoney.id,
  //   status: "initiated",
  //   createdAt: new Date(),
  // });

  // console.log("✅ Adviser payout recorded.", {
  //   payout: transferRecord,
  // });

  return res.status(httpStatus.OK).json({
    success: true,
    message: "Payment for the free session was processed successfully.",
    payout: transferMoney,
  });
});

const expressDashboardLink = catchAsync(async (req, res) => {
  const userId = req.user._id;
  const adviserStripe = await adviserStripeService.getOne({ adviser: userId });
  if (!adviserStripe)
    throw new ApiError(httpStatus.NOT_FOUND, "No stripe account found");

  const dashboardLink = await stripeService.getExpressDashboardLink(
    adviserStripe.stripeAccountId
  );
  return res.status(httpStatus.OK).json({
    message: "Link generated",
    data: {
      status: dashboardLink.status,
      dashboardLink: dashboardLink.url,
    },
  });
});

const handleStripeEvent = catchAsync(async (req, res) => {
  const sig = req.headers["stripe-signature"];

  try {
    const event = stripeService.constructEvent(
      req.rawBody,
      sig,
      config.stripe.webhookSecret
    );

    // console.log(
    //   `🔔 Stripe Event Received: [${event.type}] (id: ${event.id}) at ${new Date().toISOString()}`
    // );

    if (event.type === "customer.subscription.deleted") {
      // console.log("❌ Handling subscription deletion...");

      const userSubscription =
        await customerSubscriptionService.getCustomerSubscription({
          stripeSubscriptionId: event.data.object.id,
        });

      if (userSubscription) {
        userSubscription.isActive = false;
        userSubscription.status = "cancelled";
        await userSubscription.save();

        // console.log("✅ Subscription marked as cancelled.");
      }
      // console.log("✅ Subscription not found");

      return res.status(200).send({ status: true });
    } else if (event.type === "invoice.payment_failed") {
      const stripeSubscriptionId = event.data.object.subscription;

      const userSubscription =
        await customerSubscriptionService.getCustomerSubscription({
          stripeSubscriptionId,
        });

      if (userSubscription) {
        await customerSubscriptionService.updateCustomerSubscription(
          userSubscription._id,
          {
            isActive: false,
            paymentStatus: "failed",
            status: "cancelled",
          }
        );

        eventEmitter.emit(
          emitterEventNames.SUBSCRIPTION_PAYMENT_FAILED,
          userSubscription
        );

        // console.log("📉 Subscription marked as past_due and event emitted.");
      }

      return res.status(200).send({ status: true });
    } else if (event.type === "invoice.payment_succeeded") {
      // console.log("💰 Handling subscription invoice payment...");

      const invoice = event.data.object;
      const stripeSubscriptionId = invoice.subscription;

      if (!stripeSubscriptionId) {
        // console.log("Not a subscription invoice, skipping.");
        return;
      }

      const userSubscription =
        await customerSubscriptionService.getCustomerSubscription({
          stripeSubscriptionId,
        });

      if (!userSubscription) {
        console.warn("⚠️ Subscription not found for invoice.");
        return;
      }

      try {
        const chargeId = invoice.charge;
        const charge = await stripeService.retrieveCharge(chargeId);
        const balanceTx = await stripeService.balanceTransaction(
          charge.balance_transaction
        );

        const stripeFee = balanceTx.fee / 100;
        const netAmount = balanceTx.net.toFixed(2) / 100;
        const totalAmount = balanceTx.amount / 100;
        const invoiceUrl = invoice.invoice_pdf || null;

        await customerSubscriptionService.updateCustomerSubscription(
          userSubscription._id,
          {
            paymentStatus: "succeeded",
          }
        );

        await revenueService.createRevenue({
          subscriptionId: userSubscription._id,
          sourceType: "subscription",
          status: "collected",
          totalAmount,
          stripeFee,
          netAmount,
          user: userSubscription.user.name,
          userId: userSubscription.user._id,
          invoiceUrl,
        });

        // console.log(
        //   "✅ Revenue created and subscription payment status updated. for renewal"
        // );
      } catch (error) {
        console.error(`❌ Error processing invoice payment: ${error.message}`);
      }
    } else if (event.type === "charge.succeeded") {
      // console.log("💸 Handling successful charge...");

      const {
        payment_intent,
        balance_transaction,
        description,
        receipt_url: invoiceUrl,
        metadata, // Capture metadata
      } = event.data.object;

      // Ensure the appointment ID is present in metadata
      if (!metadata || !metadata.appointmentId) {
        // console.log("❌ No appointment ID found in metadata. Ignoring event.");
        return res
          .status(400)
          .send({ status: false, message: "No appointment ID" });
      }

      const appointmentId = metadata.appointmentId;

      if (description && description.toLowerCase().includes("subscription")) {
        // console.log("⚠️ Ignoring charge.succeeded for subscription payment");
        return res.status(200).send({ status: true });
      }

      // console.log("charge succeeded moving forward");

      // Check if the appointment exists using the appointment ID from metadata
      const appointment = await appointmentService.findOneAppointment({
        _id: appointmentId,
      });

      if (!appointment) {
        // console.log("❌ Appointment not found for ID:", appointmentId);
        return res
          .status(400)
          .send({ status: false, message: "Appointment not found" });
      }

      if (appointment.paymentStatus !== "paid") {
        appointment.paymentStatus = "paid";
        appointment.paymentIntent = payment_intent;
        await appointment.save();

        // console.log(
        //   `🔔 Triggering payment success event for appointment ${appointment._id}`
        // );
      }

      // console.log(
      //   "✅ Appointment payment captured via charge.succeeded webhook"
      // );

      const balanceTransaction =
        await stripeService.balanceTransaction(balance_transaction);

      const stripeFee = balanceTransaction.fee / 100;
      const netAmount = balanceTransaction.net.toFixed(2) / 100;

      // console.log(
      //   `💰 Actual Stripe Fee: $${stripeFee.toFixed(2)} | Net Amount: $${netAmount.toFixed(2)}`
      // );

      const commissionPercentage = appointment.commissionPercentage;
      const adviserPayoutPercentage = 100 - Number(commissionPercentage);
      const sessionPrice = appointment.amount;
      const adviserPayoutAmount =
        (adviserPayoutPercentage / 100) * sessionPrice;
      const feeId = generateFeeId();

      // Check if the revenue record already exists for this appointment
      const existingRevenue = await revenueService.getRevenue({
        appointmentId: appointment._id,
      });

      if (existingRevenue) {
        // console.log(
        //   "⚠️ Revenue record already exists for appointment:",
        //   appointment._id
        // );
        return res.status(200).send({ status: true });
      }

      // Create new revenue record
      const revenueRecord = await revenueService.createRevenue({
        appointmentId: appointment._id,
        adviserFee: adviserPayoutAmount,
        totalAmount: appointment.amount,
        stripeFee: stripeFee,
        netAmount: netAmount,
        commissionPercentage,
        feeId,
        status: "collected",
        createdAt: new Date(),
        adviser: appointment.adviser.name,
        adviserId: appointment.adviser._id,
        user: appointment.user.name,
        userId: appointment.user._id,
        sourceType: "appointment",
        invoiceUrl,
      });

      eventEmitter.emit(emitterEventNames.PAYMENT_SUCCEEDED, {
        bookingId: appointment.bookingId,
        amount: appointment.amount,
        userId: appointment.user._id,
        adviserId: appointment.adviser._id,
        appointmentId: appointment._id,
        user: {
          name: appointment.user.name,
          email: appointment.user.email,
        },
        adviser: {
          name: appointment.adviser.name,
        },
      });

      // Ensure we have all required data before emitting the event
      if (appointment && appointment.user && appointment.adviser) {
        // console.log(
        //   `🔔 Emitting appointment notification event for appointment ${appointment._id}`
        // );
        eventEmitter.emit(emitterEventNames.APPOINTMENT_NOTIFICATIONS, {
          user: appointment.user,
          adviser: appointment.adviser,
          appointment: appointment,
          timezone: appDefaults.TIMEZONE,
        });
      } else {
        console.error(
          `❌ Missing required data for appointment notification: ${JSON.stringify(
            {
              hasAppointment: !!appointment,
              hasUser: !!(appointment && appointment.user),
              hasAdviser: !!(appointment && appointment.adviser),
            }
          )}`
        );
      }

      // console.log("💰 Revenue record created:", revenueRecord);
    } else if (event.type === "charge.failed") {
      // console.log("❌ Handling failed charge...");

      const charge = event.data.object;
      const { failure_code, failure_message } = charge;
      const { appointmentId } = charge.metadata || {};

      console.error("❌ Charge failed:", failure_code, failure_message);

      const appointment = await appointmentService.findOneAppointment({
        _id: appointmentId,
      });

      if (appointment) {
        await appointmentService.deleteAppointment(appointmentId);

        // console.log(
        //   `🔔 Triggering payment failed event for appointment ${appointment._id}`
        // );

        eventEmitter.emit(emitterEventNames.PAYMENT_FAILED, {
          bookingId: appointment.bookingId,
          amount: appointment.amount,
          userId: appointment.user._id,
          adviserId: appointment.adviser._id,
          appointmentId: appointment._id,
          user: {
            name: appointment.user.name,
            email: appointment.user.email,
          },
          adviser: {
            name: appointment.adviser.name,
          },
        });
      }
    } else if (event.type === "customer.subscription.updated") {
      // console.log("🔁 Handling subscription update...");

      const subscription = event.data.object;
      const {
        id: stripeSubscriptionId,
        status,
        current_period_start,
        current_period_end,
      } = subscription;

      const userSubscription =
        await customerSubscriptionService.getCustomerSubscription({
          stripeSubscriptionId,
        });

      if (userSubscription) {
        if (status === "active") {
          const isActive = true;

          await customerSubscriptionService.updateCustomerSubscription(
            userSubscription._id,
            {
              isActive,
              remainingSessions: 1,
              startDate: new Date(current_period_start * 1000),
              endDate: new Date(current_period_end * 1000),
              renewed: true,
            }
          );

          // console.log("✅ Subscription renewed. Dates and sessions updated.");
          // goes complete user
          eventEmitter.emit(
            emitterEventNames.SUBSCRIPTION_RENEWED,
            userSubscription,
            new Date(current_period_end * 1000)
          );
        } else {
          const isActive = false;

          await customerSubscriptionService.updateCustomerSubscription(
            userSubscription._id,
            {
              isActive,
              remainingSessions: 0,
            }
          );
          // console.log(
          //   `ℹ️ Subscription status is '${status}', deactivated subscription.`
          // );
        }
      } else {
        console.warn("⚠️ Subscription not found for ID:", stripeSubscriptionId);
      }
    } else if (event.type === "customer.subscription.created") {
      // console.log("🆕 Handling subscription creation...");

      const subscription = event.data.object;
      const { id: stripeSubscriptionId, status } = subscription;

      const userSubscription =
        await customerSubscriptionService.getCustomerSubscription({
          stripeSubscriptionId,
        });

      if (userSubscription) {
        const isActive = status === "active";

        await customerSubscriptionService.updateCustomerSubscription(
          userSubscription._id,
          {
            isActive,
            stripeSubscriptionId,
            remainingSessions: isActive ? 1 : 0,
          }
        );

        // console.log("✅ Subscription record created/updated.");
        if (status === "active") {
          eventEmitter.emit(
            emitterEventNames.SUBSCRIPTION_CREATED,
            userSubscription
          );
        }
      } else {
        console.warn("⚠️ No subscription record found in DB.");
      }
    } else if (event.type === "refund.created") {
      const refund = event.data.object;
      const appointmentId = refund.metadata?.appointmentId;

      if (!appointmentId) return res.status(200).send({ status: false });

      const refundData = await Refund.findOne({ appointmentId });

      if (refundData.status !== "succeeded") {
        refundData.status = "initiated";
        refundData.updatedAt = new Date();
        await refundData.save();

        const appointment = await appointmentService.findOneAppointment({
          _id: appointmentId,
        });

        eventEmitter.emit(emitterEventNames.REFUND_INITIATED, {
          amount: refund.amount / 100,
          bookingId: appointment.bookingId,
          userId: appointment.user._id,
        });
      }
    } else if (event.type === "charge.refunded") {
      const refund = event.data.object;
      const appointmentId = refund.metadata?.appointmentId;

      if (!appointmentId) return res.status(200).send({ status: false });

      const refundData = await Refund.findOne({ appointmentId });

      if (refundData) {
        refundData.status = "succeeded";
        refundData.updatedAt = new Date();
        await refundData.save();

        // console.log("✅ Refund marked as succeeded.");
      }

      const appointment = await appointmentService.findOneAppointment({
        _id: appointmentId,
      });

      eventEmitter.emit(emitterEventNames.REFUND_SUCCEEDED, {
        amount: refund.amount_refunded / 100,
        bookingId: appointment.bookingId,
        userId: appointment.user._id,
      });
    } else if (event.type === "transfer.created") {
      // console.log("💰 Handling transfer creation...");

      const transfer = event.data.object;
      const { id: stripeTransferId, metadata, amount } = transfer;
      const { appointmentId, adviser } = metadata || {};

      if (!appointmentId || !adviser) {
        console.warn(
          `⚠️ Transfer ${stripeTransferId} has no appointmentId or adviserId in metadata`
        );
        return;
      }

      const payoutAmount = amount / 100;

      // Emit event for transfer creation
      eventEmitter.emit(emitterEventNames.STRIPE_TRANSFER_CREATED, {
        stripeTransferId,
        adviserId: adviser,
        appointmentId,
        amount: payoutAmount,
      });

      // console.log(
      //   `✅ Transfer creation event emitted for transfer ${stripeTransferId}`
      // );
    } else if (event.type === "transfer.reversed") {
      // console.log("❌ Handling transfer reversal...");

      const transfer = event.data.object;
      const { id: transferId, metadata } = transfer;
      const { appointmentId } = metadata || {};

      if (!appointmentId) {
        console.warn(
          `⚠️ Transfer ${transferId} has no appointmentId in metadata`
        );
        return;
      }

      const payoutRecord = await payoutService.getPayoutByStripeId(transferId);

      if (payoutRecord) {
        await payoutService.updatePayout(payoutRecord._id, {
          status: "failed",
          updatedAt: new Date(),
        });

        // console.log(`❌ Transfer marked as reversed: ${transferId}`);
      } else {
        console.warn(
          `⚠️ No matching payout record found for transfer ID: ${transferId}`
        );
      }
    } else if (event.type === "transfer.failed") {
      // console.log("❌ Handling transfer failure...");

      const transfer = event.data.object;
      const { id: transferId, metadata } = transfer;
      const { appointmentId, adviser } = metadata || {};

      if (!transferId) {
        console.warn("⚠️ Transfer ID is missing");
        return;
      }

      const payoutRecord = await payoutService.getPayoutByStripeId(transferId);

      if (payoutRecord) {
        // Update payout status to failed
        await payoutService.updatePayout(payoutRecord._id, {
          status: "failed",
          updatedAt: new Date(),
        });

        // Emit the transfer failed event
        eventEmitter.emit(emitterEventNames.TRANSFER_FAILED, {
          adviserId: payoutRecord.adviserId,
          amount: payoutRecord.amount,
          payoutId: payoutRecord._id,
          appointmentId: payoutRecord.appointmentId,
        });

        // console.log(`❌ Transfer failure processed: ${transferId}`);
      } else {
        console.warn(
          `⚠️ No matching payout record found for transfer ID: ${transferId}`
        );
      }
    } else if (event.type === "checkout.session.completed") {
      // console.log("✅ Handling checkout session completion...");

      const session = event.data.object;
      const { metadata, payment_intent, customer } = session;
      const { appointmentId } = metadata || {};

      if (!appointmentId) {
        console.warn("⚠️ No appointmentId found in checkout session metadata");
        return res.status(200).send({ status: true });
      }

      // console.log(
      //   `🔍 Processing checkout session for appointment ${appointmentId}`
      // );

      const appointment = await appointmentService.findOneAppointment({
        _id: appointmentId,
      });

      if (!appointment) {
        console.warn(`⚠️ Appointment not found: ${appointmentId}`);
        return res.status(200).send({ status: true });
      }

      // Update appointment payment status
      appointment.paymentStatus = "paid";
      appointment.paymentIntent = payment_intent;
      await appointment.save();

      // console.log(`✅ Appointment ${appointmentId} marked as paid`);

      // Get payment details for revenue record
      const paymentIntent =
        await stripeService.retrievePaymentIntent(payment_intent);
      const charge = paymentIntent.latest_charge;
      const balanceTransaction = await stripeService.balanceTransaction(
        charge
          ? (await stripeService.retrieveCharge(charge)).balance_transaction
          : null
      );

      const stripeFee = balanceTransaction ? balanceTransaction.fee / 100 : 0;
      const netAmount = balanceTransaction
        ? balanceTransaction.net / 100
        : appointment.amount;

      // Create revenue record
      const commissionPercentage =
        appointment.commissionPercentage || appDefaults.COMMISSION_PERCENTAGE;
      const adviserPayoutPercentage = 100 - Number(commissionPercentage);
      const sessionPrice = appointment.amount;
      const adviserPayoutAmount =
        (adviserPayoutPercentage / 100) * sessionPrice;
      const feeId = generateFeeId();

      await revenueService.createRevenue({
        appointmentId: appointment._id,
        adviserId: appointment.adviser._id,
        adviserFee: adviserPayoutAmount,
        totalAmount: appointment.amount,
        stripeFee: stripeFee,
        netAmount: netAmount,
        commissionPercentage,
        feeId,
        status: "collected",
        createdAt: new Date(),
        adviser: appointment.adviser.name,
        adviserId: appointment.adviser._id,
        user: appointment.user.name,
        userId: appointment.user._id,
        sourceType: "appointment",
      });

      // Emit payment success event
      eventEmitter.emit(emitterEventNames.PAYMENT_SUCCEEDED, {
        bookingId: appointment.bookingId,
        amount: appointment.amount,
        userId: appointment.user._id,
        adviserId: appointment.adviser._id,
        appointmentId: appointment._id,
        user: {
          name: appointment.user.name,
          email: appointment.user.email,
        },
        adviser: {
          name: appointment.adviser.name,
        },
      });

      // Emit appointment notification event
      eventEmitter.emit(emitterEventNames.APPOINTMENT_NOTIFICATIONS, {
        user: appointment.user,
        adviser: appointment.adviser,
        appointment: appointment,
        timezone: appDefaults.TIMEZONE,
      });

      // console.log(
      //   `✅ Checkout session processing completed for appointment ${appointmentId}`
      // );
    } else if (event.type === "refund.failed") {
      // console.log("❌ Handling refund failure...");

      const refund = event.data.object;
      const { id: refundId, payment_intent, metadata } = refund;
      const appointmentId = metadata?.appointmentId;

      // console.log(`❌ Refund failed: ${appointmentId}`);

      if (!appointmentId) return res.status(200).send({ status: false });

      const refundData = await Refund.findOne({
        appointmentId,
        refundId: refundId,
      });

      if (refundData) {
        refundData.status = "failed";
        refundData.updatedAt = new Date();
        await refundData.save();

        // console.log("❌ Refund marked as failed.");
      }

      const appointment = await appointmentService.findOneAppointment({
        _id: appointmentId,
      });

      if (appointment && appointment.user) {
        eventEmitter.emit(emitterEventNames.REFUND_FAILED, {
          amount: refund.amount / 100,
          bookingId: appointment.bookingId,
          userId: appointment.user._id,
          appointmentId: appointmentId,
          paymentIntentId: payment_intent,
          reason: refund.failure_reason || "Unknown reason",
        });
      } else {
        console.warn(
          `⚠️ Could not find appointment for refund: ${appointmentId}`
        );
      }
    }

    return res.status(200).send({ status: true });
  } catch (err) {
    // console.log("❌ Stripe Webhook Error:", err);
    res.status(400).send(`Webhook Error: ${err.message}`);
  }
});

const processStripeConnectWebhook = catchAsync(async (req, res) => {
  const sig = req.headers["stripe-signature"];

  try {
    // console.log("Processing Stripe Connect webhook...");

    // Construct the event using the raw body and signature
    const stripeEvent = stripeService.constructEvent(
      req.rawBody,
      sig,
      config.stripe.connectWebhookSecret
    );

    console.log(`Stripe Connect event received: ${stripeEvent.type}`);

    switch (stripeEvent.type) {
      case "account.updated":
        const payload = stripeEvent.data.object;

        // Check if the account has completed onboarding
        const account = stripeEvent.data.object;

        if (
          account.details_submitted === true &&
          account.charges_enabled === true &&
          account.payouts_enabled === true &&
          account.requirements.currently_due.length === 0 &&
          account.requirements.eventually_due.length === 0
        ) {
          // console.log(`Account ${payload.id} has completed onboarding`);
          eventEmitter.emit(
            emitterEventNames.STRIPE_CONNECTED_ACCOUNT_VERIFIED,
            {
              stripeAccountId: payload.id,
            }
          );
        }
        break;

      case "payout.paid": {
        // stripeEvent.account is the connected‐account ID (acct_1RBx3nQwyadgQ7I5)
        const connectedAccountId = stripeEvent.account;

        // The actual payout lives under data.object
        const payout = stripeEvent.data.object;

        // console.log(
        //   `Payout of £${(payout.amount / 100).toFixed(2)} ${payout.currency.toUpperCase()} ` +
        //     `to bank account ${payout.destination} (on connected account ${connectedAccountId})`
        // );

        eventEmitter.emit(emitterEventNames.STRIPE_PAYOUT_PAID, {
          amount: payout.amount,
          stripeAccountId: connectedAccountId,
          status: payout.status,
          arrivalDate: new Date(payout.arrival_date * 1000),
        });
        break;
      }

      default:
      // console.log(`Unhandled event type ${stripeEvent.type}`);
    }

    res.json({ received: true });
  } catch (err) {
    console.error("❌ Stripe Connect Webhook Error:", err);
    res.status(400).send(`Webhook Error: ${err.message}`);
  }
});

module.exports = {
  getAllSavedCards,
  saveCard,
  deleteAdviserStripeConnectAccount,
  createAdviserConnectedAccount,
  checkAdviserOnboardingStatus,
  createAccountOnboardingLink,
  processDirectPayment,
  createSubscription,
  cancelSubscription,
  handleStripeEvent,
  makeCardDefault,
  changeDefaultCard,
  refundPayment,
  processStripeConnectWebhook,
  payForFreeSession,
  createCustomerAccount,
  createCheckoutSession,
  expressDashboardLink,
};
