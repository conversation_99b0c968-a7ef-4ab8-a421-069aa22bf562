const { UserAppFeedback } = require("../models");

async function addReview(feedback) {
  return await UserAppFeedback.create(feedback);
}

async function getAllReviews(filters = {}, options = {}) {
  const query = { ...filters };

  if (filters.rating) {
    const minRating = parseFloat(filters.rating);
    if (!isNaN(minRating)) {
      query.rating = { $gte: minRating };
    }
  }
  const reviews = await UserAppFeedback.paginate(query, options);
  return reviews;
}

async function getReviewByUser(userId) {
  return await UserAppFeedback.findOne({ user: userId });
}

module.exports = {
  addReview,
  getAllReviews,
  getReviewByUser,
};
