const crypto = require("crypto");
const { v4: uuidv4 } = require("uuid");

const generateRandomEightDigitNumber = () => {
  return Math.floor(10000000 + Math.random() * 90000000);
};

const generateBookingId = () => {
  const uniqueId = uuidv4().split("-")[0].toUpperCase(); // Shorten UUID and make it uppercase
  return `APT-${uniqueId}`;
};

const generateFeeId = () => {
  const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
  let code = "";

  // Generate 4-character random alphanumeric string
  for (let i = 0; i < 4; i++) {
    const byte = crypto.randomBytes(1)[0];
    code += chars[byte % chars.length];
  }

  return `FEE${code}`;
};

const determineFileType = async (file) => {
  let fileType;

  // Regex to check for image MIME types
  if (file.mimetype.match(/^image\//)) {
    fileType = "image";
  }
  // Regex to check for video MIME types
  else if (file.mimetype.match(/^video\//)) {
    fileType = "video";
  }
  // Regex to check for audio MIME types
  else if (file.mimetype.match(/^audio\//)) {
    fileType = "audio";
  }
  // If it's neither image, video, nor audio, it's a generic file
  else {
    fileType = "file";
  }

  // Upload file to storage (e.g., S3)

  // Return the uploaded file URL and fileType
  return { fileType };
};

const generateStrongPassword = () => {
  // Generate a strong password (e.g., 12 characters with letters, numbers, and symbols)
  return crypto.randomBytes(16).toString("base64").slice(0, 12);
};

function capitalizeEachWord(str) {
  if (!str) return "";
  return str
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(" ");
}
module.exports = {
  generateRandomEightDigitNumber,
  generateBookingId,
  determineFileType,

  generateFeeId,
  generateStrongPassword,
  capitalizeEachWord,
};
