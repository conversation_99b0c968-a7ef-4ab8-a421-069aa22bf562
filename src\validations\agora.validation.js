const Joi = require("joi");
const { objectId } = require("./custom.validation");

/**
 * Validation schema for stopping Agora recording
 */
const stopAgoraRecording = {
  body: Joi.object().keys({
    resourceId: Joi.string().required().messages({
      "string.empty": "Resource ID is required",
      "any.required": "Resource ID is required"
    }),
    sid: Joi.string().required().messages({
      "string.empty": "SID is required",
      "any.required": "SID is required"
    }),
    channelName: Joi.string().required().messages({
      "string.empty": "Channel name is required",
      "any.required": "Channel name is required"
    }),
    appointmentId: Joi.string().custom(objectId).required().messages({
      "string.empty": "Appointment ID is required",
      "any.required": "Appointment ID is required",
      "string.pattern.base": "Appointment ID must be a valid MongoDB ObjectId"
    })
  })
};

/**
 * Validation schema for starting Agora recording
 */
const startAgoraRecording = {
  params: Joi.object().keys({
    appointmentId: Joi.string().custom(objectId).required().messages({
      "string.empty": "Appointment ID is required",
      "any.required": "Appointment ID is required",
      "string.pattern.base": "Appointment ID must be a valid MongoDB ObjectId"
    })
  })
};

/**
 * Validation schema for generating Agora token
 */
const generateAgoraToken = {
  params: Joi.object().keys({
    appointmentId: Joi.string().custom(objectId).required().messages({
      "string.empty": "Appointment ID is required",
      "any.required": "Appointment ID is required",
      "string.pattern.base": "Appointment ID must be a valid MongoDB ObjectId"
    })
  })
};

/**
 * Validation schema for setting up whiteboard session
 */
const setupWhiteboardSession = {
  params: Joi.object().keys({
    appointmentId: Joi.string().custom(objectId).required().messages({
      "string.empty": "Appointment ID is required",
      "any.required": "Appointment ID is required",
      "string.pattern.base": "Appointment ID must be a valid MongoDB ObjectId"
    })
  })
};

module.exports = {
  stopAgoraRecording,
  startAgoraRecording,
  generateAgoraToken,
  setupWhiteboardSession
};
