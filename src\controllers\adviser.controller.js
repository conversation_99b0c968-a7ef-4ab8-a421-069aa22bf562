const httpStatus = require("http-status");

const {
  adviserService,
  appointmentService,
  reviewService,
  appNotificationService,

  payoutService,
  appSettingsService,
} = require("../services/index");
const mongoose = require("mongoose");
const moment = require("moment");

const ApiError = require("../utils/ApiError");
const catchAsync = require("../utils/catchAsync");
const { getPaginateConfig } = require("../utils/queryPHandler");
const { fileUploadService } = require("../microservices");

const {
  userTypes,
  appDefaults,
  notificationCategories,
  emitterEventNames,
  mimetypeToExtension,
} = require("../constants");
const { Appointment, Adviser } = require("../models");
const eventEmitter = require("../events/eventEmitter");

const updateProfile = catchAsync(async (req, res) => {
  const userId =
    req.user.__t === userTypes.ADVISER ? req.user._id : req.params.id;
  let updateData = { ...req.body };

  let oldPicKey = null;

  // ✅ If profile picture is uploaded
  if (req.file) {
    // Upload new image to S3
    const [profilePic] = await fileUploadService
      .s3Upload([req.file], "profile_picture")
      .catch((e) => {
        throw new ApiError(
          httpStatus.INTERNAL_SERVER_ERROR,
          `Failed to upload profile picture: ${e.message}`
        );
      });

    // Fetch only current profilePic key (lean, minimal data)
    const currentPic = await Adviser.findById(userId, "profilePic").lean();

    if (currentPic?.profilePic?.key) {
      oldPicKey = currentPic.profilePic.key;
    }

    updateData.profilePic = profilePic;
  }

  // ✅ Update profile
  const updatedAdviser = await adviserService.updateAdviser(userId, updateData);
  if (!updatedAdviser) {
    throw new ApiError(httpStatus.NOT_FOUND, "Adviser not found");
  }

  // ✅ Emit cleanup event after DB update to avoid stale reference
  if (oldPicKey) {
    eventEmitter.emit(emitterEventNames.PROFILE_PIC_CLEANUP, { oldPicKey });
  }

  return res.status(httpStatus.OK).json({
    message: "Profile updated successfully.",
    data: updatedAdviser,
  });
});

const getAdviser = catchAsync(async (req, res) => {
  let id = req.params?.id || req.user._id;
  let query = { _id: id, isDeleted: false };

  if (req.user.__t === userTypes.ADMIN) {
    id = req.params?.id;
    query = { _id: id };
  }

  if (!id) {
    throw new ApiError(httpStatus.BAD_REQUEST, "No id found in params");
  }
  const adviser = await adviserService.getAdviserDetails(query);
  return res.status(httpStatus.OK).json({
    message: "adviser retrieved successfully",
    data: adviser,
  });
});

const getAllAdvisers = catchAsync(async (req, res) => {
  const { options, filters } = getPaginateConfig(req.query);

  let advisers;

  if (req.user.__t === userTypes.ADMIN) {
    advisers = await adviserService.getAdvisersWithDetails(filters, options);
  } else {
    advisers = await adviserService.getAllAdvisers(filters, options, req);
  }

  // Transform the results based on user role

  const sanitizedResults = advisers.results.map((adviser) => {
    const {
      _id,
      name,
      email,
      phone,
      specialization,
      averageRating,
      experience,
      bio,
      profilePic,
      documents,
      isDeleted,
      isBlockedByAdmin,
      isVerified,
      nextBookingDate, // Next appointment date for Admin users
    } = adviser;

    return {
      _id,
      name,
      email,
      phone,
      specialization,
      averageRating,
      experience,
      bio,
      profilePic,
      documents,
      isDeleted,
      isBlockedByAdmin,
      isVerified,
      nextBooking:
        req.user.__t === userTypes.ADMIN ? nextBookingDate : undefined, // Include nextBooking for Admin only
    };
  });

  return res.status(httpStatus.OK).json({
    message: "Advisers retrieved successfully",
    data: {
      ...advisers,
      results: sanitizedResults,
    },
  });
});

const getReviews = catchAsync(async (req, res) => {
  const id = req.params.id ? req.params.id.toString() : req.user._id;
  let { options, filters } = getPaginateConfig(req.query);
  filters = {
    ...filters,
    adviser: mongoose.Types.ObjectId.isValid(id)
      ? new mongoose.Types.ObjectId(id)
      : id,
  };

  // const adviser = await adviserService.getAdviserDetails({ _id: id });
  // if (!adviser) {
  //   throw new ApiError(httpStatus.NOT_FOUND, "Adviser not found");
  // }

  const reviews = await reviewService.getAllReviews(filters, options);

  if (!reviews || reviews.results.length < 1) {
    return res.status(httpStatus.OK).json({
      message: "No reviews found for this adviser",
      data: reviews,
    });
  }

  // Return the reviews
  return res.status(httpStatus.OK).json({
    message: "Reviews retrieved successfully",
    data: reviews,
  });
});

const updateAvailability = catchAsync(async (req, res) => {
  const adviserId = req.user._id;
  const { availability } = req.body;
  const timezone = req.headers["timezone"] || appDefaults.TIMEZONE;

  // Convert availability times to UTC before saving
  const convertedAvailability = availability.map((dayAvailability) => {
    return {
      ...dayAvailability,
      timeSlots: dayAvailability.timeSlots.map((slot) => {
        // Convert time from user's timezone to UTC
        const startDateTime = moment.tz(
          `1970-01-01T${slot.start}`,
          "YYYY-MM-DDTHH:mm:ss",
          timezone
        );
        const endDateTime = moment.tz(
          `1970-01-01T${slot.end}`,
          "YYYY-MM-DDTHH:mm:ss",
          timezone
        );

        // Convert to UTC format
        const startUTC = startDateTime.utc().format("HH:mm:ss[Z]");
        const endUTC = endDateTime.utc().format("HH:mm:ss[Z]");
        if (!startDateTime.isBefore(endDateTime)) {
          throw new ApiError(
            httpStatus.BAD_REQUEST,
            `End time ${slot.end} must be after start time ${slot.start}`
          );
        }
        return {
          ...slot,
          start: startUTC,
          end: endUTC,
        };
      }),
    };
  });

  // Update the adviser's availability in the database
  const updatedAdviser = await adviserService.updateAdviser(
    adviserId,
    { availability: convertedAvailability },
    { new: true }
  );

  if (!updatedAdviser) {
    throw new ApiError(httpStatus.NOT_FOUND, "Adviser not found");
  }

  return res.status(httpStatus.OK).json({
    message: "Availability updated successfully",
    data: updatedAdviser,
  });
});

const uploadDocuments = catchAsync(async (req, res) => {
  const userId = req.user._id;

  if (!req.files || req.files.length === 0) {
    throw new ApiError(httpStatus.BAD_REQUEST, "No documents provided");
  }

  if (req.user.isVerified) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      "Verified advisers cannot upload documents"
    );
  }

  // Get the current adviser to check for rejected documents
  const currentAdviser = await adviserService.getAdviserDetails({
    _id: userId,
  });

  if (!currentAdviser) {
    throw new ApiError(httpStatus.NOT_FOUND, "Adviser not found");
  }

  // Find rejected documents that need to be cleaned up
  const rejectedDocuments = currentAdviser.documents.filter(
    (doc) => doc.status === "rejected"
  );

  const documentCount = currentAdviser.documents.filter(
    (doc) => doc.status !== "rejected"
  );

  if (documentCount.length >= 1)
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      "cant upload more than 1 document "
    );

  // Upload new documents to S3
  let uploadedDocuments;
  try {
    uploadedDocuments = await fileUploadService.s3Upload(
      req.files,
      "documents"
    );
  } catch (e) {
    throw new ApiError(
      httpStatus.INTERNAL_SERVER_ERROR,
      `Failed to upload documents: ${e.message}`
    );
  }

  // Add file type information to uploaded documents
  const enhancedDocuments = uploadedDocuments.map((doc, index) => {
    const mimetype = req.files[index].mimetype;
    let fileType = "unknown";
    if (mimetype) {
      if (mimetypeToExtension[mimetype]) {
        fileType = mimetypeToExtension[mimetype];
      } else {
        const parts = mimetype.split("/");
        if (parts.length > 1) {
          fileType = parts[1];
        }
      }
    }
    return {
      ...doc,
      fileType: fileType,
    };
  });

  // Remove rejected documents from the user's document list
  const updatedDocuments = currentAdviser.documents.filter(
    (doc) => doc.status !== "rejected"
  );

  // Add the newly uploaded documents
  updatedDocuments.push(...enhancedDocuments);

  // Update the adviser with the new document list
  const updatedUser = await adviserService.updateAdviser(
    userId,
    { documents: updatedDocuments },
    { new: true }
  );

  // ✅ Emit event to clean up rejected documents asynchronously
  if (rejectedDocuments.length > 0) {
    eventEmitter.emit(emitterEventNames.DOCUMENT_CLEANUP, {
      rejectedDocuments,
    });
  }

  return res.status(httpStatus.OK).send({
    message: "Documents uploaded successfully",
    documents: updatedUser,
  });
});

const getDocuments = catchAsync(async (req, res) => {
  let userId = req.user._id;
  if (req.user.__t === userTypes.ADMIN) {
    userId = req.params.id;
  }

  const adviser = await adviserService.getAdviserDetails({ _id: userId });
  if (!adviser) {
    throw new ApiError(httpStatus.NOT_FOUND, "Adviser not found");
  }

  return res.status(httpStatus.OK).send({
    message: "Documents fetched successfully",
    documents: adviser.documents,
  });
});

const deleteDocuments = catchAsync(async (req, res) => {
  const { id } = req.params;

  if (!id) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Document ID is required");
  }

  try {
    // Call the delete document service method
    const updatedUser = await adviserService.deleteDocument(req.user, id);

    // Check if the document was successfully deleted
    if (!updatedUser) {
      throw new ApiError(
        httpStatus.NOT_FOUND,
        "Document not found or could not be deleted"
      );
    }

    // Send a success response if deletion was successful
    return res.status(httpStatus.OK).json({
      status: "success",
      message: "Document deleted successfully",
      data: updatedUser,
    });
  } catch (error) {
    // If the error is that the document is approved, return a specific error
    if (error.statusCode === httpStatus.FORBIDDEN) {
      return res.status(httpStatus.FORBIDDEN).json({
        status: "error",
        message: error.message,
      });
    }
    // Re-throw other errors to be handled by the global error handler
    throw error;
  }
});

const getAllNotifications = catchAsync(async (req, res) => {
  const id = req.user._id;
  let { options, filters } = getPaginateConfig(req.query);

  const notifications = await appNotificationService.getNotifications(
    filters,
    options,
    id,
    req.user.__t
  );

  if (!notifications.results || notifications.results.length === 0) {
    return res.status(httpStatus.OK).json({
      message: "No Notifications found",
      data: {},
    });
  }

  return res.status(httpStatus.OK).json({
    message: "Notifications fetched successfully",
    data: notifications,
  });
});

const getLeaderBoard = catchAsync(async (req, res) => {
  const { filters, options } = getPaginateConfig(req.query);
  options.sortBy = "averageRating";
  options.limit = 10;
  filters.isDeleted = false;
  filters.isVerified = true;
  options.project = {
    _id: 1,
    name: 1,
    profilePic: 1,
    averageRating: 1,
    previousRank: 1,
  };
  const advisers = await adviserService.getAdvisers(filters, options);

  const results = [];

  for (let index = 0; index < advisers.results.length; index++) {
    const adviser = advisers.results[index];
    const currentRank = index + 1;
    const previousRank = adviser.previousRank;

    if (previousRank !== currentRank) {
      await adviserService.updateAdviser(adviser._id, {
        previousRank: currentRank,
      });
    }

    results.push({
      ...adviser,
      previousRank: previousRank || null,
    });
  }

  const response = {
    results,
    page: advisers.page,
    limit: advisers.limit,
    totalPages: advisers.totalPages,
    totalResults: advisers.totalDocs,
  };

  return res.status(httpStatus.OK).send(response);
});

const getAdviserAvaliability = catchAsync(async (req, res) => {
  const id = req.params?.id || req.user?._id;
  if (!id) {
    throw new ApiError(httpStatus.BAD_REQUEST, "No ID found in params");
  }

  // Get the timezone from headers, default to 'UTC'
  const timezone = req.headers["timezone"] || appDefaults.TIMEZONE;

  const adviser = await adviserService.getAdviserAvaliability(id);
  // Convert from UTC to the provided timezone

  const convertedAvailability = adviser.availability.map((availability) => {
    return {
      ...availability,
      timeSlots: availability.timeSlots.map((slot) => {
        // Add a placeholder date to the time (RFC3339 format)
        const startDateTime = moment.tz(`1970-01-01T${slot.start}`, timezone);
        const endDateTime = moment.tz(`1970-01-01T${slot.end}`, timezone);

        if (!startDateTime.isValid() || !endDateTime.isValid()) {
          console.error(`Invalid Date: Start: ${slot.start}, End: ${slot.end}`);
          return { ...slot, start: "Invalid date", end: "Invalid date" };
        }

        const startFormatted = startDateTime.format("HH:mm");
        const endFormatted = endDateTime.format("HH:mm");

        return {
          ...slot,
          start: startFormatted,
          end: endFormatted,
        };
      }),
    };
  });

  return res.status(httpStatus.OK).json({
    message: "Availability retrieved successfully",
    data: convertedAvailability,
  });
});

const getAvailableAdvisers = catchAsync(async (req, res) => {
  const { startTime, duration } = req.body;
  const { options, filters } = getPaginateConfig(req.query);
  const timezone = req.headers.timezone || appDefaults.TIMEZONE;
  const date = req.query.dateAvailable;

  if (!date || !startTime || !duration) {
    return res
      .status(httpStatus.BAD_REQUEST)
      .json({ message: "Missing required parameters" });
  }

  // Convert date to moment object in user's timezone
  const userDate = moment.tz(date, "YYYY-MM-DD", timezone);
  const requestedDay = userDate.format("dddd");

  // Convert requested start and end time to moment in user's timezone
  const userStartTime = moment.tz(
    `${date} ${startTime}`,
    "YYYY-MM-DD HH:mm",
    timezone
  );
  const userEndTime = userStartTime.clone().add(duration, "minutes");

  options.limit = 100;

  // Fetch advisers with pagination/filter
  const advisers = await adviserService.getAllAdvisers(filters, options, req);

  if (!advisers.results.length) {
    return res
      .status(httpStatus.NOT_FOUND)
      .json({ message: "No advisers found" });
  }

  // Get adviser IDs for appointment conflict query
  const adviserIds = advisers.results.map((a) => a._id);

  // Fetch conflicting appointments overlapping requested time range
  const conflictingAppointments = await Appointment.find({
    adviser: { $in: adviserIds },
    status: "scheduled",
    paymentStatus: { $in: ["paid", "processing", "free"] },
    $or: [
      {
        "timeSlot.start": { $lt: userEndTime.toISOString() },
        "timeSlot.end": { $gt: userStartTime.toISOString() },
      },
    ],
  })
    .select("adviser timeSlot.start timeSlot.end")
    .lean();

  // Set of advisers with conflicting appointments
  const conflictingAdviserIds = new Set(
    conflictingAppointments.map((appt) => appt.adviser.toString())
  );

  // Current time + 15 min buffer (to avoid immediate past slots)
  const nowPlusBuffer = moment.tz(timezone).add(15, "minutes");

  // Filter available advisers
  const availableAdvisers = advisers.results
    .filter((adviser) => {
      // Check if adviser is available on requested day
      const availableDay = adviser.availability.find(
        (day) => day.day === requestedDay
      );
      if (!availableDay || !availableDay.timeSlots.length) return false;

      // Check if any slot fully contains requested start/end time

      const isSlotAvailable = availableDay.timeSlots.some((slot) => {
        // Remove Z and parse times as UTC times only (no date)

        const slotStartTimeOnly = moment.utc(
          slot.start.replace("Z", ""),
          "HH:mm:ss"
        );

        const slotEndTimeOnly = moment.utc(
          slot.end.replace("Z", ""),
          "HH:mm:ss"
        );

        // Convert times to user's timezone (just time portion)
        const slotStartLocalTime = slotStartTimeOnly.clone().tz(timezone);
        const slotEndLocalTime = slotEndTimeOnly.clone().tz(timezone);

        // Extract time components from converted local time
        const slotStartTimeStr = slotStartLocalTime.format("HH:mm:ss");
        const slotEndTimeStr = slotEndLocalTime.format("HH:mm:ss");

        // Combine user's date with converted local times to build full datetimes
        let slotStartLocal = moment.tz(
          `${date} ${slotStartTimeStr}`,
          "YYYY-MM-DD HH:mm:ss",
          timezone
        );

        let slotEndLocal = moment.tz(
          `${date} ${slotEndTimeStr}`,
          "YYYY-MM-DD HH:mm:ss",
          timezone
        );

        // Handle overnight slot crossing midnight
        if (slotEndLocal.isBefore(slotStartLocal)) {
          slotEndLocal = slotEndLocal.add(1, "day");
        }

        // Reject slots that start too soon or already passed (within 15min buffer)
        if (
          userStartTime.isSameOrBefore(nowPlusBuffer) &&
          userDate.isSame(moment.tz(timezone), "day")
        ) {
          return false;
        }

        // Check if slot fully contains requested interval
        const slotContainsRequest =
          slotStartLocal.isSameOrBefore(userStartTime) &&
          slotEndLocal.isSameOrAfter(userEndTime);

        return slotContainsRequest;
      });

      if (!isSlotAvailable) return false;

      // Exclude advisers with conflicting appointments
      if (conflictingAdviserIds.has(adviser._id.toString())) {
        return false;
      }

      return true;
    })
    .map((adviser) => ({
      adviserId: adviser._id,
      name: adviser.name,
      bio: adviser.bio,
      profilePic: adviser.profilePic,
      specialization: adviser.specialization,
      experience: adviser.experience,
      averageRating: adviser.averageRating,
    }));

  if (availableAdvisers.length === 0) {
    return res.status(httpStatus.NOT_FOUND).json({
      message: "No available advisers for the selected date and time slot",
    });
  }

  return res.status(httpStatus.OK).json({
    message: "Available advisers for the requested time range",
    date: userDate.format("YYYY-MM-DD"),
    timeSlot: {
      start: userStartTime.format("HH:mm"),
      end: userEndTime.format("HH:mm"),
    },
    day: requestedDay,
    page: advisers.page,
    limit: advisers.limit,
    availableAdvisers,
    timezone,
  });
});

const getWeeklyTargetStats = catchAsync(async (req, res) => {
  // Get adviser ID (either from the logged-in adviser or from params for admin)
  let adviserId = req.user._id;

  // Get the adviser's details including weekly target
  const adviser = await adviserService.getAdviserDetails({ _id: adviserId });
  if (!adviser) {
    throw new ApiError(httpStatus.NOT_FOUND, "Adviser not found");
  }

  // Get the weekly target (default to 0 if not set)
  const weeklyTarget = adviser.weeklyTarget || 0;

  const timezone = req.headers.timezone || appDefaults.TIMEZONE;

  // Calculate the start and end of the current week (Monday to Sunday)
  const startOfWeek = moment()
    .tz(timezone)
    .startOf("week")
    .startOf("day")
    .utc()
    .toISOString();

  const endOfWeek = moment()
    .tz(timezone)
    .endOf("week")
    .endOf("day")
    .utc()
    .toISOString();

  // Get completed payouts for the current week
  const weeklyPayouts = await payoutService.getPayoutsByAdviser(adviserId, {
    status: "completed",
    createdAt: {
      $gte: startOfWeek,
      $lte: endOfWeek,
    },
  });

  // Calculate total earnings for the week from payouts
  const weeklyEarnings = weeklyPayouts.reduce(
    (total, payout) => total + payout.amount,
    0
  );

  // Calculate percentage of target achieved
  const percentageAchieved =
    weeklyTarget > 0 ? (weeklyEarnings / weeklyTarget) * 100 : 0;

  // Calculate remaining amount to reach target
  const remainingAmount = Math.max(0, weeklyTarget - weeklyEarnings);

  // Format dates for response
  const formattedStartDate = moment(startOfWeek).format("YYYY-MM-DD");
  const formattedEndDate = moment(endOfWeek).format("YYYY-MM-DD");

  // Get completed appointments for additional context
  const completedAppointments = await Appointment.find({
    adviser: adviserId,
    status: "completed",
    paymentStatus: { $in: ["paid", "free"] },
    date: { $gte: startOfWeek, $lte: endOfWeek },
  })
    .select("duration date amount")
    .lean();

  // Calculate total minutes from all completed sessions
  const totalMinutes = completedAppointments.reduce(
    (sum, appointment) => sum + (appointment.duration || 0),
    0
  );

  // Convert minutes into total hours
  const totalHours = totalMinutes / 60;

  return res.status(httpStatus.OK).json({
    success: true,
    message: "Weekly target stats retrieved successfully",
    data: {
      adviserId,
      weekStartDate: formattedStartDate,
      weekEndDate: formattedEndDate,
      weeklyTarget,
      weeklyEarnings: parseFloat(weeklyEarnings.toFixed(2)),
      percentageAchieved: parseFloat(percentageAchieved.toFixed(2)),
      remainingAmount: parseFloat(remainingAmount.toFixed(2)),
      isTargetAchieved: weeklyEarnings >= weeklyTarget,
      appointmentStats: {
        totalAppointments: completedAppointments.length,
        totalMinutes,
        totalHours: parseFloat(totalHours.toFixed(2)),
      },
      hoursPerWeek: adviser.hoursPerWeek,
    },
  });
});

const deleteAdviser = catchAsync(async (req, res) => {
  const userId = req.params.id || req.user._id;
  const deleteUser = await adviserService.deleteAdviser(userId);

  //triggered notification on admin deactivate

  if (req.user.__t === userTypes.ADMIN) {
    const block = await adviserService.blockAdviser(userId);

    const notificationData = {
      title: "Account Blocked",
      description: "Your account has been Blocked by the Admin.",
      type: notificationCategories.BLOCKED,
      targetUser: userId,
      image: "",
    };
    await appNotificationService.sendNotification(
      notificationData,
      userId.toString()
    );
  }

  if (req.user.__t !== userTypes.ADMIN) {
    // emit event to delete firebase , connected account and stripe account
  }

  if (!deleteUser) {
    throw new ApiError(httpStatus.NOT_FOUND, "User not found");
  }
  return res
    .status(httpStatus.OK)
    .json({ message: "User deleted successfully" });
});

const activeAdviser = catchAsync(async (req, res) => {
  let userId = req.user._id;
  if (req.user.__t === userTypes.ADMIN) {
    userId = req.params.id;
    const unblockAdviser = await adviserService.unBlockAdviser(userId);
  }

  const user = await adviserService.getAdviserDetails({ _id: userId });
  if (user.isBlockedByAdmin) {
    throw new ApiError(httpStatus.FORBIDDEN, "Account blocked by admin");
  }
  const activeAdviser = await adviserService.activeAdviser(userId);
  if (!activeAdviser) {
    throw new ApiError(httpStatus.NOT_FOUND, "adviser not found");
  }
  return res
    .status(httpStatus.OK)
    .json({ message: "adviser Activated successfully" });
});

const getCommissionPercentage = catchAsync(async (req, res) => {
  const commissionPercentage =
    await appSettingsService.getSetting("platformCommission");

  return res.status(httpStatus.OK).json({
    message: "Commission percentage fetched successfully.",
    percent: commissionPercentage,
  });
});

module.exports = {
  updateProfile,
  getAllAdvisers,
  getReviews,
  updateAvailability,
  uploadDocuments,
  getDocuments,
  deleteDocuments,
  getAllNotifications,
  getAdviser,
  getLeaderBoard,
  getAdviserAvaliability,
  getAvailableAdvisers,
  getWeeklyTargetStats,
  deleteAdviser,
  activeAdviser,
  getCommissionPercentage,
};
