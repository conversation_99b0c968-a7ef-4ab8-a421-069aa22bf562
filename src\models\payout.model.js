const mongoose = require("mongoose");

const payoutAdviserSchema = new mongoose.Schema(
  {
    adviserId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Adviser",
      required: true,
    },
    appointmentId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Appointment",
      required: true,
    },
    amount: { type: Number, required: true },
    stripeTransferId: {
      type: String,
      required: true,
      default: null,
      unique: true,
    }, // ID from Stripe
    status: {
      type: String,
      enum: ["initialized", "completed", "failed"],
    },
  },
  {
    timestamps: true,
  }
);
payoutAdviserSchema.index({ adviserId: 1, appointmentId: 1 });
const Payout = mongoose.model("Payout", payoutAdviserSchema);
module.exports = { Payout };
