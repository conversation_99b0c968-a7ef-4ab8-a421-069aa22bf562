const express = require("express");
const { firebaseAuth } = require("../../middlewares/firebaseAuth");
const { agoraController } = require("../../controllers");
const { agoraValidation } = require("../../validations");
const validate = require("../../middlewares/validate");

const router = express.Router();

router.get(
  "/authenticate",
  firebaseAuth("All"),
  agoraController.agoraAuthenticate
);

router.get(
  "/token/:appointmentId",
  firebaseAuth("Adviser,User"),
  validate(agoraValidation.generateAgoraToken),
  agoraController.generateAgoraToken
);

router.post(
  "/start-recording/:appointmentId",
  firebaseAuth("All"),
  validate(agoraValidation.startAgoraRecording),
  agoraController.startAgoraRecording
);

router.post(
  "/stop-recording",
  firebaseAuth("All"),
  // validate(agoraValidation.stopAgoraRecording),
  agoraController.stopAgoraRecording
);

router.get(
  "/whiteboard/start/session/:appointmentId",
  firebaseAuth("All"),
  validate(agoraValidation.setupWhiteboardSession),
  agoraController.setupWhiteboardSession
);

module.exports = router;
