const express = require("express");

const validate = require("../../middlewares/validate");
const { firebaseAuth } = require("../../middlewares/firebaseAuth");
const { queryController } = require("../../controllers");
const { queryValidation } = require("../../validations");

const router = express.Router();

router.post(
  "/add/:appointmentId",
  firebaseAuth("User,Adviser"),
  validate(queryValidation.addQuery),
  queryController.addQuery
);

router.get(
  "/all",
  firebaseAuth("All"),
  validate(queryValidation.getQueries),
  queryController.getQueries
);

router.post(
  "/details/:id",
  firebaseAuth("All"),
  queryController.getQueryDetails
);

router.patch(
  "/status-update/:id",
  firebaseAuth("Admin"),
  validate(queryValidation.resolveQuery),
  queryController.updateQueryStatus
);

module.exports = router;
