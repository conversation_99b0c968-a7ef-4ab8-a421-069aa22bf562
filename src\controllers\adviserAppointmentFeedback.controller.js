const httpStatus = require("http-status");
const ApiError = require("../utils/ApiError");
const catchAsync = require("../utils/catchAsync");
const {
  adviserAppointmentFeedbackService,
  appointmentService,
} = require("../services");

const {
  adviserFeedbackResponses,
  emitterEventNames,
  notificationCategories,
} = require("../constants");

const { default: mongoose } = require("mongoose");

const eventEmitter = require("../events/eventEmitter");

const createFeedback = catchAsync(async (req, res) => {
  const { appointmentId } = req.params;
  const { adviserResponse } = req.body;
  // Check if appointment exists
  const appointment = await appointmentService.findOneAppointment(
    new mongoose.Types.ObjectId(appointmentId)
  );

  if (!appointment) {
    throw new ApiError(httpStatus.NOT_FOUND, "Appointment not found");
  }

  if (appointment.status !== "completed") {
    throw new ApiError(httpStatus.BAD_REQUEST, "Appointment not completed");
  }

  // Check if feedback already exists
  const feedbackExists =
    await adviserAppointmentFeedbackService.feedbackExists(appointmentId);
  if (feedbackExists) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      "Feedback already exists for this appointment"
    );
  }

  // Validate that the adviser is the one who conducted the appointment
  if (req.user._id.toString() !== appointment.adviser._id.toString()) {
    throw new ApiError(
      httpStatus.FORBIDDEN,
      "You can only provide feedback for your own appointments"
    );
  }
  // const normalizedResponse = adviserResponse.replace(/'/g, "’");
  const normalizedResponse = adviserResponse;

  // Create feedback
  const feedbackData = {
    appointmentId,
    adviserId: req.user._id,
    userId: appointment.user,
    adviserResponse: normalizedResponse,
  };

  const feedback =
    await adviserAppointmentFeedbackService.createFeedback(feedbackData);

  // Check if feedback requires follow-up notification
  if (
    normalizedResponse === adviserFeedbackResponses.FOLLOW_UP_RECOMMENDED ||
    normalizedResponse === adviserFeedbackResponses.NEEDS_REGULATED_GUIDANCE
  ) {
    // Emit event for notification
    eventEmitter.emit(emitterEventNames.APPOINTMENT_FEEDBACK_FOLLOWUP, {
      appointmentId,
      userId: appointment.user,
      adviserId: req.user._id,
      adviserName: req.user.name,
      feedbackType: normalizedResponse,
    });
  }

  return res.status(httpStatus.CREATED).json({
    success: true,
    message: "Feedback created successfully",
    data: feedback,
  });
});

const updateAdviserFeedback = catchAsync(async (req, res) => {
  const { appointmentId } = req.params;
  const { adviserResponse } = req.body;

  // Check if feedback exists
  const existingFeedback =
    await adviserAppointmentFeedbackService.getFeedbackByAppointmentId(
      appointmentId
    );
  if (!existingFeedback) {
    throw new ApiError(
      httpStatus.NOT_FOUND,
      "Feedback not found for this appointment"
    );
  }

  // Validate that the adviser is the one who provided the original feedback
  if (req.user._id.toString() !== existingFeedback.adviserId.toString()) {
    throw new ApiError(
      httpStatus.FORBIDDEN,
      "You can only update your own feedback"
    );
  }

  // Update feedback
  const updatedFeedback =
    await adviserAppointmentFeedbackService.updateAdviserFeedback(
      appointmentId,
      { adviserResponse }
    );

  // Check if updated feedback requires follow-up notification
  if (
    adviserResponse === adviserFeedbackResponses.FOLLOW_UP_RECOMMENDED ||
    adviserResponse === adviserFeedbackResponses.NEEDS_REGULATED_GUIDANCE
  ) {
    const appointment = await appointmentService.findAppointment(appointmentId);

    // Emit event for notification
    eventEmitter.emit(emitterEventNames.APPOINTMENT_FEEDBACK_FOLLOWUP, {
      appointmentId,
      userId: appointment.user._id,
      adviserId: req.user._id,
      adviserName: req.user.name,
      feedbackType: adviserResponse,
    });
  }

  return res.status(httpStatus.OK).json({
    success: true,
    message: "Feedback updated successfully",
    data: updatedFeedback,
  });
});

const getFeedback = catchAsync(async (req, res) => {
  const { appointmentId } = req.params;

  // Check if feedback exists
  const feedback =
    await adviserAppointmentFeedbackService.getFeedbackByAppointmentId(
      appointmentId
    );
  if (!feedback) {
    throw new ApiError(
      httpStatus.NOT_FOUND,
      "Feedback not found for this appointment"
    );
  }

  // Check if user is authorized to view the feedback
  if (
    req.user.__t !== "Admin" &&
    req.user._id.toString() !== feedback.adviserId.toString() &&
    req.user._id.toString() !== feedback.userId.toString()
  ) {
    throw new ApiError(
      httpStatus.FORBIDDEN,
      "You are not authorized to view this feedback"
    );
  }

  return res.status(httpStatus.OK).json({
    success: true,
    message: "Feedback retrieved successfully",
    data: feedback,
  });
});

const getAllFeedback = catchAsync(async (req, res) => {
  // Only admin can access all feedback
  if (req.user.__t !== "Admin") {
    throw new ApiError(
      httpStatus.FORBIDDEN,
      "Only admin can access all feedback"
    );
  }

  const { page, limit, sortBy, sortOrder, adviserId, userId, response } =
    req.query;

  // Build filter
  const filter = {};
  if (adviserId) filter.adviserId = adviserId;
  if (userId) filter.userId = userId;
  if (response) {
    filter.$or = [{ adviserResponse: response }, { userResponse: response }];
  }

  // Get paginated feedback
  const feedback = await adviserAppointmentFeedbackService.getAllFeedback(
    filter,
    {
      page,
      limit,
      sortBy,
      sortOrder,
    }
  );

  return res.status(httpStatus.OK).json({
    success: true,
    message: "Feedback retrieved successfully",
    data: feedback,
  });
});

const getAdviserFeedback = catchAsync(async (req, res) => {
  // Only advisers can access their own feedback
  if (req.user.__t !== "Adviser") {
    throw new ApiError(
      httpStatus.FORBIDDEN,
      "Only advisers can access their feedback"
    );
  }

  const { page, limit, sortBy, sortOrder, response } = req.query;

  // Build filter
  const filter = { adviserId: req.user._id };
  if (response) {
    filter.adviserResponse = response;
  }

  // Get paginated feedback
  const feedback = await adviserAppointmentFeedbackService.getAllFeedback(
    filter,
    {
      page,
      limit,
      sortBy,
      sortOrder,
    }
  );

  return res.status(httpStatus.OK).json({
    success: true,
    message: "Feedback retrieved successfully",
    data: feedback,
  });
});

const respondToFeedback = catchAsync(async (req, res) => {
  const { appointmentId } = req.params;
  const { userConsentToBeContacted } = req.body;

  const feedback =
    await adviserAppointmentFeedbackService.getFeedbackByAppointmentId(
      appointmentId
    );

  if (!feedback) {
    throw new ApiError(httpStatus.NOT_FOUND, "Feedback not found");
  }

  // Check if the user is the correct one
  if (req.user._id.toString() !== feedback.userId._id.toString()) {
    throw new ApiError(
      httpStatus.FORBIDDEN,
      "You can only respond to your own feedback"
    );
  }

  // Already responded?
  if (feedback.userConsentToBeContacted) {
    throw new ApiError(httpStatus.BAD_REQUEST, "You have already responded");
  }

  feedback.userConsentToBeContacted = userConsentToBeContacted;
  if (userConsentToBeContacted === false) {
    feedback.userRejectionReason = req.body.userRejectionReason;
  }

  await feedback.save();

  return res.status(httpStatus.OK).json({
    success: true,
    message: "Your response has been saved.",
    data: feedback,
  });
});

const getAppointmentFeedback = catchAsync(async (req, res) => {
  // Only admin can access all feedback
  if (req.user.__t !== "Admin") {
    throw new ApiError(
      httpStatus.FORBIDDEN,
      "Only admin can access all feedback"
    );
  }

  // Get paginated feedback
  const feedback =
    await adviserAppointmentFeedbackService.getFeedbackByAppointmentId(
      req.params.id
    );

  return res.status(httpStatus.OK).json({
    success: true,
    message: "Feedback retrieved successfully",
    data: feedback,
  });
});

module.exports = {
  createFeedback,
  updateAdviserFeedback,
  getFeedback,
  getAllFeedback,
  getAdviserFeedback,
  respondToFeedback,
  getAppointmentFeedback,
};
