const {
  emitterEvent<PERSON><PERSON>s,
  DIFFERENCE_DESCRIPTIONS,
  notificationCategories,
} = require("../constants");
const { mailService } = require("../microservices");
const ejs = require("ejs");
const path = require("path");
const stripeService = require("../services/stripe.service");
const { userStripeService, appNotificationService } = require("../services");
const catchEventHandler = require("../utils/catchEventHandler");

const handleUserSignedUp = catchEventHandler(async (user) => {
  const stripeUser = await stripeService.createCustomer(user);
  await userStripeService.createStripeCustomer(stripeUser.id, user._id);
});

const handleUserReward = catchEventHandler(async (referredBy, freeSessions) => {
  const data = {
    title: "Your Referral Reward",
    description: "You have received 1 free session as a referral reward ! 🎉",
    type: notificationCategories.GENERAL,
    targetUser: referredBy,
  };

  const notification = await appNotificationService.createAndSendNotification(
    data,
    referredBy._id.toString()
  );

  // console.log("✅ Referral reward notification sent:", notification);
});

const handleSendLoginCredentials = catchEventHandler(async (data) => {
  const { email, name, password, userType } = data;

  // console.log(`🔔 Sending login credentials to ${email}`);

  try {
    // Render email template
    const emailHtml = await ejs.renderFile(
      path.join(__dirname, "../views/emails/loginCredentials.ejs"),
      {
        name,
        email,
        password,
        userType,
      }
    );

    // Send email
    await mailService.sendEmail({
      to: email,
      subject: "Your Money Owl Account Credentials",
      html: emailHtml,
    });

    // console.log(`✅ Login credentials sent to ${email} successfully`);
  } catch (error) {
    console.error(`❌ Error sending login credentials: ${error.message}`);
  }
});

module.exports = (emitter) => {
  // emitter.on(emitterEventNames.USER_LOGGED_IN, handleUserLoggedIn);
  emitter.on(emitterEventNames.USER_SIGNED_UP, handleUserSignedUp);
  emitter.on(emitterEventNames.REFERRAL_REWARD, handleUserReward);
  emitter.on(
    emitterEventNames.SEND_LOGIN_CREDENTIALS,
    handleSendLoginCredentials
  );
};
