const httpStatus = require("http-status");
const { agora, aws } = require("../config/config");
const { Appointment } = require("../models");
const catchAsync = require("../utils/catchAsync");
const { userTypes, emitterEventNames } = require("../constants");
const ApiError = require("../utils/ApiError");
const { RtcTokenBuilder, RtcRole } = require("agora-access-token");
const { WhiteBoard } = require("../models/whiteBoard.model");
const { default: mongoose } = require("mongoose");
const eventEmitter = require("../events/eventEmitter");

const APP_ID = agora.appId;
const APP_CERTIFICATE = agora.appCertificate;
const CUSTOMER_ID = agora.customerKey;
const CUSTOMER_SECRET = agora.customerSecret;
const WHITEBOARD_SECRET_KEY = agora.whiteboard.secretKey;
const WHITEBOARD_ACCESS_KEY = agora.whiteboard.accessKey;
const LOCATION = agora.location;

//GENERATE TOKEN TO ACCESS THE VIDEO CALL ROOM
const generateAgoraToken = catchAsync(async (req, res) => {
  const { appointmentId } = req.params;

  if (!APP_ID || !APP_CERTIFICATE) {
    throw new ApiError(
      httpStatus.INTERNAL_SERVER_ERROR,
      "Agora credentials are missing"
    );
  }

  if (!appointmentId) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Appointment ID is required");
  }

  // Fetch appointment from the database
  const appointment = await Appointment.findById(appointmentId)
    .populate("user", "name")
    .populate("adviser", "name profilePic");

  if (!appointment) {
    throw new ApiError(httpStatus.NOT_FOUND, "Appointment not found");
  }

  const now = new Date();
  const startTime = new Date(appointment.timeSlot.start);
  const endTime = new Date(appointment.timeSlot.end);
  if (now < startTime) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      "Call cannot start before the scheduled time"
    );
  }

  if (now > endTime) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Call time has already ended");
  }

  //Ensure the requesting user is part of the appointment
  if (
    (req.user.__t === userTypes.USER &&
      !appointment.user._id.equals(req.user._id)) ||
    (req.user.__t === userTypes.ADVISER &&
      !appointment.adviser._id.equals(req.user._id))
  ) {
    throw new ApiError(httpStatus.UNAUTHORIZED, "Unauthorized");
  }

  if (
    appointment.paymentStatus !== "paid" &&
    appointment.paymentStatus !== "free"
  ) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Payment not completed yet");
  }
  if (appointment.adviserStatus !== "accepted") {
    throw new ApiError(httpStatus.BAD_REQUEST, "Adviser not accepted yet");
  }

  // Unique channel name using appointment ID
  const channelName = `${appointmentId}`;
  const expirationTimestamp = Math.floor(Date.now() / 1000) + 3600; //1 hr

  const uid = 0;

  // Generate Agora RTC Token
  const token = RtcTokenBuilder.buildTokenWithUid(
    APP_ID,
    APP_CERTIFICATE,
    channelName,
    uid,
    RtcRole.PUBLISHER,
    expirationTimestamp
  );

  // Determine who is initiating the call and who should receive the notification
  let initiator, recipient, recipientId;

  if (req.user.__t === userTypes.USER) {
    appointment.hasUserJoined = true;
    if (!appointment.userJoinTime) {
      appointment.userJoinTime = new Date();
    }
  } else if (req.user.__t === userTypes.ADVISER) {
    appointment.hasAdviserJoined = true;
    if (!appointment.adviserJoinTime) {
      appointment.adviserJoinTime = new Date();
    }
  }

  await appointment.save();

  if (req.user.__t === userTypes.ADVISER) {
    initiator = appointment.adviser.name;
    recipient = appointment.user.name;
    recipientId = appointment.user._id.toString();

    // Get profile picture URL
    const profilePicUrl =
      req.user.__t === userTypes.USER
        ? appointment.user.profilePic?.url || null
        : appointment.adviser.profilePic?.url || null;

    // Emit event to send notification asynchronously
    eventEmitter.emit(emitterEventNames.VIDEO_CALL_NOTIFICATION, {
      initiator,
      recipient,
      recipientId,
      token,
      channelName,
      appointmentId,
      appointment,
      initiatorType: req.user.__t,
      profilePicUrl,
    });
  }

  //channelname , token , who joined first
  return res.status(200).json({ token, channelName, uid });
});

const agoraAuthenticate = catchAsync(async (req, res) => {
  const plainCredential = `${CUSTOMER_ID}:${CUSTOMER_SECRET}`;
  const encodedCredential = Buffer.from(plainCredential).toString("base64");
  const authorizationField = "Basic " + encodedCredential;

  const response = await fetch("https://api.agora.io/dev/v1/projects", {
    method: "GET",
    headers: {
      Authorization: authorizationField,
      "Content-Type": "application/json",
    },
  });

  if (!response.ok) {
    throw new ApiError(
      httpStatus.INTERNAL_SERVER_ERROR,
      `error from agora status: ${response.status}`
    );
  }

  const data = await response.json();
  return res.status(200).json(data);
});

const startAgoraRecording = catchAsync(async (req, res) => {
  const { appointmentId } = req.params;
  const channelName = `${appointmentId}`;

  //123 will be uid for the agora bot
  if (!appointmentId) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Appointment ID is required");
  }

  const resourceId = await acquireResource(channelName, 123);

  const token = await generateToken(channelName, 123);

  const { sid, uid } = await startRecording(resourceId, channelName, token);

  // console.log("Agora Recording Started: -------->", { resourceId, sid, uid });

  return res.status(200).json({ resourceId, sid, uid, channelName });
});

const stopAgoraRecording = catchAsync(async (req, res) => {
  const { resourceId, sid, channelName, appointmentId } = req.body;

  const result = await stopRecording(resourceId, sid, channelName, "123");

  // console.log("Agora Recording stopped: -------->", result);

  const fileList = result.serverResponse?.fileList || [];

  const mp4File = fileList.find((file) => file.fileName.endsWith(".mp4"));

  if (!mp4File) {
    throw new ApiError(
      httpStatus.INTERNAL_SERVER_ERROR,
      "MP4 recording file not found."
    );
  }

  const fileUrl = `https://${aws.s3.name}.s3.${aws.s3.region}.amazonaws.com/${mp4File.fileName}`;
  const appointment = await Appointment.findOneAndUpdate(
    { _id: new mongoose.Types.ObjectId(channelName) },
    { recordingUrl: fileUrl },
    { new: true }
  );

  if (!appointment) {
    throw new ApiError(httpStatus.NOT_FOUND, "Appointment not found.");
  }

  return res.status(200).json({
    message: "Recording stopped and URL saved successfully",
    recordingUrl: fileUrl,
    appointment,
  });
});

const setupWhiteboardSession = catchAsync(async (req, res) => {
  const { appointmentId } = req.params;
  if (!appointmentId) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Appointment ID is required");
  }

  let whiteboardSession = await WhiteBoard.findOne({ appointmentId });

  if (!whiteboardSession) {
    const sdkToken = await generateSdkToken();
    const roomUuid = await createWhiteboardRoom(sdkToken);

    whiteboardSession = await WhiteBoard.create({
      appointmentId,
      roomUid: roomUuid,
    });
  }

  const sdkToken = await generateSdkToken();
  const whiteboardToken = await generateWhiteboardToken(
    whiteboardSession.roomUid,
    sdkToken
  );

  return res.status(200).json({
    roomUid: whiteboardSession.roomUid,
    roomToken: whiteboardToken,
  });
});

// STOP RECORDING
const stopRecording = async (resourceId, sid, channelName) => {
  const response = await fetch(
    `https://api.agora.io/v1/apps/${APP_ID}/cloud_recording/resourceid/${resourceId}/sid/${sid}/mode/mix/stop`,
    {
      method: "POST",
      headers: {
        Authorization: `Basic ${Buffer.from(
          `${CUSTOMER_ID}:${CUSTOMER_SECRET}`
        ).toString("base64")}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        cname: channelName,
        uid: "123",
        clientRequest: {},
      }),
    }
  );

  const data = await response.json();
  // console.log("Agora Stop Recording Response:", JSON.stringify(data, null, 2));

  if (!response.ok) {
    throw new ApiError(
      httpStatus.INTERNAL_SERVER_ERROR,
      `Agora Stop Recording Failed: ${JSON.stringify(data)}`
    );
  }

  // console.log("Agora Recording Stopped:", data);
  return data;
};

// START RECORDING
const startRecording = async (resourceId, channelName, token = "") => {
  const body = {
    cname: channelName,
    uid: "123", // in each room 123 will be the agora bot uuid
    clientRequest: {
      token: token,
      recordingConfig: {
        channelType: 0, // Communication mode
        streamTypes: 2, // Record both audio and video
        audioProfile: 1, // Medium quality audio
        videoStreamType: 0, // High-quality video
        maxIdleTime: 300, // Stop after 5 MINs of inactivity
        autoSubscribeAudio: true, //Record audio even if user joins late
        autoSubscribeVideo: true, //Record video even if user joins late
        transcodingConfig: {
          width: 1280,
          height: 720,
          fps: 30,
          bitrate: 2260,
          mixedVideoLayout: 1,
          backgroundColor: "#000000",
        },
        subscribeVideoUids: [],
        subscribeAudioUids: [],
        subscribeUidGroup: 1,
      },
      recordingFileConfig: {
        avFileType: ["hls", "mp4"],
      },

      storageConfig: {
        vendor: 1,
        region: 21,
        bucket: aws.s3.name,
        accessKey: aws.s3.accessKeyId,
        secretKey: aws.s3.secretAccessKey,
        fileNamePrefix: ["recordings"],
      },
    },
  };

  const response = await fetch(
    `https://api.agora.io/v1/apps/${APP_ID}/cloud_recording/resourceid/${resourceId}/mode/mix/start`,
    {
      method: "POST",
      headers: {
        Authorization: `Basic ${Buffer.from(
          `${CUSTOMER_ID}:${CUSTOMER_SECRET}`
        ).toString("base64")}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(body),
    }
  );

  const data = await response.json();

  if (!response.ok || !data.sid) {
    throw new ApiError(
      httpStatus.INTERNAL_SERVER_ERROR,
      `Agora Start Recording Failed: ${JSON.stringify(data)}`
    );
  }

  return { sid: data.sid, uid: "0" };
};

//ACCQUIRE RESOURCE ID FOR THE CLOUD RECORDING
const acquireResource = async (channelName, uid) => {
  const body = {
    cname: channelName,
    uid: uid.toString(),
    clientRequest: {
      resourceExpiredHour: 24, // Validity time (in hours)
      scene: 0, // 0 for RTC recording
    },
  };

  const response = await fetch(
    `https://api.agora.io/v1/apps/${APP_ID}/cloud_recording/acquire`,
    {
      method: "POST",
      headers: {
        Authorization: `Basic ${Buffer.from(
          `${CUSTOMER_ID}:${CUSTOMER_SECRET}`
        ).toString("base64")}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(body),
    }
  );

  const data = await response.json();
  // console.log("📥 Agora Acquire Response:", JSON.stringify(data, null, 2));

  if (!response.ok || !data.resourceId) {
    throw new ApiError(
      httpStatus.INTERNAL_SERVER_ERROR,
      `Agora Acquire Failed: ${JSON.stringify(data)}`
    );
  }

  return data.resourceId;
};

//GENERATE RTC TOKEN
const generateToken = async (channelName, uid) => {
  const appId = APP_ID;
  const appCertificate = APP_CERTIFICATE;
  const role = RtcRole.SUBSCRIBER;
  const expirationTime = Math.floor(Date.now() / 1000) + 3600; // Token valid for 1 hour

  return RtcTokenBuilder.buildTokenWithUid(
    appId,
    appCertificate,
    channelName,
    uid.toString(),
    role,
    expirationTime
  );
};

//GENERATE SDK TOKEN TO CREATE A WHITEBOARD
const generateSdkToken = async () => {
  const response = await fetch("https://api.netless.link/v5/tokens/teams", {
    method: "POST",
    headers: { "Content-Type": "application/json", region: LOCATION }, // ✅ Force Region
    body: JSON.stringify({
      accessKey: WHITEBOARD_ACCESS_KEY,
      secretAccessKey: WHITEBOARD_SECRET_KEY,
      lifespan: 36000, // Token valid for ~1000 hours
      role: "admin",
    }),
  });

  const data = await response.json();

  if (!response.ok) {
    throw new ApiError(
      httpStatus.INTERNAL_SERVER_ERROR,
      `SDK Token Generation Error: ${JSON.stringify(data)}`
    );
  }

  return data; // SDK Token needed for frontend
};

//CREATE WHITEBOARD
const createWhiteboardRoom = async (sdkToken) => {
  const response = await fetch("https://api.netless.link/v5/rooms", {
    method: "POST",
    headers: {
      token: sdkToken.toString(),
      "Content-Type": "application/json",
      region: LOCATION, // ✅ Ensure Region Consistency
    },
    body: JSON.stringify({
      isRecord: true, // ✅ Prevent Room from Being Auto-Deleted
      limit: 0, // ✅ No User Limit
    }),
  });

  const data = await response.json();

  if (!response.ok || !data.uuid) {
    throw new ApiError(
      httpStatus.INTERNAL_SERVER_ERROR,
      `Whiteboard Room Creation Error: ${JSON.stringify(data)}`
    );
  }
  return data.uuid;
};

//GENERATE TOKEN TO ACCESS THE WHITEBOARD
const generateWhiteboardToken = async (roomUuid, sdkToken) => {
  const response = await fetch(
    `https://api.netless.link/v5/tokens/rooms/${roomUuid}`,
    {
      method: "POST",
      headers: {
        token: sdkToken.toString(),
        "Content-Type": "application/json",
        region: LOCATION, // ✅ Ensure Region Consistency
      },
      body: JSON.stringify({
        lifespan: 36000, // 1 hour
        role: "writer",
      }),
    }
  );

  const data = await response.json();

  if (!response.ok) {
    throw new ApiError(
      httpStatus.INTERNAL_SERVER_ERROR,
      `Whiteboard Token Generation Error: ${JSON.stringify(data)}`
    );
  }

  return data;
};

module.exports = {
  agoraAuthenticate,
  generateAgoraToken,
  startAgoraRecording,
  stopAgoraRecording,
  setupWhiteboardSession,
};
