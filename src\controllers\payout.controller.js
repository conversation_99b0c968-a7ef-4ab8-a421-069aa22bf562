const httpStatus = require("http-status");
const ApiError = require("../utils/ApiError");
const catchAsync = require("../utils/catchAsync");
const { payoutService } = require("../services");
const moment = require("moment-timezone");
const { appDefaults } = require("../constants");

const getAdviserEarnings = catchAsync(async (req, res) => {
  let adviserId;

  if (req.user.__t === "Admin") {
    adviserId = req.params.adviserId;
  } else {
    adviserId = req.user._id;
  }

  const timezone = req.headers.timezone || appDefaults.TIMEZONE;

  // Get earnings data
  const earningsData = await payoutService.getAdviserEarnings(
    adviserId,
    req.query.format
  );

  // Convert timestamps in monthly payouts
  const convertedMonthlyData = earningsData.monthlyData.map((month) => {
    const convertedPayouts = month.payouts.map((payout) => {
      return {
        ...payout,
        createdAt: moment(payout.createdAt)
          .tz(timezone)
          .format("YYYY-MM-DD HH:mm:ss"),
        updatedAt: moment(payout.updatedAt)
          .tz(timezone)
          .format("YYYY-MM-DD HH:mm:ss"),
      };
    });

    return {
      ...month,
      payouts: convertedPayouts,
    };
  });

  const convertedEarningsData = {
    ...earningsData,
    monthlyData: convertedMonthlyData,
  };

  return res.status(httpStatus.OK).json({
    message: "Earnings data retrieved successfully",
    data: convertedEarningsData,
  });
});

const getPayoutStats = catchAsync(async (req, res) => {
  // Extract filter parameters from query
  const { startDate, endDate, adviserId } = req.query;
  const filters = {};

  // Add date range filter if provided
  if (startDate && endDate) {
    const parsedStartDate = new Date(startDate);
    const parsedEndDate = new Date(endDate);

    if (isNaN(parsedStartDate.getTime()) || isNaN(parsedEndDate.getTime())) {
      throw new ApiError(httpStatus.BAD_REQUEST, "Invalid date format");
    }

    filters.createdAt = {
      $gte: parsedStartDate,
      $lte: parsedEndDate,
    };
  }

  // Add adviser filter if provided
  if (adviserId) {
    filters.adviserId = adviserId;
  }

  // Get payout stats
  const stats = await payoutService.getPayoutCompletionStats(filters);

  return res.status(httpStatus.OK).json({
    success: true,
    message: "Payout completion statistics retrieved successfully",
    data: stats,
  });
});

module.exports = {
  getAdviserEarnings,
  getPayoutStats,
};
