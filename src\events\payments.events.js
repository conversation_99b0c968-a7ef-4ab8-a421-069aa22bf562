const {
  emitterEventNames,
  notificationCategories,
} = require("../constants/index");

const {
  stripeService,
  adviserStripeService,
  payoutService,
  appNotificationService,
  appointmentService,
} = require("../services");

const { Adviser, AdviserStripe } = require("../models");

const { notificationService, mailService } = require("../microservices");
const ejs = require("ejs");
const path = require("path");

const catchEventHandler = require("../utils/catchEventHandler");

const payoutAdviser = catchEventHandler(async (appointmentData) => {
  if (!appointmentData) {
    throw new Error("Appointment data not found.");
  }

  const adviserStripeDetails = await adviserStripeService.getOne({
    adviser: appointmentData.adviser._id,
  });

  // console.log("triggered payout for adviser");

  if (!adviserStripeDetails?.stripeAccountId) {
    throw new Error("Adviser's Stripe account not found.");
  }

  if (!adviserStripeDetails.hasCompletedOnboarding) {
    throw new Error("Adviser onboarding not completed , can't send money");
  }

  if (
    appointmentData.mode === "payment" &&
    (!appointmentData.amount || appointmentData.amount <= 0)
  ) {
    throw new Error("Invalid appointment amount for transfer.");
  }

  const sessionPrice = appointmentData.amount;
  const commissionPercentage = appointmentData.commissionPercentage;
  const adviserPayoutPercentage = 100 - Number(commissionPercentage);
  const adviserPayoutAmount = (adviserPayoutPercentage / 100) * sessionPrice;
  const payoutAmount = Math.round(adviserPayoutAmount);

  try {
    const transferResult = await stripeService.transferMoney({
      adviserId: appointmentData.adviser._id.toString(),
      stripeAccountId: adviserStripeDetails.stripeAccountId,
      amount: payoutAmount,
      appointmentId: appointmentData._id.toString(),
    });

    // console.log("✅ Adviser payout result.", {
    //   result: transferResult,
    // });
  } catch (error) {
    console.error("❌ Payout failed for adviser.", error);
    throw new Error("Adviser payout failed. Please try again.");
  }
});

const handlePaymentSucceeded = catchEventHandler(async (data) => {
  const { bookingId, amount, userId, adviserId, appointmentId, user, adviser } =
    data;

  // console.log(
  //   `🔔 Processing payment success notification for appointment ${appointmentId}`
  // );

  try {
    // Format amount safely
    let formattedAmount = "0.00";
    try {
      if (amount) {
        formattedAmount = parseFloat(amount).toFixed(2);
      }
    } catch (error) {
      console.error(`❌ Error formatting payment amount: ${error.message}`);
    }

    // Create notification title and description
    const title = "Payment Successful";
    const description = `Your payment of £${formattedAmount} for the appointment ${bookingId} has been successfully processed.`;

    // Send push notification
    await notificationService.sendToTopic(
      userId.toString(),
      {
        title,
        body: description,
        image: "",
      },
      {
        type: notificationCategories.GENERAL,
        appointmentId: appointmentId.toString(),
      }
    );

    // Create in-app notification
    await appNotificationService.createNotification({
      title,
      description,
      type: notificationCategories.GENERAL,
      targetUser: userId,
      scheduledAt: Date.now(),

      data: {
        appointmentId: appointmentId.toString(),
        adviserId: adviserId.toString(),
      },
    });

    // Send email notification
    try {
      // console.log(`📧 Sending payment success email to ${user.email}`);

      // Render email template
      const emailHtml = await ejs.renderFile(
        path.join(__dirname, "../views/emails/paymentSuccess.ejs"),
        {
          name: user.name || "",
          email: user.email,
          amount: formattedAmount,
          adviserName: adviser.name,
          bookingId: bookingId,
        }
      );

      // Send email
      await mailService.sendEmail({
        to: user.email,
        subject: "Money Owls - Payment Confirmation",
        html: emailHtml,
      });

      // console.log(`✅ Payment success email sent to ${user.email}`);
    } catch (error) {
      console.error(`❌ Error sending payment success email: ${error.message}`);
    }

    // console.log(`✅ Payment success notification sent to user ${userId}`);
  } catch (error) {
    console.error(
      `❌ Error sending payment success notification: ${error.message}`
    );
  }
});

const handlePaymentFailed = catchEventHandler(async (data) => {
  const { bookingId, amount, userId, adviserId, appointmentId, user, adviser } =
    data;

  // console.log(
  //   `🔔 Processing payment failure notification for appointment ${appointmentId}`
  // );

  try {
    // Format amount safely
    let formattedAmount = "0.00";
    try {
      if (amount) {
        formattedAmount = parseFloat(amount).toFixed(2);
      }
    } catch (error) {
      console.error(
        `❌ Error formatting payment failed amount: ${error.message}`
      );
    }

    // Create notification title and description
    const title = "Payment Failed";
    const description = `Your payment of £${formattedAmount} for the appointment ${bookingId} has failed`;

    // Send push notification
    await notificationService.sendToTopic(
      userId.toString(),
      {
        title,
        body: description,
        image: "",
      },
      {
        type: notificationCategories.GENERAL,
        appointmentId: appointmentId.toString(),
      }
    );

    // Create in-app notification
    await appNotificationService.createNotification({
      title,
      description,
      type: notificationCategories.GENERAL,
      targetUser: userId,
      scheduledAt: Date.now(),
      data: {
        appointmentId: appointmentId.toString(),
        adviserId: adviserId.toString(),
      },
    });

    // Send email notification
    try {
      // console.log(`📧 Sending payment failed email to ${user.email}`);

      // Render email template
      const emailHtml = await ejs.renderFile(
        path.join(__dirname, "../views/emails/paymentFailed.ejs"),
        {
          name: user.name || "",
          email: user.email,
          amount: formattedAmount,
          adviserName: adviser.name,
          bookingId: bookingId,
        }
      );

      // Send email
      await mailService.sendEmail({
        to: user.email,
        subject: "Money Owls - Payment Failed",
        html: emailHtml,
      });

      // console.log(`✅ Payment failed email sent to ${user.email}`);
    } catch (error) {
      console.error(`❌ Error sending payment failed email: ${error.message}`);
    }

    // console.log(`✅ Payment failure notification sent to user ${userId}`);
  } catch (error) {
    console.error(
      `❌ Error sending payment failure notification: ${error.message}`
    );
  }
});

// const handleTransferCompleted = catchEventHandler(async (data) => {
//   const { adviserId, amount, payoutId, appointmentId } = data;

//   console.log(`🔔 Processing transfer completion for payout ${payoutId}`);

//   try {
//     // Update payout status to completed
//     const updatedPayout = await payoutService.updatePayout(payoutId, {
//       status: "completed",
//       updatedAt: new Date(),
//     });

//     if (!updatedPayout) {
//       console.error(`❌ Could not find payout with ID ${payoutId}`);
//       return;
//     }

//     // Format amount safely
//     let formattedAmount = "0.00";
//     try {
//       if (amount) {
//         formattedAmount = parseFloat(amount).toFixed(2);
//       }
//     } catch (error) {
//       console.error(`❌ Error formatting transfer amount: ${error.message}`);
//     }

//     // Get adviser details
//     const adviser = await Adviser.findById(adviserId);
//     if (!adviser) {
//       console.error(`❌ Could not find adviser with ID ${adviserId}`);
//       return;
//     }

//     // Create notification title and description
//     const title = "Payment Completed";
//     const description = `Your payment of £${formattedAmount} has been completed and should now be available in your account.`;

//     // Send push notification
//     await notificationService.sendToTopic(
//       adviserId.toString(),
//       {
//         title,
//         body: description,
//         image: "",
//       },
//       {
//         type: notificationCategories.GENERAL,
//       }
//     );

//     // Create in-app notification
//     await appNotificationService.createNotification({
//       title,
//       description,
//       type: notificationCategories.GENERAL,
//       targetUser: adviserId,
//       scheduledAt: Date.now(),
//       data: {
//         appointmentId: appointmentId ? appointmentId.toString() : null,
//       },
//     });

//     // Send email notification
//     if (adviser.email) {
//       try {
//         console.log(`📧 Sending transfer completed email to ${adviser.email}`);

//         // Render email template
//         const emailHtml = await ejs.renderFile(
//           path.join(__dirname, "../views/emails/transferCompleted.ejs"),
//           {
//             name: adviser.name || "",
//             amount: formattedAmount,
//             appointmentId: appointmentId,
//           }
//         );

//         // Send email
//         await mailService.sendEmail({
//           to: adviser.email,
//           subject: "Money Owls - Payment Completed",
//           html: emailHtml,
//         });

//         console.log(`✅ Transfer completed email sent to ${adviser.email}`);
//       } catch (error) {
//         console.error(
//           `❌ Error sending transfer completed email: ${error.message}`
//         );
//       }
//     }

//     console.log(
//       `✅ Transfer completion notification sent to adviser ${adviserId}`
//     );
//   } catch (error) {
//     console.error(`❌ Error processing transfer completion: ${error.message}`);
//   }
// });

const handleTransferFailed = catchEventHandler(async (data) => {
  const { adviserId, amount, payoutId, appointmentId } = data;

  // console.log(`🔔 Processing transfer failure for payout ${payoutId}`);

  try {
    // Format amount safely
    let formattedAmount = "0.00";
    try {
      if (amount) {
        formattedAmount = parseFloat(amount).toFixed(2);
      }
    } catch (error) {
      console.error(`❌ Error formatting transfer amount: ${error.message}`);
    }

    // Get adviser details
    const adviser = await Adviser.findById(adviserId);
    if (!adviser) {
      console.error(`❌ Could not find adviser with ID ${adviserId}`);
      return;
    }

    // Create notification title and description
    const title = "Payment Failed";
    const description = `Your payment of £${formattedAmount} has failed. Our team has been notified and will investigate the issue.`;

    // Send push notification
    await notificationService.sendToTopic(
      adviserId.toString(),
      {
        title,
        body: description,
        image: "",
      },
      {
        type: notificationCategories.GENERAL,
      }
    );

    // Create in-app notification
    await appNotificationService.createNotification({
      title,
      description,
      type: notificationCategories.GENERAL,
      targetUser: adviserId,
      scheduledAt: Date.now(),
      data: {
        appointmentId: appointmentId ? appointmentId.toString() : null,
      },
    });

    // Send email notification
    if (adviser.email) {
      try {
        // console.log(`📧 Sending transfer failed email to ${adviser.email}`);

        // Render email template
        const emailHtml = await ejs.renderFile(
          path.join(__dirname, "../views/emails/transferFailed.ejs"),
          {
            name: adviser.name || "",
            amount: formattedAmount,
            appointmentId: appointmentId,
          }
        );

        // Send email
        await mailService.sendEmail({
          to: adviser.email,
          subject: "Money Owls - Payment Failed",
          html: emailHtml,
        });

        // console.log(`✅ Transfer failed email sent to ${adviser.email}`);
      } catch (error) {
        console.error(
          `❌ Error sending transfer failed email: ${error.message}`
        );
      }
    }

    // console.log(
    //   `✅ Transfer failure notification sent to adviser ${adviserId}`
    // );
  } catch (error) {
    console.error(`❌ Error processing transfer failure: ${error.message}`);
  }
});

const handleRefundFailed = catchEventHandler(async (data) => {
  const { amount, bookingId, userId, appointmentId, paymentIntentId, reason } =
    data;

  // console.log(`🔔 Processing refund failure for appointment ${appointmentId}`);

  try {
    // Format amount safely
    let formattedAmount = "0.00";
    try {
      if (amount) {
        formattedAmount = parseFloat(amount).toFixed(2);
      }
    } catch (error) {
      console.error(`❌ Error formatting refund amount: ${error.message}`);
    }

    // Get user details
    const appointment = await appointmentService.findOneAppointment({
      _id: appointmentId,
    });

    if (!appointment || !appointment.user) {
      console.error(
        `❌ Could not find appointment or user for ID ${appointmentId}`
      );
      return;
    }

    const user = appointment.user;

    // Create notification title and description
    const title = "Refund Failed";
    const description = `Your refund of £${formattedAmount} for appointment ${bookingId} has failed, You can raise a ticket for this issue.`;

    // Send push notification
    await notificationService.sendToTopic(
      userId.toString(),
      {
        title,
        body: description,
        image: "",
      },
      {
        type: notificationCategories.GENERAL,
        appointmentId: appointmentId.toString(),
      }
    );

    // Create in-app notification
    await appNotificationService.createNotification({
      title,
      description,
      type: notificationCategories.GENERAL,
      targetUser: userId,
      scheduledAt: Date.now(),
      data: {
        appointmentId: appointmentId.toString(),
      },
    });

    // Send email notification
    if (user.email) {
      try {
        // console.log(`📧 Sending refund failed email to ${user.email}`);

        // Render email template
        const emailHtml = await ejs.renderFile(
          path.join(__dirname, "../views/emails/refundFailed.ejs"),
          {
            name: user.name || "",
            email: user.email,
            amount: formattedAmount,
            bookingId: bookingId,
            reason: reason || "Unknown reason",
          }
        );

        // Send email
        await mailService.sendEmail({
          to: user.email,
          subject: "Money Owls - Refund Failed",
          html: emailHtml,
        });

        // console.log(`✅ Refund failed email sent to ${user.email}`);
      } catch (error) {
        console.error(`❌ Error sending refund failed email: ${error.message}`);
      }
    }

    // console.log(`✅ Refund failure notification sent to user ${userId}`);
  } catch (error) {
    console.error(`❌ Error processing refund failure: ${error.message}`);
  }
});

const handleTransferCreated = catchEventHandler(async (data) => {
  const { stripeTransferId, adviserId, appointmentId, amount } = data;

  // console.log(`🔔 Processing transfer creation for adviser ${adviserId}`);

  try {
    // Create payout record
    const payoutRecord = await payoutService.createPayout({
      stripeTransferId,
      adviserId,
      appointmentId,
      amount,
      status: "completed",
      updatedAt: new Date(),
    });

    if (!payoutRecord) {
      console.error("❌ Failed to create payout record");
      return;
    }

    // Format amount safely
    let formattedAmount = "0.00";
    try {
      if (amount) {
        formattedAmount = parseFloat(amount).toFixed(2);
      }
    } catch (error) {
      console.error(`❌ Error formatting transfer amount: ${error.message}`);
    }

    // Get adviser details
    const adviser = await Adviser.findById(adviserId);
    const appointment = await appointmentService.findOneAppointment({
      _id: appointmentId,
    });
    if (!adviser) {
      console.error(`❌ Could not find adviser with ID ${adviserId}`);
      return;
    }

    // Create notification title and description
    const title = "Your Earnings Are On the Way";
    const description = `A payment of £${formattedAmount} has been initiated for your service.`;

    // Send push notification
    await notificationService.sendToTopic(
      adviserId.toString(),
      {
        title,
        body: description,
        image: "",
      },
      {
        type: notificationCategories.GENERAL,
      }
    );

    // Create in-app notification
    await appNotificationService.createNotification({
      title,
      description,
      type: notificationCategories.GENERAL,
      targetUser: adviserId,
      scheduledAt: Date.now(),
      targetRole: null,
      isCreatedByAdmin: false,
    });

    // Send email notification
    if (adviser.email) {
      try {
        // console.log(
        //   `📧 Sending transfer initialized email to ${adviser.email}`
        // );

        // Render email template
        const emailHtml = await ejs.renderFile(
          path.join(__dirname, "../views/emails/transferCompleted.ejs"),
          {
            name: adviser.name || "",
            amount: formattedAmount,
            appointmentId: appointment.bookingId,
          }
        );

        // Send email
        await mailService.sendEmail({
          to: adviser.email,
          subject: "Money Owls - Payment Initiated",
          html: emailHtml,
        });

        // console.log(`✅ Transfer initialized email sent to ${adviser.email}`);
      } catch (error) {
        console.error(
          `❌ Error sending transfer initialized email: ${error.message}`
        );
      }
    }

    // console.log(
    //   `✅ Transfer creation notification sent to adviser ${adviserId}`
    // );
  } catch (error) {
    console.error(`❌ Error processing transfer creation: ${error.message}`);
  }
});

const handlePayoutPaid = catchEventHandler(async (data) => {
  const { amount, stripeAccountId, status, arrivalDate } = data;

  // console.log(`🔔 Processing payout notification for payout `);

  try {
    // Format the amount (convert from cents to pounds)
    let formattedAmount = "0.00";
    try {
      if (amount) {
        formattedAmount = (amount / 100).toFixed(2); // Convert from cents to pounds
      }
    } catch (error) {
      console.error(`❌ Error formatting payout amount: ${error.message}`);
    }

    // Get adviser details (for the recipient of the payout)
    const adviserStripe = await AdviserStripe.findOne({
      stripeAccountId: stripeAccountId,
    }).populate("adviser");

    if (!adviserStripe || !adviserStripe.adviser) {
      console.error(
        `❌ Could not find adviser with stripeAccountId ${stripeAccountId}`
      );
      return;
    }

    // Create the notification title and description
    const title = "Funds Transferred";
    const description = `£${formattedAmount} has been transferred to your bank account. It may take some time to reflect, based on your bank's processing schedule.`;

    // Send push notification to adviser
    await notificationService.sendToTopic(
      adviserStripe.adviser._id.toString(),
      {
        title,
        body: description,
        image: "",
      },
      {
        type: notificationCategories.GENERAL,
      }
    );

    // Create in-app notification
    await appNotificationService.createNotification({
      title,
      description,
      type: notificationCategories.GENERAL,
      targetUser: adviserStripe.adviser._id,
      scheduledAt: Date.now(),
      data: {
        amount: formattedAmount,
      },
    });

    //  Send email notification to adviser (uncomment and ensure it's working)
    if (adviserStripe.adviser.email) {
      try {
        // console.log(
        //   `📧 Sending payout completed email to ${adviserStripe.adviser.email}`
        // );

        // Render email template for payout completion
        const emailHtml = await ejs.renderFile(
          path.join(__dirname, "../views/emails/payoutCompleted.ejs"),
          {
            name: adviserStripe.adviser.name || "",
            amount: formattedAmount,
            payoutId,
            arrivalDate: new Date(arrivalDate).toLocaleString(),
          }
        );

        // Send the email
        await mailService.sendEmail({
          to: adviserStripe.adviser.email,
          subject: "Money Owls - Payout Completed",
          html: emailHtml,
        });

        // console.log(
        //   `✅ Payout completed email sent to ${adviserStripe.adviser.email}`
        // );
      } catch (error) {
        console.error(
          `❌ Error sending payout completed email: ${error.message}`
        );
      }
    }

    // console.log(
    //   `✅ Payout notification sent to adviser ${adviserStripe.adviser._id}`
    // );
  } catch (error) {
    console.error(`❌ Error processing payout notification: ${error.message}`);
  }
});

// ✅ Register event listeners
module.exports = (emitter) => {
  emitter.on(emitterEventNames.ADVISER_PAYOUT, payoutAdviser);
  emitter.on(emitterEventNames.PAYMENT_SUCCEEDED, handlePaymentSucceeded);
  emitter.on(emitterEventNames.PAYMENT_FAILED, handlePaymentFailed);
  // emitter.on(emitterEventNames.TRANSFER_COMPLETED, handleTransferCompleted);
  emitter.on(emitterEventNames.TRANSFER_FAILED, handleTransferFailed);
  emitter.on(emitterEventNames.REFUND_FAILED, handleRefundFailed);
  emitter.on(emitterEventNames.STRIPE_TRANSFER_CREATED, handleTransferCreated);
  emitter.on(emitterEventNames.STRIPE_PAYOUT_PAID, handlePayoutPaid);
};
