const httpStatus = require("http-status");
const { Specialization } = require("../models");
const ApiError = require("../utils/ApiError");

async function addSpecialization(data) {
  const specialization = new Specialization(data);
  const savedSpecialization = await specialization.save();
  return savedSpecialization;
}

async function updateSpecialization(specializationId, specializationData) {
  const updatedSpecialization = await Specialization.findByIdAndUpdate(
    specializationId,
    specializationData,
    {
      new: true,
      runValidators: true,
    }
  );

  if (!updatedSpecialization) {
    throw new ApiError(httpStatus.NOT_FOUND, "Specialization not found");
  }

  return updatedSpecialization;
}

async function deleteSpecialization(specializationId) {
  const specialization = await Specialization.findByIdAndDelete(
    specializationId
  );

  if (!specialization) {
    throw new ApiError(httpStatus.NOT_FOUND, "Specialization not found");
  }

  return true;
}

async function getAllSpecializations() {
  const specializations = await Specialization.find({}).select("name");
  return specializations;
}

async function getSpecializationById(specializationId) {
  const specialization = await Specialization.findById(specializationId);

  return specialization;
}

module.exports = {
  addSpecialization,
  updateSpecialization,
  deleteSpecialization,
  getAllSpecializations,
  getSpecializationById,
};
