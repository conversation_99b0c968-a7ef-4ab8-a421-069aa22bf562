const mongoose = require("mongoose");
const { paginate } = require("./plugins/paginate");

const goalSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
    },

    amount: {
      type: Number,
      default: null,
      min: 1,
      required: true,
    },
    targetAmount: {
      type: Number,
      required: true,
      min: 1,
    },
    targetDate: {
      type: Date,
      default: null,
      required: true,
    },
    monthlyContribution: {
      type: Number,
      required: true,
    },
    isCompleted: {
      type: Boolean,
      default: false,
    },
    completedPercentage: {
      type: Number,
      default: 0.0,
    },
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    lastContributionDate: {
      type: Date,
    },
  },
  { timestamps: true }
);
goalSchema.plugin(paginate);
goalSchema.index({ userId: 1 });
const Goal = mongoose.model("Goal", goalSchema);
module.exports = { Goal };
