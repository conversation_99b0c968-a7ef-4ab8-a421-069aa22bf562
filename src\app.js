const cors = require("cors");
const express = require("express");
const compression = require("compression");
const helmet = require("helmet");
const httpStatus = require("http-status");
const routes = require("./routes/v1");
const morgan = require("./config/morgan");
const config = require("./config/config");
const ApiError = require("./utils/ApiError");
const { errorConverter, errorHandler } = require("./middlewares/error");
const app = express();
require("./events");

// Morgan will handle logging HTTP requests,
// while winston logger will take care of your application-specific logs

if (config.env !== "test") {
  app.use(morgan.successHandler);
  app.use(morgan.errorHandler);
}

// Set security HTTP headers
app.use(helmet());

// Parse JSON request body with special handling for webhook routes
app.use(
  express.json({
    verify: (req, res, buf) => {
      if (req.originalUrl.includes("webhook")) {
        req.rawBody = buf; // Store raw body for Stripe signature verification
      }
    },
  })
);

// Parse URL-encoded request body
app.use(express.json());

app.use(express.urlencoded({ extended: true }));

// Gzip compression
app.use(compression());

// Enable CORS
app.use(
  cors({
    maxAge: 86400, // cache options requests 24hrs
  })
);
app.options("*", cors());
// Reroute all API requests starting with "/v1" route
app.use("/v1", routes);

// Send back a 404 error for any unknown API request
app.use((req, res, next) => {
  next(new ApiError(httpStatus.NOT_FOUND, "Path Not found"));
});

// Convert error to ApiError, if needed
app.use(errorConverter);

app.use(errorHandler);

module.exports = app;
