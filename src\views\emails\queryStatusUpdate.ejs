<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Query Status Update</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f5f5f5;
      color: #333;
    }

    .container {
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
      background-color: #ffffff;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .header {
      background-color: #ffffff;
      padding: 20px;
      text-align: center;
    }

    .logo {
      max-width: 150px;
      height: auto;
    }

    .title {
      color: #00AA9D;
      margin-top: 0;
    }

    .info-box {
      background-color: #f9f9f9;
      border-left: 4px solid #00AA9D;
      padding: 15px;
      margin: 20px 0;
      border-radius: 4px;
    }

    .button {
      display: inline-block;
      background-color: #00AA9D;
      color: white;
      text-decoration: none;
      padding: 10px 20px;
      border-radius: 4px;
      margin-top: 15px;
    }

    .footer {
      margin-top: 30px;
      text-align: center;
      color: #777;
      font-size: 12px;
      border-top: 1px solid #eaeaea;
      padding-top: 20px;
    }

    a {
      color: #00AA9D;
      text-decoration: none;
    }

    p {
      line-height: 1.6;
    }

    @media only screen and (max-width: 600px) {
      .container {
        padding: 20px;
        margin: 0px;
      }

      .title {
        font-size: 22px;
      }

      .header,
      .footer {
        padding: 10px;
      }
    }
  </style>
</head>

<body>
  <div class="container">
    <div class="header">
      <img src="https://moneyowl.s3.eu-north-1.amazonaws.com/public/logos/final+logo+moneyowsl.png"
        alt="Money Owls Logo" class="logo">
    </div>

    <h2 class="title">Query Status Update</h2>

    <p>Hello <%= name %>,</p>

    <p>We're writing to inform you that the status of your query has been updated.</p>

    <div class="info-box">
      <p><strong>Query Details:</strong></p>
      <p>Ticket ID: <%= ticketId %>
      </p>
      <p>Status: <strong>
          <%= status %>
        </strong></p>
      <p>Admin Message: <%= adminMessage %>
      </p>
    </div>

    <p>You can view the full details of your query and any additional information by logging into your Money Owls
      Application.</p>


    <p>If you have any further questions or need additional assistance, please don't hesitate to contact our support
      team at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>

    <div class="footer">
      <p>Thanks,<br />The Money Owls Team</p>
      <p>&copy; <%= new Date().getFullYear() %> Money Owls. All rights reserved.</p>
    </div>
  </div>
</body>

</html>