const Joi = require("joi");

const baseRegisterSchema = {
  name: Joi.string().trim().required(),
  email: Joi.string().email().required(),
  phone: Joi.string()
    .trim()
    .pattern(/^\+?[0-9]{10,15}$/)
    .allow(null), // Phone number pattern validation
  dob: Joi.date().required(), // Ensure ISO date
  gender: Joi.string().required(),
};

const availabilitySchema = Joi.object().keys({
  day: Joi.string()
    .valid(
      "Monday",
      "Tuesday",
      "Wednesday",
      "Thursday",
      "Friday",
      "Saturday",
      "Sunday"
    )
    .required(),
  timeSlots: Joi.array()
    .items(
      Joi.object().keys({
        start: Joi.string().required(),
        end: Joi.string().required(),
      })
    )
    .required(),
});

// User Registration Schema
const registerSchema = Joi.object().keys({
  ...baseRegisterSchema,

  profilePic: Joi.object()
    .keys({
      key: Joi.string(), // Ensure the key is a URI
      url: Joi.string().uri(), // Ensure the URL is a valid URI
    })
    .allow(null), // Allow profile picture to be null

  firebaseUid: Joi.string().required(),
  firebaseSignInProvider: Joi.string().required(),
  latitude: Joi.number().required().allow(null),
  longitude: Joi.number().required().allow(null),
  code: Joi.string(),
  dob: Joi.date().required(),
  profession: Joi.string().required(), // Required for users, but will be omitted for advisers
  address: Joi.object()
    .keys({
      line1: Joi.string().trim().optional(),
      line2: Joi.string().trim().optional(),
      city: Joi.string().trim().required(),
      postalCode: Joi.string().trim().required(),
      state: Joi.string().trim().required(),
      country: Joi.string().trim().required(),
    })
    .required(),
});

// Adviser-specific schema
const adviserSchema = Joi.object().keys({
  sessionCharge: Joi.number().min(1).required(),
  bio: Joi.string().trim(),
  experience: Joi.number().min(0).required(), // Experience must be positive
  specialization: Joi.array()
    .items(Joi.string().trim().required()) // Array of strings, each trimmed and required
    .min(1)
    .required(),
  availability: Joi.array().items(availabilitySchema).required(), // List of available days and time slots
});

// Use this for user registration (includes profession)
const registerUser = {
  body: registerSchema,
};

// Use this for adviser registration (excludes profession)
const registerAdviser = {
  body: registerSchema
    .fork(["profession"], (schema) => schema.forbidden()) // Profession is removed for adviser
    .concat(adviserSchema), // Merge user and adviser-specific fields
};

const registerAdmin = {
  name: Joi.string().trim().required(),
  email: Joi.string().email().required(),
  phone: Joi.string()
    .trim()
    .pattern(/^\+?[0-9]{10,15}$/)
    .allow(null),
  firebaseUid: Joi.string().required(),
  firebaseSignInProvider: Joi.string().required(),
};

const registerAdviserByAdmin = {
  body: registerSchema
    .fork(["profession", "firebaseUid", "firebaseSignInProvider"], (schema) =>
      schema.forbidden()
    ) // Remove 'profession', 'firebaseUid', 'firebaseSignInProvider'
    .concat(adviserSchema) // Merge user and adviser-specific fields
    .keys({
      password: Joi.string()
        .min(8) // Minimum password length (you can adjust the length as needed)
        .required() // Make password required
        .pattern(/[a-zA-Z0-9]{3,30}/) // Optional: add a custom regex to validate password complexity
        .messages({
          "string.empty": "Password cannot be empty",
          "string.min": "Password must be at least 8 characters long",
        }),
    }), // Add 'password' field
};

const registerUserByAdmin = {
  body: registerSchema
    .fork(["firebaseUid", "firebaseSignInProvider"], (schema) =>
      schema.forbidden()
    )
    .keys({
      password: Joi.string()
        .min(8) // Minimum password length (you can adjust the length as needed)
        .required() // Make password required
        .pattern(/[a-zA-Z0-9]{3,30}/) // Optional: add a custom regex to validate password complexity
        .messages({
          "string.empty": "Password cannot be empty",
          "string.min": "Password must be at least 8 characters long",
        }),
    }),
};

module.exports = {
  registerUser,
  registerAdviser,
  registerAdmin,
  registerAdviserByAdmin,
  registerUserByAdmin,
};
