const Joi = require("joi");

const financialHealthValidationSchema = {
  body: Joi.object().keys({
    maritalStatus: Joi.string()
      .valid("Single", "Married", "Divorced", "Widowed", "Cohabitant")
      .required()
      .messages({
        "any.only":
          "Marital status must be one of [Single, Married, Divorced, Widowed, Cohabitant].",
      }),

    dependents: Joi.number().integer().min(0).required().messages({
      "number.base": "Dependents must be a number.",
      "number.min": "Dependents must be 0 or greater.",
      "number.empty": "Dependents are required.",
    }),

    annualIncome: Joi.number().min(0).required().messages({
      "number.base": "Annual income must be a number.",
      "number.min": "Annual income must be 0 or greater.",
      "number.empty": "Annual income is required.",
    }),

    savings: Joi.number().min(0).required().messages({
      "number.base": "Savings must be a number.",
      "number.min": "Savings must be 0 or greater.",
      "number.empty": "Savings are required.",
    }),

    investments: Joi.number().min(0).required().messages({
      "number.base": "Investments must be a number.",
      "number.min": "Investments must be 0 or greater.",
      "number.empty": "Investments are required.",
    }),

    pensions: Joi.number().min(0).required().messages({
      "number.base": "Pensions must be a number.",
      "number.min": "Pensions must be 0 or greater.",
      "number.empty": "Pensions are required.",
    }),

    debt: Joi.number().min(0).required().messages({
      "number.base": "Debt must be a number.",
      "number.min": "Debt must be 0 or greater.",
      "number.empty": "Debt is required.",
    }),

    propertyOwnership: Joi.string()
      .valid("Own", "Rent", "Other")
      .required()
      .messages({
        "any.only": "Property ownership must be one of [Own, Rent, Other].",
        "string.empty": "Property ownership is required.",
      }),

    insurances: Joi.object({
      lifeInsurance: Joi.boolean(),
      criticalIllnessCover: Joi.boolean(),
      incomeProtection: Joi.boolean(),
      privateMedical: Joi.boolean(),
    }),

    monthlyNetIncome: Joi.number().min(0).required().messages({
      "number.base": "Monthly net income must be a number.",
      "number.min": "Monthly net income must be 0 or greater.",
      "number.empty": "Monthly net income is required.",
    }),

    monthlyExpenses: Joi.number().min(0).required().messages({
      "number.base": "Monthly expenses must be a number.",
      "number.min": "Monthly expenses must be 0 or greater.",
      "number.empty": "Monthly expenses are required.",
    }),

    monthlySavings: Joi.number().min(0).required().messages({
      "number.base": "Monthly savings must be a number.",
      "number.min": "Monthly savings must be 0 or greater.",
      "number.empty": "Monthly savings are required.",
    }),
  }),
};

module.exports = {
  financialHealthValidationSchema,
};
