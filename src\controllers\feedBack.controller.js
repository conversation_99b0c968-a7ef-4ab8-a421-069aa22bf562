const httpStatus = require("http-status");
const { appFeedBackService } = require("../services");
const ApiError = require("../utils/ApiError");
const catchAsync = require("../utils/catchAsync");
const { getPaginateConfig } = require("../utils/queryPHandler");

//app reviews/feedbacks

const reviewApp = catchAsync(async (req, res) => {
  const { rating, review } = req.body;
  const existingReview = await appFeedBackService.getReviewByUser(req.user._id);
  if (existingReview) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Review already added");
  }
  const feedback = {
    user: req.user._id,
    rating,
    review,
  };
  const data = await appFeedBackService.addReview(feedback);
  res
    .status(httpStatus.OK)
    .json({ status: true, data, message: "Review added successfully" });
});

const getReviews = catchAsync(async (req, res) => {
  const { filters, options } = getPaginateConfig(req.query);

  const reviews = await appFeedBackService.getAllReviews(filters, options);

  if (!reviews.results || reviews.results.length === 0) {
    return res
      .status(httpStatus.OK)
      .json({ status: true, message: "No reviews found " });
  }

  return res
    .status(httpStatus.OK)
    .json({ status: true, reviews, message: "Reviews fetched" });
});

module.exports = { reviewApp, getReviews };
