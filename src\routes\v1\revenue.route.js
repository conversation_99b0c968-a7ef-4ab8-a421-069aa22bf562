const express = require("express");
const validate = require("../../middlewares/validate");
const { firebaseAuth } = require("../../middlewares/firebaseAuth");
const { adminController, revenueController } = require("../../controllers");
const { fileUploadService } = require("../../microservices");

const router = express.Router();

// router.get(
//   "/monthly",
//   firebaseAuth("Admin"),
//   revenueController.totalRevenueForYear
// );

// Get platform commission data
router.get(
  "/commission",
  firebaseAuth("Admin"),
  revenueController.getPlatformCommission
);

// Get subscription revenue growth
router.get(
  "/subscription-growth",
  firebaseAuth("Admin"),
  revenueController.subscriptionRevenueGrowth
);
//new added altered

router.get(
  "/total-revenue",
  firebaseAuth("Admin"),
  revenueController.getTotalNetRevenue
);
router.get(
  "/total-monthly-revenue",
  firebaseAuth("Admin"),
  revenueController.getMonthlyRevenue
);

router.get(
  "/comparison",
  firebaseAuth("Admin"),
  revenueController.getRevenueComparison
);

module.exports = router;
