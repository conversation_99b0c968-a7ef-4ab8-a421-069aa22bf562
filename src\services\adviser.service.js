const httpStatus = require("http-status");
const { Adviser } = require("../models");
const ApiError = require("../utils/ApiError");
const moment = require("moment");
const { userTypes, appDefaults } = require("../constants");
const { fileUploadService } = require("../microservices");

async function getAllAdvisers(filters = {}, options = {}, req) {
  let query = { isVerified: true, isDeleted: false, isBlockedByAdmin: false };
  // let query = {};

  if (req.user.__t === userTypes.ADMIN) {
    query = {};

    if (filters.status === "Active") {
      query.isDeleted = false;
    }
    if (filters.status === "Inactive") {
      query.isDeleted = true;
    }
    if (filters.isVerified === true) {
      query.isVerified = true;
    }
    if (filters.isVerified === false) {
      query.isVerified = false;
    }
  }

  if (filters.specialization) {
    const specializations = Array.isArray(filters.specialization)
      ? filters.specialization
      : [filters.specialization];

    query.specialization = {
      $in: specializations.map((spec) => new RegExp(spec, "i")),
    };
    // console.log("Added specialization filter:", query.specialization);
  }

  if (filters.experience) {
    query.experience = { $gte: Number(filters.experience) };
    //console.log("Added experience filter:", query.experience);
  }

  if (filters.averageRating) {
    query.averageRating = { $gte: Number(filters.averageRating) };
    // console.log("Added averageRating filter:", query.averageRating);
  }

  if (filters.dateAvailable) {
    const userTimezone = req.headers?.timezone || appDefaults.TIMEZONE;
    const dateAvailable = moment.tz(filters.dateAvailable, userTimezone);
    const dayOfWeek = dateAvailable.format("dddd");

    // console.log(
    //   `User-provided date: ${filters.dateAvailable} (${userTimezone})`
    // );
    // console.log(`Converted to day of the week: ${dayOfWeek}`);

    query.availability = {
      $elemMatch: {
        day: dayOfWeek,
      },
    };
  }

  if (filters.name) {
    query.name = { $regex: filters.name, $options: "i" };
  }

  const sortField = options.sortBy; // Default to averageRating if no sortBy
  const sortOrder = options.sortOrder === "desc" ? -1 : 1; // Desc or Asc

  options.sort = { [sortField]: sortOrder, _id: sortOrder };

  // Pagination
  options.page = options.page || 1;
  options.limit = options.limit || 10;

  const advisers = await Adviser.paginate(query, options);

  return advisers;
}

async function getAdviserDetails(query, populateFields = []) {
  let adviserQuery = Adviser.findOne(query).lean(); // Use lean() for better performance

  // Dynamically populate fields if provided
  populateFields.forEach((field) => {
    adviserQuery = adviserQuery.populate(field);
  });

  const adviser = await adviserQuery.exec();

  if (!adviser) {
    throw new ApiError(httpStatus.NOT_FOUND, "Adviser not found");
  }

  return adviser;
}

async function createAdviser(data) {
  const adviser = await Adviser.create(data);

  return adviser;
}

async function updateAdviser(id, updateData) {
  // Ensure name is lowercase if it's being updated
  if (updateData.name) {
    updateData.name = updateData.name.toLowerCase();
  }

  // Remove any undefined values to prevent accidental field deletion
  // Object.keys(updateData).forEach((key) => {
  //   if (
  //     updateData[key] === undefined ||
  //     updateData[key] === null ||
  //     updateData[key] === ""
  //   ) {
  //     delete updateData[key];
  //   }
  // });

  const adviser = await Adviser.findByIdAndUpdate(id, updateData, {
    new: true,
    runValidators: true,
  });

  return adviser;
}

async function update(query) {
  return await Adviser.findByIdAndUpdate(query);
}

async function deleteAdviser(id) {
  return await Adviser.findByIdAndUpdate(
    id,
    { isDeleted: true },
    { new: true }
  );
}

async function blockAdviser(id) {
  return await Adviser.findByIdAndUpdate(
    id,
    { isBlockedByAdmin: true },
    { new: true }
  );
}

async function unBlockAdviser(id) {
  return await Adviser.findByIdAndUpdate(
    id,
    { isBlockedByAdmin: false },
    { new: true }
  );
}

async function calculateAvgRating(adviserId) {
  const [result] = await Adviser.aggregate([
    { $match: { _id: adviserId } },
    {
      $lookup: {
        from: "reviews",
        localField: "reviews",
        foreignField: "_id",
        as: "reviewDetails",
      },
    },
    {
      $project: {
        averageRating: { $avg: "$reviewDetails.rating" },
      },
    },
  ]);

  return result?.averageRating || 0;
}

async function deleteDocument(adviser, documentId) {
  // Check if the document exists in the adviser's documents
  const document = adviser.documents.find(
    (doc) => doc._id.toString() === documentId
  );

  if (!document) {
    return null;
  }

  // If the document is approved, don't allow deletion
  if (document.status === "approved") {
    throw new ApiError(
      httpStatus.FORBIDDEN,
      "Approved documents cannot be deleted. Please contact support for assistance."
    );
  }

  // If the document is not approved, proceed with deletion

  // Delete the document from S3 as well
  try {
    await fileUploadService.s3Delete(document.key);
  } catch (error) {
    console.error(`Failed to delete document from S3: ${error.message}`);
    // Continue with the process even if S3 deletion fails
  }

  const updatedUser = await Adviser.findOneAndUpdate(
    { _id: adviser._id },
    { $pull: { documents: { _id: documentId } } },
    { new: true }
  );

  return updatedUser;
}

async function countAdvisers() {
  return await Adviser.countDocuments({ isVerified: true });
}

async function getAdviserAvaliability(id) {
  const adviser = await Adviser.findById(id).select("availability").lean();
  return adviser;
}

async function activeAdviser(id) {
  return await Adviser.findByIdAndUpdate(
    id,
    { isDeleted: false },
    { new: true }
  );
}

const getAdvisersWithDetails = async (filters, options) => {
  // console.log(filters);
  const matchStage = { ...filters };

  // Apply filters
  if (filters.status === "Active") {
    delete matchStage.status;
    matchStage.isDeleted = false;
  }
  if (filters.status === "Inactive") {
    delete matchStage.status;
    matchStage.isDeleted = true;
  }
  if (filters.keyword) {
    delete matchStage.keyword;
    matchStage.$or = [
      { name: { $regex: filters.keyword, $options: "i" } },
      { email: { $regex: filters.keyword, $options: "i" } },
      { phone: { $regex: filters.keyword, $options: "i" } },
    ];
  }

  const limit = parseInt(options.limit) || 10;
  const skip = parseInt(options.page > 0 ? (options.page - 1) * limit : 0);
  const sortBy = options.sortBy || "createdAt"; // Default to sorting by name
  const sortOrder = options.sortOrder === "asc" ? 1 : -1;

  const aggregationPipeline = [
    { $match: matchStage },

    // Lookup next appointment
    {
      $lookup: {
        from: "appointments",
        let: { adviserId: "$_id" },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ["$adviser", "$$adviserId"] },
                  { $gte: ["$date", new Date()] },
                ],
              },
            },
          },
          { $sort: { date: 1 } },
          { $limit: 1 },
        ],
        as: "nextAppointment",
      },
    },
    {
      $addFields: {
        nextBookingDate: {
          $ifNull: [{ $arrayElemAt: ["$nextAppointment.date", 0] }, null],
        },
      },
    },
    { $project: { nextAppointment: 0 } },
    { $sort: { [sortBy]: sortOrder } },

    // Pagination using facet
    {
      $facet: {
        results: [{ $skip: skip }, { $limit: limit }],
        totalCount: [{ $count: "count" }],
      },
    },
  ];

  // console.log(aggregationPipeline);
  const result = await Adviser.aggregate(aggregationPipeline);

  const results = result[0].results;
  const totalResults = result[0].totalCount[0]?.count || 0;
  const totalPages = Math.ceil(totalResults / limit); // Calculate total pages

  return {
    results,
    page: parseInt(options.page) || 1,
    limit,
    totalResults,
    totalPages, // Include totalPages in the response
  };
};

async function getAdvisers(filters, options) {
  const advisers = await Adviser.paginate(filters, options);
  return advisers;
}

module.exports = {
  getAllAdvisers,
  getAdviserDetails,
  createAdviser,
  updateAdviser,
  deleteAdviser,
  calculateAvgRating,
  deleteDocument,
  countAdvisers,
  update,
  getAdviserAvaliability,
  activeAdviser,
  getAdvisersWithDetails,
  getAdvisers,
  blockAdviser,
  unBlockAdviser,
};
