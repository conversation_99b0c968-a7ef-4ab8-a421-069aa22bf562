const mongoose = require("mongoose");
const { paginate } = require("./plugins/paginate");

const refundSchema = new mongoose.Schema(
  {
    appointmentId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Appointment",
      required: true,
    },

    paymentIntentId: { type: String, required: true },
    refundId: { type: String, default: null },
    amount: { type: Number, required: true },
    status: {
      type: String,
      enum: ["pending", "succeeded", "failed", "initiated"],
      default: "pending",
    },
    isIntiatedByAdmin: { type: Boolean, default: false },
    isIntiatedBySystem: { type: Boolean, default: false },
    errorMessage: { type: String, default: null },
    reason: { type: String, required: true },
  },
  { timestamps: true }
);
refundSchema.plugin(paginate);
refundSchema.index({ appointmentId: 1, paymentIntentId: 1 });
const Refund = mongoose.model("Refund", refundSchema);
module.exports = { Refund };
