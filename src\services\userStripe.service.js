const { CustomerStripe } = require("../models");

async function createStripeCustomer(customerId, userId) {
  const stripeCustomerDetails = new CustomerStripe({
    stripeCustomerId: customerId,
    user: userId,
  });
  const savedStripeCustomerDetails = await stripeCustomerDetails.save();
  return savedStripeCustomerDetails;
}

async function deleteStripeCustomer(customerId) {
  await CustomerStripe.findByIdAndDelete(customerId);
  return true;
}

async function getAllStripeCustomers(filter, options = {}) {
  const customers = await CustomerStripe.paginate(filter, options);
  return customers;
}

async function getOne(filter) {
  const customer = await CustomerStripe.findOne(filter);
  return customer;
}

module.exports = {
  createStripeCustomer,
  deleteStripeCustomer,
  getAllStripeCustomers,
  getOne,
};
