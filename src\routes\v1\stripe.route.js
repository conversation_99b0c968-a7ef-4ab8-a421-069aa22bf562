const express = require("express");
const validate = require("../../middlewares/validate");
const { firebaseAuth } = require("../../middlewares/firebaseAuth");
const { stripeController } = require("../../controllers");
const { stripeValidation } = require("../../validations");
const { fileUploadService } = require("../../microservices");

const router = express.Router();

router.delete(
  "/connect/account/delete",
  firebaseAuth("Admin,Adviser"),
  validate(stripeValidation.deleteStripeAccount),
  stripeController.deleteAdviserStripeConnectAccount
);

router.post(
  "/account/create",
  firebaseAuth("Adviser"),
  // validate(stripeValidation.createStripeAccount),
  stripeController.createAdviserConnectedAccount
);

router.get(
  "/account/status",
  firebaseAuth("Adviser"),
  validate(stripeValidation.checkOnboardingStatus),
  stripeController.checkAdviserOnboardingStatus
);

router.get(
  "/account/onboard",
  firebaseAuth("Adviser"),
  validate(stripeValidation.createOnboardingLink),
  stripeController.createAccountOnboardingLink
);

router.post(
  "/payment/:id",
  firebaseAuth("User"),
  validate(stripeValidation.processDirectPayment),
  stripeController.processDirectPayment
);

router.post(
  "/refund",
  firebaseAuth("Admin"),
  validate(stripeValidation.refundPayment),
  stripeController.refundPayment
);

router.post(
  "/transfer/:appointmentId",
  firebaseAuth("Admin"),
  validate(stripeValidation.payForFreeSession),
  stripeController.payForFreeSession
);

// Card management routes
router.get("/cards", firebaseAuth("User"), stripeController.getAllSavedCards);

router.post(
  "/card/save",
  firebaseAuth("User"),
  validate(stripeValidation.saveCard),
  stripeController.saveCard
);

router.post(
  "/card/default",
  firebaseAuth("User"),
  validate(stripeValidation.makeCardDefault),
  stripeController.makeCardDefault
);

router.post(
  "/card/change-default",
  firebaseAuth("User"),
  validate(stripeValidation.changeDefaultCard),
  stripeController.changeDefaultCard
);

// Subscription routes
router.post(
  "/subscription/create",
  firebaseAuth("User"),
  validate(stripeValidation.createSubscription),
  stripeController.createSubscription
);

router.post(
  "/checkout/:appointmentId",
  firebaseAuth("User"),
  validate(stripeValidation.createCheckoutSession),
  stripeController.createCheckoutSession
);

router.post(
  "/subscription/cancel",
  firebaseAuth("User"),
  stripeController.cancelSubscription
);

router.post(
  "/customer/account/create",
  firebaseAuth("User"),
  stripeController.createCustomerAccount
);
router.get(
  "/adviser/dashboard",
  firebaseAuth("Adviser"),
  stripeController.expressDashboardLink
);
// Webhook routes - no validation needed as they are handled by Stripe
router.post("/webhook", stripeController.handleStripeEvent);

router.post("/connect_webhook", stripeController.processStripeConnectWebhook);

module.exports = router;
