const express = require("express");

const validate = require("../../middlewares/validate");
const { firebaseAuth } = require("../../middlewares/firebaseAuth");
const { authValidation } = require("../../validations");
const { fileUploadService } = require("../../microservices");
const { authController, userController } = require("../../controllers");

const router = express.Router();

router.post(
  "/register",
  firebaseAuth("User"),
  fileUploadService.multerUpload.single("profilePic"),
  // validate(authValidation.registerUser),
  authController.registerUser
);

router.post(
  "/register-adviser",
  firebaseAuth("Adviser"),
  fileUploadService.multerUpload.single("profilePic"),
  // validate(authValidation.registerAdviser),
  authController.registerUser
);

router.post(
  "/register-admin",
  firebaseAuth("Admin"),
  validate(authValidation.registerAdmin),
  authController.registerUser
);

router.post("/login", firebaseAuth("All"), authController.loginUser);

router.patch(
  "/activate-account/:id?",
  firebaseAuth("All"),
  authController.activeUser
);

router.post("/generateToken", authController.generateToken);
router.post("/emailcheck", authController.emailCheck);
router.post("/resetCheck", authController.resetPasswordEmailCheck);

module.exports = router;
