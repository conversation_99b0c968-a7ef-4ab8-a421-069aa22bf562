const Joi = require("joi");
const { objectId } = require("./custom.validation");

const processDirectPayment = {
  params: Joi.object().keys({
    id: Joi.string().custom(objectId).required().messages({
      "string.empty": "Appointment ID is required",
      "any.required": "Appointment ID is required",
      "string.pattern.base": "Appointment ID must be a valid MongoDB ObjectId",
    }),
  }),
  body: Joi.object().keys({
    cardId: Joi.string().optional().messages({
      "string.base": "Card ID must be a string",
    }),
  }),
};

const refundPayment = {
  body: Joi.object().keys({
    bookingId: Joi.string().required().messages({
      "string.empty": "Booking ID is required",
      "any.required": "Booking ID is required",
    }),
    amount: Joi.number().min(0).optional().messages({
      "number.base": "Amount must be a number",
      "number.min": "Amount must be greater than 0",
    }),
    reason: Joi.string().messages({
      "string.empty": "reason is required",
      "any.required": "reason is required",
    }),
  }),
};

const payForFreeSession = {
  params: Joi.object().keys({
    appointmentId: Joi.string().custom(objectId).required().messages({
      "string.empty": "Appointment ID is required",
      "any.required": "Appointment ID is required",
      "string.pattern.base": "Appointment ID must be a valid MongoDB ObjectId",
    }),
  }),
  body: Joi.object().keys({
    amount: Joi.number().required().min(1).messages({
      "number.base": "Amount must be a number",
      "number.min": "Amount must be at least 1",
      "any.required": "Amount is required",
    }),
  }),
};

const saveCard = {
  body: Joi.object().keys({
    token: Joi.string().required().messages({
      "string.empty": "Token is required",
      "any.required": "Token is required",
    }),
  }),
};

const makeCardDefault = {
  body: Joi.object().keys({
    cardId: Joi.string().required().messages({
      "string.empty": "Card ID is required",
      "any.required": "Card ID is required",
    }),
  }),
};

const changeDefaultCard = {
  body: Joi.object().keys({
    cardId: Joi.string().required().messages({
      "string.empty": "Card ID is required",
      "any.required": "Card ID is required",
    }),
  }),
};

const createSubscription = {
  body: Joi.object().keys({
    productId: Joi.string().custom(objectId).required().messages({
      "string.empty": "Product ID is required",
      "any.required": "Product ID is required",
      "string.pattern.base": "Product ID must be a valid MongoDB ObjectId",
    }),
    cardId: Joi.string().optional().messages({
      "string.base": "Card ID must be a string",
    }),
  }),
};

const createCheckoutSession = {
  params: Joi.object().keys({
    appointmentId: Joi.string().custom(objectId).required().messages({
      "string.empty": "Appointment ID is required",
      "any.required": "Appointment ID is required",
      "string.pattern.base": "Appointment ID must be a valid MongoDB ObjectId",
    }),
  }),
};

module.exports = {
  processDirectPayment,
  refundPayment,
  payForFreeSession,
  saveCard,
  makeCardDefault,
  changeDefaultCard,
  createSubscription,
  createCheckoutSession,
};
