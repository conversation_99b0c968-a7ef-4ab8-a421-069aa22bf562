const admin = require("firebase-admin");

function constructPayload(notification, data) {
  // console.log("in construct", notification, "--", data);
  return {
    notification,
    data,
    android: {
      priority: "high",
      notification: { channel_id: "high_importance_channel" },
    },
  };
}

async function sendToTopic(topic, notification, data, opts = {}) {
  const messaging = admin.messaging();
  const { groupKey = null } = opts;

  // Base payload
  const payload = {
    topic,
    notification: {
      title: notification.title || "",
      body: notification.body || "",
    },
    data,
    android: {
      priority: "high",
      collapseKey: groupKey || undefined,

      notification: {
        channel_id: "high_importance_channel",
        sound: "default",
        defaultSound: true,

        // only set these if grouping is on
        ...(groupKey && { tag: groupKey }), // optional: replaces existing with same tag
      },
    },
    apns: {
      headers: {
        "apns-priority": "10",
        ...(groupKey && { "apns-collapse-id": groupKey }), // iOS equivalent of collapse<PERSON><PERSON>
      },
      payload: {
        aps: {
          sound: "default",
          "mutable-content": 1,
          // iOS thread‐grouping
          ...(group<PERSON>ey && { "thread-id": groupK<PERSON> }),
        },
      },
    },
  };

  // Only include image if it's present and a valid URL
  if (notification.image) {
    payload.notification.image = notification.image;
    payload.android.notification.image = notification.image;
    payload.apns.fcm_options = { image: notification.image };
  }

  try {
    const response = await messaging.send(payload);
    return true;
  } catch (err) {
    return false;
  }
}

async function sendMulticast(deviceTokens, notification, data) {
  const messaging = admin.messaging();
  const payload = {
    tokens: deviceTokens,
    ...constructPayload(notification, data),
  };
  try {
    await messaging
      .sendEachForMulticast(payload)
      .then((res) =>
        console.log(
          "🚀 ~ file: notification.service.js:42 ~ sendMulticast ~ res:",
          res
        )
      )
      .catch((err) =>
        console.log(
          "🚀 ~ file: notification.service.js:45 ~ sendMulticast ~ err:",
          err
        )
      );
    return true;
  } catch {
    return false;
  }
}

async function unsubscribeFromTopics(token, topics) {
  return Promise.all(
    topics.map(async (topic) =>
      admin.messaging().unsubscribeFromTopic(token, topic)
    )
  );
}

async function subscribeToTopics(token, topics) {
  return Promise.allSettled(
    topics.map(async (topic) =>
      admin.messaging().subscribeToTopic(token, topic)
    )
  );
}

module.exports = {
  sendToTopic,
  unsubscribeFromTopics,
  subscribeToTopics,
};
