const Joi = require("joi");

const { objectId } = require("./custom.validation");

const rejectAdviserDoc = {
  params: Joi.object({
    adviserId: Joi.string().custom(objectId).required().messages({
      "string.base": "Adviser ID must be a string.",
      "string.empty": "Adviser ID is required.",
      "any.required": "Adviser ID is required.",
    }),
    docId: Joi.string().custom(objectId).required().messages({
      "string.base": "Document ID must be a string.",
      "string.empty": "Document ID is required.",
      "any.required": "Document ID is required.",
    }),
  }),

  body: Joi.object({
    rejectReason: Joi.string().required().min(3).messages({
      "string.base": "Reject reason must be a string.",
      "string.empty": "Reject reason is required.",
      "string.min": "Reject reason must be at least 3 characters long.",
      "any.required": "Reject reason is required.",
    }),
  }),
};

const approveAdviserDoc = {
  params: Joi.object({
    adviserId: Joi.string().custom(objectId).required().messages({
      "string.base": "Adviser ID must be a string.",
      "string.empty": "Adviser ID is required.",
      "any.required": "Adviser ID is required.",
    }),
    docId: Joi.string().custom(objectId).required().messages({
      "string.base": "Document ID must be a string.",
      "string.empty": "Document ID is required.",
      "any.required": "Document ID is required.",
    }),
  }),
};

/**
 * Validation schema for sending adviser invitations
 */
const inviteAdviser = {
  body: Joi.object({
    email: Joi.string().email().required().messages({
      "string.base": "Email must be a string.",
      "string.empty": "Email is required.",
      "string.email": "Please provide a valid email address.",
      "any.required": "Email is required.",
    }),
    name: Joi.string().allow("", null).messages({
      "string.base": "Name must be a string.",
    }),
  }),
};

module.exports = { rejectAdviserDoc, approveAdviserDoc, inviteAdviser };
