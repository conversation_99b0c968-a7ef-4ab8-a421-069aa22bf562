const { SingleSessionProduct } = require("../models");

const createSingleSessionProduct = async (data) => {
  return await SingleSessionProduct.create(data);
};

const getAllActiveSessions = async (filter = {}) => {
  return await SingleSessionProduct.find({ isActive: true, ...filter });
};

const getSessionById = async (id) => {
  return await SingleSessionProduct.findById(id);
};

const updateSession = async (id, updateData) => {
  return await SingleSessionProduct.findByIdAndUpdate(id, updateData, {
    new: true,
    runValidators: true,
  });
};

const deleteSessionProduct = async (id) => {
  return await SingleSessionProduct.findByIdAndDelete(id);
};

const getSessionByDuration = async (duration) => {
  return await SingleSessionProduct.findOne({
    duration: duration,
    isActive: true,
  });
};
module.exports = {
  createSingleSessionProduct,
  getAllActiveSessions,
  getSessionById,
  updateSession,
  deleteSessionProduct,
  getSessionByDuration,
};
