const httpStatus = require("http-status");
const ApiError = require("../utils/ApiError");
const catchAsync = require("../utils/catchAsync");
const { specializationService } = require("../services");

const addSpecialization = catchAsync(async (req, res) => {
  const savedSpecialization = await specializationService.addSpecialization(
    req.body
  );
  return res.status(httpStatus.CREATED).json({
    message: "Specialization added successfully",
    data: savedSpecialization,
  });
});

const updateSpecialization = catchAsync(async (req, res) => {
  const specializationId = req.params.id;
  const updatedSpecialization =
    await specializationService.updateSpecialization(
      specializationId,
      req.body
    );

  if (!updatedSpecialization) {
    throw new ApiError(httpStatus.NOT_FOUND, "Specialization not found");
  }

  return res.status(httpStatus.OK).json({
    message: "Specialization updated successfully",
    data: updatedSpecialization,
  });
});

const deleteSpecialization = catchAsync(async (req, res) => {
  const specializationId = req.params.id;
  const specialization =
    await specializationService.deleteSpecialization(specializationId);

  if (!specialization) {
    throw new ApiError(httpStatus.NOT_FOUND, "Specialization not found");
  }

  return res.status(httpStatus.NO_CONTENT).send();
});

const getAllSpecializations = catchAsync(async (req, res) => {
  const specializations = await specializationService.getAllSpecializations();
  if (!specializations || specializations.length === 0) {
    throw new ApiError(httpStatus.NOT_FOUND, "Specialization not found");
  }

  return res.status(httpStatus.OK).json({
    message: "Specializations retrieved successfully",
    data: specializations,
  });
});

const getSpecializationById = catchAsync(async (req, res) => {
  const specializationId = req.params.id;
  const specialization =
    await specializationService.getSpecializationById(specializationId);

  if (!specialization) {
    throw new ApiError(httpStatus.NOT_FOUND, "Specialization not found");
  }

  return res.status(httpStatus.OK).json({
    message: "Specialization retrieved successfully",
    data: specialization,
  });
});

module.exports = {
  addSpecialization,
  updateSpecialization,
  deleteSpecialization,
  getAllSpecializations,
  getSpecializationById,
};
