const mongoose = require("mongoose");
const { paginate } = require("./plugins/paginate");

const baseFields = {
  name: {
    type: String,
    trim: true,
    required: true,
    lowercase: true,
  },
  phone: {
    type: String,
    trim: true,
    unique: true,
    sparse: true,
    default: null,
  },
  email: {
    type: String,
    trim: true,
    unique: true,
    required: true,
  },

  firebaseUid: {
    type: String,
    required: true,
    unique: true,
  },
  firebaseSignInProvider: {
    type: String,
    required: true,
  },
  appNotificationsLastSeenAt: {
    type: Date,
    default: Date.now,
  },
  isDeleted: {
    type: Boolean,
    default: false,
    require: true,
  },
};

const mainSchema = new mongoose.Schema(
  {
    ...baseFields,
  },
  {
    timestamps: true,
    discriminatorKey: "__t", // discriminator key for distinguishing between User and Adviser
    methods: {
      isUser() {
        return this.__t === "User";
      },
      isAdviser() {
        return this.__t === "Adviser";
      },
    },
  }
);

// User Schema - inherits from Admin
const userSchema = new mongoose.Schema(
  {
    ...baseFields,
    profilePic: {
      type: {
        key: String,
        url: String,
      },
      default: null,
    },
    dob: {
      type: Date,
      required: true,
    },
    age: {
      type: Number,
      default: null,
    },
    gender: {
      type: String,
      required: true,
      enum: ["male", "female", "other"],
    },
    isGoaladded: {
      type: Boolean,
      required: true,
      default: false,
    },
    goals: [
      {
        type: mongoose.SchemaTypes.ObjectId,
        ref: "Goal",
      },
    ],
    isFinancialHealthadded: {
      type: Boolean,
      required: true,
      default: false,
    },
    financialHealth: {
      type: mongoose.SchemaTypes.ObjectId,
      ref: "FinancialHealth",
    },
    appointments: [
      {
        type: mongoose.SchemaTypes.ObjectId,
        ref: "Appointment",
      },
    ],
    profession: {
      type: String,
      required: true,
      trim: true,
    },
    favorites: [
      {
        type: mongoose.SchemaTypes.ObjectId,
        ref: "Adviser",
      },
    ],
    latitude: {
      type: Number,
      default: null,
    },
    longitude: {
      type: Number,
      default: null,
    },
    referralCode: {
      type: String,
      unique: true,
      required: true,
    },
    referredBy: {
      type: mongoose.SchemaTypes.ObjectId,
      ref: "User",
      default: null,
    },
    referralCount: {
      type: Number,
      default: 0,
    },
    freeSessions: {
      type: Number,
      default: 0,
    },
    address: {
      line1: { type: String }, // Street name or building number
      line2: { type: String }, // Optional second address line
      city: { type: String }, // City or town
      postalCode: { type: String }, // Postal code (ZIP code)
      state: { type: String }, // State or region
      country: { type: String }, // Country code, default to "GB" for UK
    },
    isBlockedByAdmin: {
      type: Boolean,
      required: true,
      default: false,
    },
  },

  {
    timestamps: true,
    discriminatorKey: "__t",
    methods: {
      isUser() {
        return this.__t === "User";
      },
      isAdviser() {
        return this.__t === "Adviser";
      },
    },
  }
);

// Adviser Schema - inherits from Admin
const adviserSchema = new mongoose.Schema(
  {
    ...baseFields,
    profilePic: {
      type: {
        key: String,
        url: String,
      },
      default: null,
    },
    dob: {
      type: Date,
      required: true,
    },
    age: {
      type: Number,
      default: null,
    },
    gender: {
      type: String,
      required: true,
      enum: ["male", "female", "other"],
    },
    isVerified: {
      type: Boolean,
      required: true,
      default: false,
    },
    averageRating: {
      type: Number,
      default: 0.0,
      min: 0,
      max: 5,
      required: true,
    },
    previousRank: {
      type: Number,
      default: 0,
    },
    education: {
      type: String,
      default: "",
    },
    experience: {
      type: Number,
      required: true,
    },
    specialization: [
      {
        type: String,
        trim: true,
      },
    ],
    bio: {
      type: String,
      default: null,
    },
    reviews: [{ type: mongoose.SchemaTypes.ObjectId, ref: "Review" }],

    documents: [
      {
        key: {
          type: String,
          required: true,
        },
        url: {
          type: String,
          required: true,
        },
        status: {
          type: String,
          enum: ["pending", "approved", "rejected"],
          default: "pending",
        },

        fileType: {
          type: String,
        },
        rejectReason: { type: String },
      },
    ],

    availability: [
      {
        day: {
          type: String,
          enum: [
            "Monday",
            "Tuesday",
            "Wednesday",
            "Thursday",
            "Friday",
            "Saturday",
            "Sunday",
          ],
          required: true,
        },
        timeSlots: [
          {
            start: {
              type: String,
              required: true,
            },
            end: {
              type: String,
              required: true,
            },
          },
        ],
      },
    ],

    latitude: {
      type: Number,
      required: true,
      default: null,
    },
    longitude: {
      type: Number,
      required: true,
      default: null,
    },

    hoursPerWeek: {
      type: Number,
      min: 1,
    },

    weeklyTarget: {
      type: Number,
      min: 1,
    },
    address: {
      line1: { type: String }, // Street name or building number
      line2: { type: String }, // Optional second address line
      city: { type: String }, // City or town
      postalCode: { type: String }, // Postal code (ZIP code)
      state: { type: String }, // State or region
      country: { type: String }, // Country code, default to "GB" for UK
    },

    commissionPercentage: { type: Number },
    isBlockedByAdmin: {
      type: Boolean,
      required: true,
      default: false,
    },
  },

  {
    timestamps: true,
    discriminatorKey: "__t",
    methods: {
      isUser() {
        return this.__t === "User";
      },
      isAdviser() {
        return this.__t === "Adviser";
      },
    },
  }
);

mainSchema.plugin(paginate);
userSchema.plugin(paginate);
adviserSchema.plugin(paginate);

userSchema.pre("save", async function (next) {
  if (this.dob) {
    const today = new Date();
    const birthDate = new Date(this.dob);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (
      monthDiff < 0 ||
      (monthDiff === 0 && today.getDate() < birthDate.getDate())
    ) {
      age--;
    }
    this.age = age;
  }
  next();
});

adviserSchema.pre("save", async function (next) {
  if (this.dob) {
    const today = new Date();
    const birthDate = new Date(this.dob);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (
      monthDiff < 0 ||
      (monthDiff === 0 && today.getDate() < birthDate.getDate())
    ) {
      age--;
    }
    this.age = age;
  }
  next();
});

userSchema.pre("findOneAndUpdate", function (next) {
  const update = this.getUpdate();

  if (update.dob) {
    const today = new Date();
    const birthDate = new Date(update.dob);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (
      monthDiff < 0 ||
      (monthDiff === 0 && today.getDate() < birthDate.getDate())
    ) {
      age--;
    }

    update.age = age; // Set the recalculated age
    this.setUpdate(update);
  }
  next();
});

adviserSchema.pre("findOneAndUpdate", function (next) {
  const update = this.getUpdate();

  if (update.dob) {
    const today = new Date();
    const birthDate = new Date(update.dob);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (
      monthDiff < 0 ||
      (monthDiff === 0 && today.getDate() < birthDate.getDate())
    ) {
      age--;
    }

    update.age = age; // Set the recalculated age
    this.setUpdate(update);
  }
  next();
});

userSchema.pre("validate", function (next) {
  if (this.isNew && !this.referralCode) {
    this.referralCode = generateReferralCode();
  }
  next();
});

// Create and export models
const Users = mongoose.model("Users", mainSchema); // use USERS when common id query
const Admin = Users.discriminator("Admin", mainSchema); // just to add __t for admin
const User = Users.discriminator("User", userSchema);
const Adviser = Users.discriminator("Adviser", adviserSchema);

const generateReferralCode = () => {
  return Math.random().toString(36).substring(2, 8).toUpperCase();
};

module.exports = {
  Admin,
  Users,
  User,
  Adviser,
};
