const mongoose = require("mongoose");

const subscriptionProductSchema = new mongoose.Schema({
  name: { type: String, required: true }, // Plan Name (Monthly, Yearly)
  price: { type: Number, required: true },
  description: { type: String, trim: true, required: true }, // Plan Price
  billingCycle: {
    type: String,
    enum: ["week", "month", "year", "day"],
    required: true,
  },
  isActive: { type: Boolean },
  stripeProductId: { type: String, required: true },
  stripePriceId: { type: String, required: true }, // Stripe Price ID
  maxSessions: { type: Number, required: true }, // Sessions included per period
  subscriberCount: { type: Number, default: 0, min: 0 },
});

const SubscriptionProduct = mongoose.model(
  "SubscriptionProduct",
  subscriptionProductSchema
);
module.exports = { SubscriptionProduct };
