const mongoose = require("mongoose");

const financialHealthSchema = new mongoose.Schema(
  {
    userId: {
      type: mongoose.SchemaTypes.ObjectId,
      ref: "User",
      required: true,
    },
    maritalStatus: {
      type: String,
      enum: ["Single", "Married", "Divorced", "Widowed", "Cohabitant"],
      required: true,
    },
    dependents: {
      type: Number,
      required: true,
      min: 0,
    },
    annualIncome: {
      type: Number,
      required: true,
      min: 0,
    },
    savings: {
      type: Number,
      required: true,
      min: 0,
    },
    investments: {
      type: Number,
      required: true,
      min: 0,
    },
    pensions: {
      type: Number,
      required: true,
      min: 0,
    },
    debt: {
      type: Number,
      required: true,
      min: 0,
    },
    propertyOwnership: {
      type: String,
      enum: ["Own", "Rent", "Other"],
      required: true,
    },
    insurances: {
      lifeInsurance: {
        type: Boolean,
        default: false,
      },
      criticalIllnessCover: {
        type: Boolean,
        default: false,
      },
      incomeProtection: {
        type: Boolean,
        default: false,
      },
      privateMedical: {
        type: Boolean,
        default: false,
      },
    },
    monthlyNetIncome: {
      type: Number,
      required: true,
      min: 0,
    },
    monthlyExpenses: {
      type: Number,
      required: true,
      min: 0,
    },
    monthlySavings: {
      type: Number,
      required: true,
      min: 0,
    },
  },
  {
    timestamps: true,
  }
);
financialHealthSchema.index({ userId: 1 }, { unique: true });

// Create the model
const FinancialHealth = mongoose.model(
  "FinancialHealth",
  financialHealthSchema
);

module.exports = { FinancialHealth };
