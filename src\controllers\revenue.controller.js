const httpStatus = require("http-status");
const { Faq } = require("../models");
const ApiError = require("../utils/ApiError");
const catchAsync = require("../utils/catchAsync");
const moment = require("moment");
const { revenueService, payoutService, refundService } = require("../services");

//shows or calculate on gross (excluding stripe fees)
// const totalRevenueForYear = catchAsync(async (req, res) => {
//   const currentMonth = moment().month(); // 0-indexed
//   const currentYear = moment().year();

//   // ✅ Current year for data retrieval

//   // ✅ Fetch data for the entire year in a single call
//   const [monthlyRevenueData, monthlyPayoutData, monthlyRefundData] =
//     await Promise.all([
//       revenueService.getRevenueInRangeForYear(currentYear),
//       payoutService.getMonthlyPayoutsForYear(currentYear),
//       refundService.getMonthlyRefundsForYear(currentYear),
//     ]);

//   // Extract total values from the monthly data
//   const revenueData = {
//     totalNetRevenue: monthlyRevenueData.reduce(
//       (sum, item) => sum + (item.total || 0),
//       0
//     ),
//     totalGrossRevenue: monthlyRevenueData.reduce(
//       (sum, item) => sum + (item.grossTotal || 0),
//       0
//     ),
//     totalStripeFees: monthlyRevenueData.reduce(
//       (sum, item) => sum + (item.stripeFees || 0),
//       0
//     ),
//     count: monthlyRevenueData.length,
//   };

//   const payoutData = {
//     totalPayoutAmount: monthlyPayoutData.reduce(
//       (sum, item) => sum + (item.total || 0),
//       0
//     ),
//     count: monthlyPayoutData.length,
//   };

//   const refundData = {
//     totalRefundAmount: monthlyRefundData.reduce(
//       (sum, item) => sum + (item.total || 0),
//       0
//     ),
//     count: monthlyRefundData.length,
//   };

//   // ✅ Calculate net revenue (Revenue - Payouts - Refunds)
//   const netRevenue = parseFloat(
//     (
//       revenueData.totalNetRevenue -
//       payoutData.totalPayoutAmount -
//       refundData.totalRefundAmount
//     ).toFixed(2)
//   );

//   // ✅ Process monthly data
//   const monthlyRevenueProcessed = processMonthlyRevenueData(
//     monthlyRevenueData,
//     "total"
//   );
//   const monthlyGrossRevenueProcessed = processMonthlyRevenueData(
//     monthlyRevenueData,
//     "grossTotal"
//   );
//   const monthlyStripeFeeProcessed = processMonthlyRevenueData(
//     monthlyRevenueData,
//     "stripeFees"
//   );
//   const monthlyPayoutProcessed = processMonthlyRevenueData(monthlyPayoutData);
//   const monthlyRefundProcessed = processMonthlyRevenueData(monthlyRefundData);

//   // ✅ Calculate monthly net revenue (Revenue - Payouts - Refunds)
//   const monthlyGraphData = calculateMonthlyNetRevenue(
//     monthlyRevenueProcessed,
//     monthlyPayoutProcessed,
//     monthlyRefundProcessed
//   );

//   // ✅ Growth/loss % from last month
//   const lastMonthIndex = currentMonth - 1;
//   const currentValue = monthlyGraphData[currentMonth] || 0;
//   const lastValue =
//     lastMonthIndex >= 0 ? monthlyGraphData[lastMonthIndex] || 0 : 0;

//   let percentageChange = 0;
//   if (lastValue === 0) {
//     percentageChange = currentValue === 0 ? 0 : 100;
//   } else {
//     percentageChange = parseFloat(
//       (((currentValue - lastValue) / lastValue) * 100).toFixed(2)
//     );
//   }

//   const direction =
//     currentValue === lastValue
//       ? "no-change"
//       : currentValue > lastValue
//         ? "growth"
//         : "loss";

//   res.status(httpStatus.OK).json({
//     year: moment().format("YYYY"),
//     grossRevenue: revenueData.totalGrossRevenue, // Total gross revenue for the current year
//     netRevenue: netRevenue, // Net revenue after deducting payouts and refunds
//     monthlyData: {
//       netRevenue: monthlyGraphData, // Monthly net revenue data for the year
//       revenue: monthlyRevenueProcessed, // Monthly net revenue data
//       grossRevenue: monthlyGrossRevenueProcessed, // Monthly gross revenue data
//       stripeFees: monthlyStripeFeeProcessed, // Monthly stripe fees
//       payouts: monthlyPayoutProcessed, // Monthly payout data
//       refunds: monthlyRefundProcessed, // Monthly refund data
//     },
//     financialDetails: {
//       revenue: {
//         totalNetRevenue: revenueData.totalNetRevenue,
//         totalGrossRevenue: revenueData.totalGrossRevenue,
//         totalStripeFees: revenueData.totalStripeFees,
//         count: revenueData.count,
//       },
//       payouts: {
//         totalPayoutAmount: payoutData.totalPayoutAmount,
//         count: payoutData.count,
//       },
//       refunds: {
//         totalRefundAmount: refundData.totalRefundAmount,
//         count: refundData.count,
//       },
//     },
//     comparison: {
//       lastMonth:
//         lastMonthIndex >= 0
//           ? moment().subtract(1, "months").format("MMMM")
//           : null,
//       currentValue,
//       lastValue,
//       percentageChange,
//       direction,
//     },
//   });
// });

// const processMonthlyRevenueData = (monthlyData, field = "total") => {
//   // Initialize array with 12 months set to 0
//   const processedData = Array(12).fill(0);

//   // Fill in the data from the database results
//   if (monthlyData && monthlyData.length > 0) {
//     monthlyData.forEach((item) => {
//       const monthIndex = item._id - 1; // Convert 1-based month to 0-based index
//       processedData[monthIndex] = item[field] || 0;
//     });
//   }

//   return processedData;
// };

// const calculateMonthlyNetRevenue = (revenueData, payoutData, refundData) => {
//   // Initialize array for net revenue
//   const netRevenueData = Array(12).fill(0);

//   // Calculate net revenue for each month
//   for (let i = 0; i < 12; i++) {
//     const revenue = revenueData[i] || 0;
//     const payout = payoutData[i] || 0;
//     const refund = refundData[i] || 0;

//     // Net revenue = Revenue - Payouts - Refunds
//     netRevenueData[i] = parseFloat((revenue - payout - refund).toFixed(2));
//   }

//   return netRevenueData;
// };

// gets from day 1

const getTotalNetRevenue = catchAsync(async (req, res) => {
  const result = await revenueService.getTotalNetRevenue();
  return res.status(httpStatus.OK).json({
    data: result,
  });
});

// get for the current month
const getMonthlyRevenue = catchAsync(async (req, res) => {
  const startDate = moment().startOf("month").toDate();
  const endDate = moment().endOf("month").toDate();

  // Get the revenue for the current month
  const result = await revenueService.getRevenueInRange(startDate, endDate);
  return res.status(httpStatus.OK).json({
    data: {
      netTotal: parseFloat(result.netTotal.toFixed(2)),
      grossTotal: parseFloat(result.grossTotal.toFixed(2)),
      stripeFees: parseFloat(result.stripeFees.toFixed(2)),
    },
  });
});

const subscriptionRevenueGrowth = catchAsync(async (req, res) => {
  const result =
    await revenueService.getMonthlySubscriptionRevenueWithComparison();
  return res.status(httpStatus.OK).json({
    data: result,
  });
});

// calculated based on all 3 this year
const getPlatformCommission = catchAsync(async (req, res) => {
  const currentYear = moment().year();

  // Get monthly data for all time in a single call
  const [monthlyRevenueData, monthlyPayoutData, monthlyRefundData] =
    await Promise.all([
      revenueService.getRevenueInRangeForYear(currentYear),
      payoutService.getMonthlyPayoutsForYear(currentYear),
      refundService.getMonthlyRefundsForYear(currentYear),
    ]);

  // Extract total values from the monthly data
  const revenueData = {
    totalNetRevenue: monthlyRevenueData.reduce(
      (sum, item) => sum + (item.netTotal || 0),
      0
    ),
    totalGrossRevenue: monthlyRevenueData.reduce(
      (sum, item) => sum + (item.grossTotal || 0),
      0
    ),
    totalStripeFees: monthlyRevenueData.reduce(
      (sum, item) => sum + (item.stripeFees || 0),
      0
    ),
  };

  const payoutData = {
    totalPayoutAmount: monthlyPayoutData.reduce(
      (sum, item) => sum + (item.total || 0),
      0
    ),
  };

  const refundData = {
    totalRefundAmount: monthlyRefundData.reduce(
      (sum, item) => sum + (item.total || 0),
      0
    ),
  };

  // Calculate platform commission (Revenue - Payouts - Refunds)
  const platformCommission = parseFloat(
    (
      revenueData.totalNetRevenue -
      payoutData.totalPayoutAmount -
      refundData.totalRefundAmount
    ).toFixed(2)
  );

  // Return only the commission earned
  return res.status(httpStatus.OK).json({
    success: true,
    message: "Platform commission data retrieved successfully",
    data: {
      platformCommission,
    },
  });
});

const getRevenueComparison = catchAsync(async (req, res) => {
  const currentYear = moment().year();
  const lastYear = currentYear - 1;

  // Get revenue for current year and last year
  const currentYearRevenue =
    await revenueService.getRevenueInRangeForYear(currentYear);
  const lastYearRevenue =
    await revenueService.getRevenueInRangeForYear(lastYear);

  // Initialize monthsData with default values (0 for all months)
  // Use map instead of fill to create new objects for each month
  const monthsData = Array(12)
    .fill()
    .map(() => ({
      netTotal: { current: 0, lastYear: 0, difference: 0, percentage: 0 },
      grossTotal: { current: 0, lastYear: 0, difference: 0, percentage: 0 },
      stripeFees: { current: 0, lastYear: 0, difference: 0, percentage: 0 },
    }));

  // Map revenue for the current year
  currentYearRevenue.forEach((data) => {
    if (data._id >= 1 && data._id <= 12) {
      const monthIndex = data._id - 1;
      monthsData[monthIndex].netTotal.current = data.netTotal || 0;
      monthsData[monthIndex].grossTotal.current = data.grossTotal || 0;
      monthsData[monthIndex].stripeFees.current = data.stripeFees || 0;
    }
  });

  // Map revenue for the last year
  lastYearRevenue.forEach((data) => {
    if (data._id >= 1 && data._id <= 12) {
      const monthIndex = data._id - 1;
      monthsData[monthIndex].netTotal.lastYear = data.netTotal || 0;
      monthsData[monthIndex].grossTotal.lastYear = data.grossTotal || 0;
      monthsData[monthIndex].stripeFees.lastYear = data.stripeFees || 0;
    }
  });

  // Calculate differences and percentages
  const revenueComparison = monthsData.map((month, index) => {
    const { netTotal, grossTotal, stripeFees } = month;

    // Calculate differences
    const netTotalDifference = netTotal.current - netTotal.lastYear;
    const grossTotalDifference = grossTotal.current - grossTotal.lastYear;
    const stripeFeesDifference = stripeFees.current - stripeFees.lastYear;

    // Calculate percentages with division by zero check
    const netTotalPercentage =
      netTotal.lastYear === 0 && netTotal.current === 0
        ? 0
        : netTotal.lastYear === 0
          ? 100
          : ((netTotalDifference / netTotal.lastYear) * 100).toFixed(2);

    const grossTotalPercentage =
      grossTotal.lastYear === 0 && grossTotal.current === 0
        ? 0
        : grossTotal.lastYear === 0
          ? 100
          : ((grossTotalDifference / grossTotal.lastYear) * 100).toFixed(2);

    const stripeFeesPercentage =
      stripeFees.lastYear === 0 && stripeFees.current === 0
        ? 0
        : stripeFees.lastYear === 0
          ? 100
          : ((stripeFeesDifference / stripeFees.lastYear) * 100).toFixed(2);

    // Return formatted data with cleaner structure
    return {
      month: moment().month(index).format("MMM"), // Fix for month formatting
      netTotal: {
        current: netTotal.current,
        lastYear: netTotal.lastYear,
        difference: netTotalDifference,
        percentage: netTotalPercentage,
      },
      grossTotal: {
        current: grossTotal.current,
        lastYear: grossTotal.lastYear,
        difference: grossTotalDifference,
        percentage: grossTotalPercentage,
      },
      stripeFees: {
        current: stripeFees.current,
        lastYear: stripeFees.lastYear,
        difference: stripeFeesDifference,
        percentage: stripeFeesPercentage,
      },
    };
  });

  // Return the comparison results as JSON response
  return res.status(httpStatus.OK).json({
    data: revenueComparison,
  });
});

module.exports = {
  subscriptionRevenueGrowth,
  getPlatformCommission,
  getMonthlyRevenue,
  getTotalNetRevenue,
  getRevenueComparison,
};
