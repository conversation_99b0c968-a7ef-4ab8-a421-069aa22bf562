const mongoose = require("mongoose");
const { adviserFeedbackResponses } = require("../constants");

const adviserAppointmentFeedbackSchema = new mongoose.Schema(
  {
    appointmentId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Appointment",
      required: true,
    },
    adviserId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Adviser",
      required: true,
    },
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    adviserResponse: {
      type: String,
      enum: Object.values(adviserFeedbackResponses),
      required: true,
    },
    userConsentToBeContacted: {
      type: <PERSON>olean,
    },
    userRejectionReason: {
      type: String, // optional reason like "Not interested"
    },
  },
  {
    timestamps: true,
  }
);
const AdviserAppointmentFeedBack = mongoose.model(
  "AdviserFeedback",
  adviserAppointmentFeedbackSchema
);
module.exports = { AdviserAppointmentFeedBack };
