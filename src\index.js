const app = require("./app");
const config = require("./config/config");
const logger = require("./config/logger");
const mongoose = require("mongoose");
const { appDefaults } = require("./constants");

console.log("App default timezone:", appDefaults.TIMEZONE);
console.log("App Environtment:", config.env);

let server;
mongoose
  .connect(config.mongoose.url, config.mongoose.options)
  .then(() => {
    console.log("Connected to mongodb");
    app.listen(config.port, () => {
      console.log(`Money OWL app listening on port ${config.port}!`);
    });
  })
  .catch((err) => {
    console.log(err);
  });

// ------------- Don't Modify  -------------
const exitHandler = () => {
  if (server) {
    server.close(() => {
      logger.info("Server closed");
      process.exit(1);
    });
  } else {
    process.exit(1);
  }
};

const unexpectedErrorHandler = (error) => {
  console.log(error);
  // logger.error(error);
  exitHandler();
};

process.on("uncaughtException", unexpectedErrorHandler);
process.on("unhandledRejection", unexpectedErrorHandler);

process.on("SIGTERM", () => {
  logger.info("SIGTERM received");
  if (server) {
    server.close();
  }
});
// ------------- Don't Modify  -------------
