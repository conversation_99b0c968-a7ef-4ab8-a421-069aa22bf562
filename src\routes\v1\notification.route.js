const express = require("express");

const { firebaseAuth } = require("../../middlewares/firebaseAuth");

const { notificationController } = require("../../controllers");

const router = express.Router();

router.patch(
  "/read/:id",
  firebaseAuth("All"),
  notificationController.readNotifications
);

router.patch(
  "/markAll",
  firebaseAuth("All"),
  notificationController.markAllNotifications
);

router.get(
  "/count",
  firebaseAuth("Adviser,User"),
  notificationController.getUnreadCount
);
module.exports = router;
