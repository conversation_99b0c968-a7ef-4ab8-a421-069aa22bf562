const { Payout } = require("../models/index");
const mongoose = require("mongoose");
const moment = require("moment");

const createPayout = async (payoutData) => {
  return await Payout.create(payoutData);
};

const getPayoutById = async (payoutId) => {
  return await Payout.findById(payoutId);
};

const getOnePayout = async (query) => {
  return await Payout.findOne(query);
};

const getPayoutByStripeId = async (stripeTransferId) => {
  return await Payout.findOne({ stripeTransferId: stripeTransferId });
};

const getPayoutsByAdviser = async (adviserId, additionalFilters = {}) => {
  const filters = {
    adviserId,
    ...additionalFilters,
  };

  return await Payout.find(filters).sort({ createdAt: -1 });
};

const updatePayout = async (payoutId, updateData) => {
  return await Payout.findByIdAndUpdate(payoutId, updateData, { new: true });
};

const getPendingPayouts = async () => {
  return await Payout.find({ status: "pending" }).sort({ createdAt: -1 });
};

const getCompletedPayouts = async () => {
  return await Payout.find({ status: "completed" }).sort({ createdAt: -1 });
};

const deletePayout = async (payoutId) => {
  return await Payout.findByIdAndDelete(payoutId);
};

const getTotalPayouts = async (filters = {}) => {
  // Default to completed status if not specified
  const query = {
    ...filters,
  };

  const result = await Payout.aggregate([
    { $match: query },
    {
      $group: {
        _id: null,
        totalPayoutAmount: { $sum: "$amount" },
        count: { $sum: 1 },
      },
    },
  ]);

  if (result.length === 0) {
    return {
      totalPayoutAmount: 0,
      count: 0,
    };
  }

  return {
    totalPayoutAmount: parseFloat(result[0].totalPayoutAmount.toFixed(2)),
    count: result[0].count,
  };
};

const getMonthlyPayoutsForYear = async (year) => {
  const start = new Date(year, 0, 1); // January 1st of the specified year
  const end = new Date(year, 11, 31, 23, 59, 59); // December 31st of the specified year

  // Get monthly payout data
  const result = await Payout.aggregate([
    {
      $match: {
        createdAt: { $gte: start, $lte: end },
        status: "completed",
      },
    },
    {
      $group: {
        _id: { $month: "$createdAt" },
        total: { $sum: "$amount" },
      },
    },
    { $sort: { _id: 1 } }, // Sort by month (1 = ascending)
  ]);

  return result;
};

const getAdviserEarnings = async (
  adviserId,
  format = "monthly",
  year = new Date().getFullYear()
) => {
  // Create date range for the year
  const startDate = moment([year]).startOf("year").toDate();
  const endDate = moment([year]).endOf("year").toDate();

  // Base query to get all payouts for the year
  const baseQuery = {
    adviserId: new mongoose.Types.ObjectId(adviserId),
    status: "completed",
    createdAt: { $gte: startDate, $lte: endDate },
  };
  // Get all payouts for the year with populated appointment data
  const payouts = await Payout.find(baseQuery)
    .populate({
      path: "appointmentId",
      select: "date timeSlot duration amount status paymentStatus user",
      populate: { path: "user", select: "name email" },
    })
    .sort({ createdAt: -1 })
    .lean();

  // Calculate total earnings for the year
  const totalEarnings = payouts.reduce((sum, payout) => sum + payout.amount, 0);

  // If format is yearly, return simple yearly summary
  if (format === "yearly") {
    return {
      year,
      totalEarnings,
      payoutCount: payouts.length,
      payouts,
    };
  }

  // For monthly format, group payouts by month
  const monthlyData = [];

  // Initialize array for all 12 months
  for (let month = 1; month <= 12; month++) {
    const monthName = moment()
      .month(month - 1)
      .format("MMMM");

    // Filter payouts for this month
    const monthPayouts = payouts.filter((payout) => {
      const payoutMonth = moment(payout.createdAt).month() + 1; // moment months are 0-indexed
      return payoutMonth === month;
    });

    // Calculate total for this month
    const monthTotal = monthPayouts.reduce(
      (sum, payout) => sum + payout.amount,
      0
    );

    monthlyData.push({
      month,
      monthName,
      total: monthTotal,
      payouts: monthPayouts,
    });
  }

  return {
    year,
    totalEarnings,
    monthlyData,
  };
};

const getPayoutCompletionStats = async (filters = {}) => {
  const result = await Payout.aggregate([
    { $match: filters },
    {
      $group: {
        _id: null,
        totalPayouts: { $sum: 1 },
        completedPayouts: {
          $sum: {
            $cond: [{ $eq: ["$status", "completed"] }, 1, 0],
          },
        },
      },
    },
  ]);

  if (result.length === 0) {
    return {
      totalPayouts: 0,
      completionPercentage: 0,
    };
  }

  const { totalPayouts, completedPayouts } = result[0];
  const completionPercentage = parseFloat(
    ((completedPayouts / totalPayouts) * 100).toFixed(2)
  );

  return {
    totalPayouts,
    completionPercentage,
  };
};

const getYearlyPayoutData = async (year) => {
  const start = moment().year(year).startOf("year").toDate();
  const end = moment().year(year).endOf("year").toDate();

  // Aggregate payout data for the given year
  const result = await Payout.aggregate([
    {
      $match: {
        createdAt: { $gte: start, $lte: end },
      },
    },
    {
      $group: {
        _id: null,
        totalPayoutAmount: { $sum: "$totalAmount" },
      },
    },
  ]);

  return result[0] || { totalPayoutAmount: 0 };
};

module.exports = {
  createPayout,
  getPayoutById,
  getPayoutByStripeId,
  getPayoutsByAdviser,
  updatePayout,
  getPendingPayouts,
  getCompletedPayouts,
  deletePayout,
  getOnePayout,
  getTotalPayouts,
  getMonthlyPayoutsForYear,
  getAdviserEarnings,
  getPayoutCompletionStats,
  getYearlyPayoutData,
};
