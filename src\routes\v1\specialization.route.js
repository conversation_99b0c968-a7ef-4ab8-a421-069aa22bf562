const express = require("express");
const { firebaseAuth } = require("../../middlewares/firebaseAuth");
const { specializationController } = require("../../controllers");

const router = express.Router();

router.get(
  "/",
  firebaseAuth("All"),
  specializationController.getAllSpecializations
);

router.get(
  "/get/:id?",
  firebaseAuth("All"),
  specializationController.getSpecializationById
);

router.post(
  "/add",
  firebaseAuth("Admin"),
  specializationController.addSpecialization
);

router.patch(
  "/update/:id",
  firebaseAuth("Admin"),
  specializationController.updateSpecialization
);

router.delete(
  "/delete/:id",
  firebaseAuth("Admin"),
  specializationController.deleteSpecialization
);

module.exports = router;
