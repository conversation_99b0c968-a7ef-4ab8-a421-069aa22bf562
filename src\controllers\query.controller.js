const httpStatus = require("http-status");
const { appointmentService, queryService } = require("../services");
const ApiError = require("../utils/ApiError");
const catchAsync = require("../utils/catchAsync");
const { generateRandomEightDigitNumber } = require("../utils/helper");
const { getPaginateConfig } = require("../utils/queryPHandler");
const { userTypes, emitterEventNames } = require("../constants");
const eventEmitter = require("../events/eventEmitter");

const addQuery = catchAsync(async (req, res) => {
  const { query } = req.body;
  const ticketId = "#" + generateRandomEightDigitNumber();
  const appointmentId = req.params.appointmentId;

  if (!appointmentId) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Appointment ID is required");
  }

  const raisedById = req.user._id;
  const role = req.user.__t;

  if (role !== "User" && role !== "Adviser") {
    throw new ApiError(httpStatus.FORBIDDEN, "Invalid user role");
  }

  // Find appointment depending on role
  const appointmentFilter = { _id: appointmentId };
  if (role === "User") appointmentFilter.user = raisedById;
  if (role === "Adviser") appointmentFilter.adviser = raisedById;

  const appointment =
    await appointmentService.findOneAppointment(appointmentFilter);

  if (!appointment) {
    throw new ApiError(httpStatus.NOT_FOUND, "Appointment not found");
  }

  // Check if a query already exists for this appointment by this user/adviser
  const existingQuery = await queryService.getQueryWhere({
    appointment: appointmentId,
    raisedBy: raisedById,
  });

  if (existingQuery.length !== 0) {
    throw new ApiError(
      httpStatus.FORBIDDEN,
      "You have already raised a ticket against this appointment."
    );
  }

  // Create query with common raisedBy field
  const data = {
    ticketId,
    raisedBy: raisedById,
    query,
    appointment: appointment._id,
  };

  const newQuery = await queryService.createQuery(data);

  return res
    .status(httpStatus.OK)
    .json({ status: true, newQuery, message: "Ticket Raised Successfully" });
});

const getQueries = catchAsync(async (req, res) => {
  const userId = req.user._id;
  let { filters, options } = getPaginateConfig(req.query);

  if (req.user.__t !== userTypes.ADMIN) {
    filters = {
      ...filters,
      raisedBy: userId,
    };
  }

  const queries = await queryService.getAllQueries(filters, options);

  return res.status(httpStatus.OK).json({
    message: "queries retrieved successfully",
    data: queries,
  });
});

const updateQueryStatus = catchAsync(async (req, res) => {
  const queryId = req.params.id;
  const { message: adminMessage } = req.body;

  const updatedQuery = await queryService.updateQuery(queryId, {
    status: "resolved",
    adminMessage,
  });

  if (!updatedQuery) {
    throw new ApiError(httpStatus.NOT_FOUND, "Query not found");
  }

  // Trigger notification event
  eventEmitter.emit(emitterEventNames.QUERY_STATUS_UPDATED, {
    queryId: updatedQuery._id,
    userId: updatedQuery.raisedBy,
    ticketId: updatedQuery.ticketId,
    status: updatedQuery.status,
    adminMessage: updatedQuery.adminMessage,
  });

  return res.status(httpStatus.OK).json({
    message: `query ${updatedQuery.status} successfully`,
    data: updatedQuery,
  });
});

const getQueryDetails = catchAsync(async (req, res) => {
  const userId = req.user._id;
  const queryId = req.params.id;
  if (!queryId) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Query id is required");
  }
  let filter = { _id: queryId };
  if (req.user.__t === userTypes.USER) {
    filter = { ...filter, user: userId };
  }
  if (req.user.__t === userTypes.ADVISER) {
    filter = { ...filter, adviser: userId };
  }
  const queries = await queryService.getQueryWhere(filter);
  res.status(httpStatus.OK).json({
    message: "query retrieved successfully",
    data: queries,
  });
});

module.exports = { addQuery, getQueries, updateQueryStatus, getQueryDetails };
