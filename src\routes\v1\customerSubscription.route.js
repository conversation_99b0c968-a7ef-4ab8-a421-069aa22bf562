const express = require("express");
const validate = require("../../middlewares/validate");
const { firebaseAuth } = require("../../middlewares/firebaseAuth");
const {
  customerSubscriptionController,
  stripeController,
  revenueController,
} = require("../../controllers");

const { subscriptionValidation } = require("../../validations");

const router = express.Router();

router.get(
  "/exists/:id?",
  firebase<PERSON>uth("Admin,User"),
  customerSubscriptionController.getUserSubscriptiondata
);

router.get(
  "/stats",
  firebaseAuth("Admin"),
  customerSubscriptionController.getSubscriptionStats
);

router.get(
  "/usage",
  firebase<PERSON>uth("User"),
  customerSubscriptionController.checkSubscriptionUsage
);

router.post(
  "/subscribe",
  firebaseAuth("User"),
  validate(subscriptionValidation.createSubscription),
  stripeController.createSubscription
);

router.post(
  "/cancel",
  firebaseAuth("User"),
  validate(subscriptionValidation.cancelSubscription),
  stripeController.cancelSubscription
);

router.get(
  "/",
  firebaseAuth("Admin"),
  customerSubscriptionController.getAllSubscriptions
);

router.get(
  "/revenue/growth",
  firebaseAuth("Admin"),
  revenueController.subscriptionRevenueGrowth
);

// Check subscription after appointment completion
router.get(
  "/set/reminder",
  firebaseAuth("Admin,User"),
  customerSubscriptionController.checkSubscriptionAfterAppointment
);

router.get(
  "/geo/stats",
  firebaseAuth("Admin"),
  customerSubscriptionController.getSubscribersGeoStats
);

router.get(
  "/history/:id?",
  firebaseAuth("User,Admin"),
  customerSubscriptionController.getSubscriptionHistoryData
);

module.exports = router;
