const { Appointment, Users } = require("../models");
const moment = require("moment");

async function createAppointment(appointmentData) {
  const appointment = new Appointment(appointmentData);
  return await appointment.save();
}

async function getAllAppointments(
  filters = {},
  options = {},
  user,
  populate = true
) {
  const query = { ...filters };

  if (filters.isFollowup) {
    query.parentAppointment = { $ne: null };
    delete query.isFollowup;
  } else if (filters.status === "upcoming") {
    query.status = { $nin: ["completed", "cancelled"] };
    query.adviserStatus = "accepted";
    options.sortBy = "timeSlot.start";
    options.sortOrder = "asc";
    query["timeSlot.end"] = { $gte: new Date() };
  } else if (filters.status === "completed") {
    query.status = "completed";
  } else if (filters.status === "cancelled") {
    query.status = "cancelled";
  } else if (filters.status === "requested") {
    query.status = "scheduled";
    query.adviserStatus = "pending";
    if (user.__t === "Adviser") {
      query.paymentStatus = { $in: ["free", "paid"] };
      // query["timeSlot.start"] = { $gte: new Date() };
    }
  } else if (filters.paymentStatus) {
    query.paymentStatus = filters.paymentStatus; // Direct match
  }

  delete filters.paymentStatus;

  if (filters.search) {
    if (
      filters.search.startsWith("APT-") ||
      filters.search.startsWith("apt-")
    ) {
      const searchValue = filters.search.slice(4).toUpperCase(); // Remove 'APT-' and normalize

      // Match any bookingId that contains the partial after 'APT-'
      // query.bookingId = { $regex: `\\b${searchValue}`, $options: "i" };

      query.bookingId = { $regex: `${searchValue}`, $options: "i" };
      delete query.search;
    } else {
      const searchRegex = new RegExp(filters.search, "i"); // Case-insensitive regex

      // Find matching users by name or email
      const users = await Users.find({
        $or: [{ name: searchRegex }],
      }).select("_id");

      const userIds = users.map((user) => user._id);

      // Apply flexible search filters
      delete query.search;
      if (userIds.length > 0) {
        query.$or = [{ user: { $in: userIds } }, { adviser: { $in: userIds } }];
      } else {
        return;
      }
    }
  }
  if (populate) {
    options.populate = [
      "user::name,profilePic,profession",
      "adviser::name,profilePic,averageRating,experience",
    ];
  }

  options.project = {
    hasUserJoined: 0,
    hasAdviserJoined: 0,
    adviserJoinTime: 0,
    userJoinTime: 0,
    isReminderSet: 0,
    updatedAt: 0,
    __v: 0,
    paymentIntent: 0,
    parentAppointment: 0,
    systemCancellationReason: 0,
  };
  const appointments = await Appointment.paginate(query, options);

  return appointments;
}

async function getAppointmentDetails(id, populateFields = []) {
  let query = Appointment.findById(id);
  if (populateFields.length > 0) {
    populateFields.forEach((field) => {
      query = query.populate(field); // Chain populate calls
    });
  }
  const appointmentDetails = await query.exec(); // Execute the query to fetch data
  return appointmentDetails;
}

async function findAppointmentWhere(query) {
  let appointment = await Appointment.find(query);
  return appointment;
}

async function updateAppointment(id, updateData) {
  const appointment = await Appointment.findByIdAndUpdate(id, updateData, {
    new: true,
    runValidators: true,
  });

  return appointment;
}

async function findAndUpdate(filter, updateData, populateFields = []) {
  let query = Appointment.findOneAndUpdate(filter, updateData, {
    new: true, // Return the updated document
    runValidators: true, // Ensure validation rules are applied
  });

  // Apply population if fields are provided
  if (populateFields.length > 0) {
    query = query.populate(populateFields);
  }

  const appointment = await query.exec();
  return appointment;
}

async function deleteAppointment(id) {
  const appointment = await Appointment.findByIdAndDelete(id);
  return appointment;
}

async function findAppointment(query) {
  const appointment = await Appointment.find(query).populate([
    "adviser",
    "user",
  ]);

  return appointment;
}

async function findOneAppointment(query) {
  const appointment = await Appointment.findOne(query).populate([
    "adviser",
    "user",
  ]);

  return appointment;
}

async function countFutureAppointments() {
  const nowUtc = moment.utc().toDate(); // Current date and time in UTC

  return await Appointment.countDocuments({
    adviserStatus: "accepted",
    status: "scheduled",
    date: { $gte: nowUtc },
  });
}

async function getAppointmentsExcludingRejectedOrCancelled(userId) {
  // Hardcoded filter to exclude "rejected" and "cancelled" adviser status
  const query = {
    $or: [{ user: userId }, { adviser: userId }],
    adviserStatus: { $nin: ["rejected", "pending"] },
    status: { $nin: ["cancelled"] },
  };

  // Fetch all appointments with the query and populate necessary fields
  const appointments = await Appointment.find(query)
    .populate("user adviser")
    .select("_id name profilePic");

  return appointments;
}

async function getNextUpcomingAppointment(adviserId) {
  const query = {
    adviser: adviserId,
    status: { $nin: ["completed", "cancelled"] },
    adviserStatus: "accepted",
    date: { $gte: new Date() }, // Get future appointments
  };

  const nextAppointment = await Appointment.findOne(query)
    .sort({ date: 1 })
    .limit(1); // Nearest first

  return nextAppointment;
}

async function getNextUpcomingAppointmentUser(userid) {
  const query = {
    user: userid,
    status: { $nin: ["completed", "cancelled"] },
    adviserStatus: "accepted",
    date: { $gte: new Date() }, // Get future appointments
  };

  const nextAppointment = await Appointment.findOne(query).sort({ date: 1 }); // Nearest first

  return nextAppointment;
}

async function calculateAverageSessionDuration() {
  try {
    const result = await Appointment.aggregate([
      {
        $match: {
          status: "completed", // Only completed appointments
          "timeSlot.start": { $exists: true },
          "timeSlot.end": { $exists: true },
        },
      },
      {
        $project: {
          durationInMinutes: {
            $divide: [
              { $subtract: ["$timeSlot.end", "$timeSlot.start"] }, // Get duration in milliseconds
              1000 * 60, // Convert milliseconds to minutes
            ],
          },
        },
      },
      {
        $group: {
          _id: null,
          avgSessionDuration: { $avg: "$durationInMinutes" }, // Calculate the average
        },
      },
    ]);

    return result.length > 0 ? result[0].avgSessionDuration : 0;
  } catch (error) {
    console.error("🚀 Error calculating average session duration:", error);
    throw error;
  }
}

async function searchAppointments(search) {
  const users = await Users.find({
    $or: [
      { name: { $regex: search, $options: "i" } },
      { email: { $regex: search, $options: "i" } },
    ],
  });

  const userIds = users.map((u) => u._id); // Extract user IDs

  return users;
}

async function calculateAverageSessionCost() {
  try {
    const result = await Appointment.aggregate([
      {
        $match: {
          status: "completed",
          adviserStatus: "accepted",
          paymentStatus: "paid",
          mode: "payment",
          amount: { $gt: 0 }, // optional: only positive payments
        },
      },
      {
        $group: {
          _id: null,
          avgSessionCost: { $avg: "$amount" },
        },
      },
    ]);

    return result.length > 0 ? result[0].avgSessionCost : 0;
  } catch (error) {
    console.error("❌ Error calculating average session cost:", error);
    throw error;
  }
}

async function getMonthlyBookingStats() {
  const currentYear = moment().year();

  const monthlyStats = await Appointment.aggregate([
    {
      $match: {
        "timeSlot.start": {
          $gte: moment().startOf("year").toDate(),
          $lte: moment().endOf("year").toDate(),
        },
        status: { $ne: "cancelled" },
      },
    },
    {
      $group: {
        _id: { month: { $month: "$timeSlot.start" } },
        count: { $sum: 1 },
      },
    },
    { $sort: { "_id.month": 1 } },
  ]);

  const monthlyData = Array(12).fill(0);
  monthlyStats.forEach((entry) => {
    const { month } = entry._id;
    monthlyData[month - 1] = entry.count;
  });

  return {
    year: currentYear,
    monthlyBreakdown: monthlyData,
  };
}

async function getYearlyBookingStats() {
  const currentYear = moment().year();
  const yearRangeStart = moment().subtract(4, "years").startOf("year").toDate();

  const yearlyStats = await Appointment.aggregate([
    {
      $match: {
        "timeSlot.start": { $gte: yearRangeStart },
        status: { $ne: "cancelled" },
      },
    },
    {
      $group: {
        _id: { year: { $year: "$timeSlot.start" } },
        count: { $sum: 1 },
      },
    },
    { $sort: { "_id.year": 1 } },
  ]);

  const responseData = {};
  for (let i = 4; i >= 0; i--) {
    const year = currentYear - i;
    responseData[year] =
      yearlyStats.find((item) => item._id.year === year)?.count || 0;
  }

  return responseData;
}

async function getCommissionsEarnedSummary() {
  const result = await Appointment.aggregate([
    {
      $match: {
        paymentStatus: "paid",
        mode: { $in: ["subscription", "payment"] },
      },
    },
    {
      $project: {
        amount: 1,
        commissionAmount: {
          $divide: [{ $multiply: ["$commissionPercentage", "$amount"] }, 100],
        },
      },
    },
    {
      $group: {
        _id: null,
        totalRevenue: { $sum: "$amount" },
        totalCommission: { $sum: "$commissionAmount" },
      },
    },
  ]);

  if (!result.length) {
    return {
      totalRevenue: "0.00",
      totalCommission: "0.00",
    };
  }

  const { totalRevenue, totalCommission } = result[0];

  return {
    totalRevenue: totalRevenue.toFixed(2),
    totalCommission: totalCommission.toFixed(2),
  };
}

async function countAppointments(filters = {}) {
  return await Appointment.countDocuments(filters);
}

module.exports = {
  createAppointment,
  getAllAppointments,
  getAppointmentDetails,
  updateAppointment,
  deleteAppointment,
  findAppointment,
  countFutureAppointments,
  findAppointmentWhere,
  findAndUpdate,
  getAppointmentsExcludingRejectedOrCancelled,
  getNextUpcomingAppointment,
  calculateAverageSessionDuration,
  getNextUpcomingAppointmentUser,
  searchAppointments,
  calculateAverageSessionCost,
  getMonthlyBookingStats,
  getYearlyBookingStats,
  getCommissionsEarnedSummary,
  findOneAppointment,
  countAppointments,
};
