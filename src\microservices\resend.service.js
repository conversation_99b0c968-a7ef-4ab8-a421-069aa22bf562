const { Resend } = require("resend");
const config = require("../config/config");

class EmailQueue {
  constructor() {
    this.resend = new Resend(config.resend.apiKey);
    this.queue = [];
    this.RATE_LIMIT = 2;
    this.INTERVAL_MS = 1000;
    this.isProcessing = false;
  }

  async processQueue() {
    if (this.isProcessing || this.queue.length === 0) return;

    this.isProcessing = true;
    // console.log(`📤 START QUEUE: ${this.queue.length} email(s) pending.`);

    try {
      const batch = this.queue.splice(0, this.RATE_LIMIT);
      // console.log(`📦 Processing batch of ${batch.length} email(s)...`);

      for (const item of batch) {
        const { mailOptions, resolve, reject, retries = 0 } = item;

        try {
          // console.log(
          //   `📧 [${new Date().toISOString()}] Sending to ${mailOptions.to} (Attempt ${retries + 1})`
          // );
          const { data, error } = await this.resend.emails.send(mailOptions);

          if (error) {
            console.error(
              `❌ [${new Date().toISOString()}] Failed to send to ${mailOptions.to}:`,
              error
            );

            if (retries < 2 && this.isRetryableError(error)) {
              console.warn(
                `🔁 Retrying ${mailOptions.to} after delay (Attempt ${retries + 1}/3)`
              );
              this.queue.push({
                mailOptions,
                resolve,
                reject,
                retries: retries + 1,
              });
            } else {
              reject(
                new Error(error.message || "Unknown error while sending email")
              );
            }
          } else {
            // console.log(
            //   `✅ [${new Date().toISOString()}] Email sent to ${mailOptions.to}`
            // );
            resolve(data);
          }
        } catch (err) {
          console.error(
            `🔥 Exception in queue for ${mailOptions.to}:`,
            err.message
          );
          reject(new Error(`Failed to send email: ${err.message}`));
        }
      }

      if (this.queue.length > 0) {
        // console.log("⏱ Scheduling next batch...");
        setTimeout(() => {
          this.isProcessing = false;
          this.processQueue();
        }, this.INTERVAL_MS);
      } else {
        // console.log("🎉 Queue complete. No more emails.");
        this.isProcessing = false;
      }
    } catch (err) {
      console.error("💥 Queue processor crashed:", err);
      this.isProcessing = false;

      if (this.queue.length > 0) {
        // console.log(`🔄 Restarting queue processor in ${this.INTERVAL_MS}ms`);
        setTimeout(() => this.processQueue(), this.INTERVAL_MS);
      }
    }
  }

  isRetryableError(error) {
    const retryableErrors = [
      "rate_limit_exceeded",
      "too_many_requests",
      // "validation_error",
      "timeout",
      "network_error",
      "server_error",
      "500",
      "502",
      "503",
      "504",
    ];

    return retryableErrors.some(
      (e) =>
        (error.code && error.code.includes(e)) ||
        (error.message && error.message.toLowerCase().includes(e))
    );
  }

  queueEmail(mailOptions) {
    // console.log(`📥 Queued: ${mailOptions.to}`);
    return new Promise((resolve, reject) => {
      this.queue.push({ mailOptions, resolve, reject });
      if (!this.isProcessing) this.processQueue();
    });
  }

  async sendEmail(emailData) {
    const mailOptions = {
      from: "<EMAIL>",
      to: emailData.to,
      subject: emailData.subject,
      html: emailData.html,
    };
    return this.queueEmail(mailOptions);
  }

  async sendEmailMultiple(emailData) {
    const promises = emailData.recipients.map((recipient) => {
      const mailOptions = {
        from: "<EMAIL>",
        to: recipient.email,
        subject: recipient.subject || emailData.subject,
        html: emailData.html,
      };
      return this.queueEmail(mailOptions);
    });

    const results = await Promise.allSettled(promises);
    const failed = results.filter((r) => r.status === "rejected");

    if (failed.length > 0) {
      console.warn(
        `⚠️ ${failed.length}/${results.length} email(s) failed.`,
        failed.map((f) => f.reason.message)
      );
    }

    // console.log(
    //   `✅ Email batch complete: ${results.length - failed.length} succeeded, ${failed.length} failed.`
    // );

    return {
      success: failed.length === 0,
      results: results.map((r) =>
        r.status === "fulfilled" ? r.value : r.reason
      ),
    };
  }

  getQueueStatus() {
    return {
      queueLength: this.queue.length,
      isProcessing: this.isProcessing,
      rateLimit: this.RATE_LIMIT,
      intervalMs: this.INTERVAL_MS,
    };
  }
}

// ✅ Export singleton instance
module.exports = new EmailQueue();
