const httpStatus = require("http-status");
const { appNotificationService } = require("../services/index");
const ApiError = require("../utils/ApiError");
const catchAsync = require("../utils/catchAsync");
const { AppNotification } = require("../models");

const readNotifications = catchAsync(async (req, res) => {
  const { id: notificationId } = req.params;
  const { _id: userId, __t: userRole } = req.user;

  const notification =
    await appNotificationService.getNotificationById(notificationId);

  if (!notification) {
    throw new ApiError(httpStatus.NOT_FOUND, "Notification not found.");
  }

  if (
    notification.targetUser &&
    notification.targetUser.toString() === userId.toString()
  ) {
    notification.isRead = true;
  } else if (notification.targetRole && notification.targetRole === userRole) {
    if (!notification.readBy.includes(userId)) {
      notification.readBy.push(userId);
    }
  } else {
    throw new ApiError(
      httpStatus.FORBIDDEN,
      "You are not authorized to read this notification."
    );
  }

  await notification.save();

  return res.status(httpStatus.OK).json({
    success: true,
    message: "Notification marked as read successfully.",
    data: notification,
  });
});

const markAllNotifications = catchAsync(async (req, res) => {
  const userId = req.user._id;
  const userRole = req.user.__t;
  const { isRead } = req.body;

  if (isRead === undefined) {
    return res.status(httpStatus.BAD_REQUEST).json({
      message: "'isRead' must be either true or false",
    });
  }

  const result = await appNotificationService.markAllNotificationsService(
    userId,
    userRole,
    isRead
  );

  return res.status(httpStatus.OK).json({
    message: `Notifications marked as ${
      isRead ? "read" : "unread"
    } successfully`,
    data: result,
  });
});

const getUnreadCount = catchAsync(async (req, res) => {
  const { _id: userId, __t: role } = req.user;

  const count = await appNotificationService.getUnreadCount(userId, role);
  return res.status(200).json({ count: count });
});

module.exports = { readNotifications, markAllNotifications, getUnreadCount };
