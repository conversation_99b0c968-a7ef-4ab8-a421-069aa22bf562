const mongoose = require("mongoose");

const appSettingsSchema = new mongoose.Schema(
  {
    key: {
      type: String,
      required: true,
      unique: true,
    },
    value: {
      type: mongoose.Schema.Types.Mixed, // can store object, string, number, etc.
      required: true,
    },
  },
  { timestamps: true }
);

const AppSettings = mongoose.model("AppSettings", appSettingsSchema);

module.exports = { AppSettings };
