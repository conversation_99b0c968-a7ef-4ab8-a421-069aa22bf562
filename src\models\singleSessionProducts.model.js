const { unique } = require("agenda/dist/job/unique");
const mongoose = require("mongoose");

const singleSessionProductSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
      maxLength: 100,
      trim: true,
      unique: true,
    },
    duration: {
      type: Number, // in minutes (e.g., 15, 30, 60)
      required: true,
      unique: true,
    },
    price: {
      type: Number, // in your base currency (e.g., euros)
      required: true,
      unique: true,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    description: {
      type: String,
      trim: true,
    },
  },
  { timestamps: true }
);

const SingleSessionProduct = mongoose.model(
  "SingleSessionProduct",
  singleSessionProductSchema
);

module.exports = { SingleSessionProduct };
