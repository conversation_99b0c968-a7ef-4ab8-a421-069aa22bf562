const mongoose = require("mongoose");
const { paginate } = require("./plugins/paginate");

const revenueSchema = new mongoose.Schema(
  {
    appointmentId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Appointment",
    },
    adviserFee: { type: Number },
    totalAmount: { type: Number, required: true }, // Total session price (gross amount)
    stripeFee: { type: Number, default: 0 }, // Stripe processing fee
    netAmount: { type: Number }, // Net amount after Stripe fee
    commissionPercentage: { type: Number }, // Commission percentage applied
    status: {
      type: String,
      enum: ["collected", "pending", "failed"],
      default: "collected",
    },
    user: { type: String },
    adviser: { type: String },
    userId: { type: mongoose.Schema.Types.ObjectId, ref: "User" },
    adviserId: { type: mongoose.Schema.Types.ObjectId, ref: "Adviser" },
    feeId: { type: String },
    subscriptionId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "CustomerSubscription",
    },

    sourceType: {
      type: String,
      enum: ["appointment", "subscription"],
      required: true,
    },
    invoiceUrl: { type: String, default: null },
  },
  { timestamps: true }
);

revenueSchema.methods.isCollected = function () {
  return this.status === "collected";
};

revenueSchema.methods.isPending = function () {
  return this.status === "pending";
};

revenueSchema.methods.getCommissionDetails = function () {
  return {
    commissionPercentage: this.commissionPercentage,
    adviserFee: this.adviserFee,
  };
};
revenueSchema.plugin(paginate);
revenueSchema.index({ appointmentId: 1, adviserId: 1, userId: 1 });
const Revenue = mongoose.model("Revenue", revenueSchema);
module.exports = { Revenue };
