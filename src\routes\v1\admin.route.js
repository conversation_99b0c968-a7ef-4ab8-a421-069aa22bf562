const express = require("express");
const validate = require("../../middlewares/validate");
const { firebaseAuth } = require("../../middlewares/firebaseAuth");
const { adminController } = require("../../controllers");
const { fileUploadService } = require("../../microservices");

const {
  notificationValidation,
  authValidation,
  adminValidation,
  userValidation,
} = require("../../validations");

const router = express.Router();

router.get("/count-stats", firebaseAuth("Admin"), adminController.getCount);

router.patch(
  "/update",
  firebaseAuth("Admin"),
  fileUploadService.multerUpload.single("profilePic"),
  adminController.updateProfile
);

router.get(
  "/appointment/:id",
  firebaseAuth("Admin"),
  adminController.getAppointmentDetails
);

router.get("/me", firebaseAuth("Admin"), adminController.getProfile);

router.get(
  "/users",
  firebaseAuth("Admin"),
  // validate(userValidation.getUserOptions),
  adminController.getAllUsers
);

router.get(
  "/adviser-docs/:id",
  firebaseAuth("Admin"),
  adminController.getAdviserDocs
);

//patch
router.post(
  "/approve-docs/:adviserId/:docId",
  firebaseAuth("Admin"),
  validate(adminValidation.approveAdviserDoc),
  adminController.approveAdviserDoc
);

//patch
router.post(
  "/reject-docs/:adviserId/:docId",
  firebaseAuth("Admin"),
  validate(adminValidation.rejectAdviserDoc),
  adminController.rejectAdviserDoc
);

router.get(
  "/gender-stats",
  firebaseAuth("Admin"),
  adminController.getGenderStats
);

router.get("/age-stats", firebaseAuth("Admin"), adminController.getAgeStats);

router.post(
  "/notification/send",
  firebaseAuth("Admin"),
  // validate(notificationValidation.sendNotificationValidation),
  fileUploadService.multerUpload.single("image"),
  adminController.sendNotification
);

router.patch(
  "/notification/delete/:id",
  firebaseAuth("Admin"),
  adminController.deleteSentNotifications
);

router.get(
  "/notifications",
  firebaseAuth("Admin"),
  validate(notificationValidation.getNotificationValidation),
  adminController.getSentNotification
);

router.post(
  "/create-adviser",
  firebaseAuth("Admin"),
  fileUploadService.multerUpload.single("profilePic"),
  adminController.createAccount
);

router.post(
  "/create-user",
  firebaseAuth("Admin"),
  fileUploadService.multerUpload.single("profilePic"),
  // validate(authValidation.registerUserByAdmin),
  adminController.createAccount
);

// router.get(
//   "/location-stats",
//   firebaseAuth("Admin"),
//   adminController.geographicData
// );

router.get(
  "/specialization-stats",
  firebaseAuth("Admin"),
  adminController.expertiseChartData
);

router.get(
  "/user/financedata/:id",
  firebaseAuth("Admin"),
  adminController.getUserFinancialHealth
);

router.get(
  "/session-length",
  firebaseAuth("Admin"),
  adminController.averageSessionLength
);

router.get(
  "/booking-cost",
  firebaseAuth("Admin"),
  adminController.avgBookingValue
);

router.get("/search", firebaseAuth("Admin"), adminController.searchUser);

router.post(
  "/set/commission",
  firebaseAuth("Admin"),
  adminController.setCommissionPercentage
);

router.get(
  "/get/commission",
  firebaseAuth("Admin"),
  adminController.getCommissionPercentage
);

router.get("/get/fees", firebaseAuth("Admin"), adminController.getFees);

router.get(
  "/bookings/insight",
  firebaseAuth("Admin"),
  adminController.getUserBookingsGraph
);

router.get(
  "/refund-percentage",
  firebaseAuth("Admin"),
  adminController.getRefundStats
);

router.post(
  "/invite-adviser",
  firebaseAuth("Admin"),
  // validate(adminValidation.inviteAdviser),
  adminController.sendAdviserInvitation
);

// router.get(
//   "/test-email",
//   firebaseAuth("Admin"),
//   adminController.testEmailQueue
// );

module.exports = router;
