const Joi = require("joi");
const { objectId } = require("./custom.validation");
const { adviserFeedbackResponses } = require("../constants");

/**
 * Validation schema for creating feedback
 */

const createFeedback = {
  params: Joi.object().keys({
    appointmentId: Joi.string().custom(objectId).required().messages({
      "string.empty": "Appointment ID is required",
      "any.required": "Appointment ID is required",
      "string.pattern.base": "Appointment ID must be a valid MongoDB ObjectId",
    }),
  }),
  body: Joi.object().keys({
    adviserResponse: Joi.string()
      .valid(...Object.values(adviserFeedbackResponses))
      .required()
      .messages({
        "string.empty": "Adviser response is required",
        "any.required": "Adviser response is required",
        "any.only": `Adviser response must be one of: ${Object.values(
          adviserFeedbackResponses
        ).join(", ")}`,
      }),
  }),
};

/**
 * Validation schema for updating adviser feedback
 */
const updateAdviserFeedback = {
  params: Joi.object().keys({
    appointmentId: Joi.string().custom(objectId).required().messages({
      "string.empty": "Appointment ID is required",
      "any.required": "Appointment ID is required",
      "string.pattern.base": "Appointment ID must be a valid MongoDB ObjectId",
    }),
  }),
  body: Joi.object().keys({
    adviserResponse: Joi.string()
      .valid(...Object.values(adviserFeedbackResponses))
      .required()
      .messages({
        "string.empty": "Adviser response is required",
        "any.required": "Adviser response is required",
        "any.only": `Adviser response must be one of: ${Object.values(
          adviserFeedbackResponses
        ).join(", ")}`,
      }),
  }),
};

/**
 * Validation schema for user responding to feedback
 */
const respondToFeedback = {
  params: Joi.object().keys({
    appointmentId: Joi.string().custom(objectId).required().messages({
      "string.empty": "Appointment ID is required",
      "any.required": "Appointment ID is required",
      "string.pattern.base": "Appointment ID must be a valid MongoDB ObjectId",
    }),
  }),
  body: Joi.object().keys({
    userConsentToBeContacted: Joi.boolean().required().messages({
      "boolean.base": "Consent must be a boolean value",
      "any.required": "Consent is required",
    }),
    userRejectionReason: Joi.string().when("userConsentToBeContacted", {
      is: false,
      then: Joi.string().required().messages({
        "string.empty": "Rejection reason is required when consent is false",
        "any.required": "Rejection reason is required when consent is false",
      }),
      otherwise: Joi.string().allow("", null),
    }),
  }),
};

const getAppointmentFeedback = {
  params: Joi.object().keys({
    id: Joi.string().custom(objectId).required().messages({
      "string.empty": "Appointment ID is required",
      "any.required": "Appointment ID is required",
      "string.pattern.base": "Appointment ID must be a valid MongoDB ObjectId",
    }),
  }),
};

module.exports = {
  createFeedback,
  updateAdviserFeedback,
  respondToFeedback,
  getAppointmentFeedback,
};
