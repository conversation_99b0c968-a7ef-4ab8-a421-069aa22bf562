const axios = require("axios");
const { authService } = require("../services");
const catchAsync = require("../utils/catchAsync");
const admin = require("firebase-admin");
const moment = require("moment");
const httpStatus = require("http-status");
const { fileUploadService } = require("../microservices");
const { User, Users } = require("../models");
const ApiError = require("../utils/ApiError");
const eventEmitter = require("../events/eventEmitter");
const { emitterEventNames, appDefaults } = require("../constants");
const config = require("../config/config");

const createNewUserObject = (newUser) => ({
  email: newUser.email,
  firebaseUid: newUser.uid,
  profilePic: newUser.picture,
  isEmailVerified: newUser.isEmailVerified,
  firebaseSignInProvider: newUser.firebase.sign_in_provider,
});

const loginUser = catchAsync(async (req, res) => {
  const user = await authService.getUserByFirebaseUId(req.user.firebaseUid);

  if (!user) {
    return res.status(httpStatus.NOT_FOUND).send({ message: "User not found" });
  }

  if (user.isDeleted) {
    return res
      .status(httpStatus.BAD_REQUEST)
      .send({ message: "account is deactivated" });
  }

  return res.status(httpStatus.OK).json({ data: user });
});

const registerUser = catchAsync(async (req, res) => {
  // Check if the user already exists
  let profilePic = null;

  if (req.user) {
    return res.status(401).send({ message: "User already exists" });
  } else {
    if (req.file) {
      [profilePic] = await fileUploadService
        .s3Upload([req.file], "profile_picture")
        .catch((e) => {
          throw new ApiError(
            httpStatus.INTERNAL_SERVER_ERROR,
            `Failed to upload documents : ${e.message}`
          );
        });
    }

    const userObj = {
      ...createNewUserObject(req.newUser),
      ...req.body,
      profilePic,
    };

    if (userObj.address && typeof userObj.address === "string") {
      try {
        userObj.address = JSON.parse(userObj.address);
      } catch (err) {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          "Invalid JSON format for address"
        );
      }
    }

    let user = null;

    try {
      switch (req.routeType) {
        case "User":
          let referredBy = null;
          let shouldEmitReward = false;

          if (userObj.code) {
            referredBy = await User.findOne({
              referralCode: userObj.code,
              isDeleted: false,
            });

            if (!referredBy) {
              throw new ApiError(
                httpStatus.BAD_REQUEST,
                "Invalid referral code"
              );
            }

            referredBy.referralCount += 1;

            if (referredBy.referralCount % 5 === 0) {
              referredBy.freeSessions += 1;
              shouldEmitReward = true;
            }
          }

          user = await authService.createUser(userObj, referredBy);
          eventEmitter.emit(emitterEventNames.USER_SIGNED_UP, user);

          if (user && referredBy) {
            await referredBy.save();

            if (shouldEmitReward) {
              eventEmitter.emit(
                emitterEventNames.REFERRAL_REWARD,
                referredBy,
                referredBy.freeSessions
              );
            }
          }
          break;

        case "Adviser":
          const timezone = req.headers?.timezone || appDefaults.TIMEZONE;
          if (!timezone) {
            return res
              .status(httpStatus.BAD_REQUEST)
              .send({ message: "Timezone header is missing" });
          }

          if (typeof userObj.availability === "string") {
            try {
              userObj.availability = JSON.parse(userObj.availability);
            } catch (error) {
              throw new ApiError(
                httpStatus.BAD_REQUEST,
                `Invalid JSON format for availability: ${error}`
              );
            }
          }

          const convertedAvailability = userObj.availability.map(
            (availability) => {
              return {
                ...availability,
                timeSlots: availability.timeSlots.map((slot) => {
                  const startDateTime = moment.tz(
                    slot.start,
                    "HH:mm",
                    timezone
                  );
                  const endDateTime = moment.tz(slot.end, "HH:mm", timezone);

                  if (!startDateTime.isBefore(endDateTime)) {
                    throw new ApiError(
                      httpStatus.BAD_REQUEST,
                      `End time ${slot.end} must be after start time ${slot.start}`
                    );
                  }

                  const startUTC = startDateTime.utc().format("HH:mm:ss[Z]");
                  const endUTC = endDateTime.utc().format("HH:mm:ss[Z]");

                  return {
                    ...slot,
                    start: startUTC,
                    end: endUTC,
                  };
                }),
              };
            }
          );

          userObj.availability = convertedAvailability;

          user = await authService.createAdviser(userObj);
          break;

        case "Admin":
          user = await authService.createAdmin(userObj);
          break;

        default:
          throw new ApiError(httpStatus.BAD_REQUEST, "Invalid route type");
      }

      if (!user) {
        throw new ApiError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Failed to create user"
        );
      }

      return res.status(httpStatus.CREATED).send({ data: user });
    } catch (error) {
      if (error.code === 11000) {
        const duplicateField = Object.keys(error.keyValue)[0];
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          `${duplicateField.charAt(0).toUpperCase() + duplicateField.slice(1)} already exists in db`
        );
      }
      throw error;
    }
  }
});

const health = catchAsync(async (req, res) => {
  return res.status(httpStatus.OK).send({
    msg: `Money owl ${config.env === "production" ? "production" : config.env} server working 🔥`,
  });
});

const generateToken = catchAsync(async (req, res) => {
  const token = await admin.auth().createCustomToken(req.body.uid);
  const {
    data: { idToken },
  } = await axios({
    url: `https://www.googleapis.com/identitytoolkit/v3/relyingparty/verifyCustomToken?key=
AIzaSyD7kSvHPb6TyCPXYFychZri1M7qMp9ErL8`,
    method: "post",
    data: {
      token,
      returnSecureToken: true,
    },
    json: true,
  });

  return res.json({
    data: {
      status: "succeddddd",
      token: idToken,
    },
  });
});

const emailCheck = catchAsync(async (req, res) => {
  const email = req.body.email;

  const user = await Users.findOne({ email });

  if (user) {
    return res.status(httpStatus.OK).json({ valid: false });
  }

  return res.status(httpStatus.OK).json({ valid: true });
});

const resetPasswordEmailCheck = catchAsync(async (req, res) => {
  const email = req.body.email;
  const firebaseUid = req.body.uid;

  const user = await Users.findOne({ email });

  if (user && user.firebaseUid === firebaseUid) {
    return res.status(httpStatus.OK).json({ valid: true });
  }

  return res.status(httpStatus.OK).json({ valid: false });
});

const activeUser = catchAsync(async (req, res) => {
  let fireBaseUid = req.user.uid;

  // Regular user, check if their account is blocked

  const updatedUser = await Users.findOneAndUpdate(
    { firebaseUid: fireBaseUid },
    {
      isBlockedByAdmin: false,
      isDeleted: false,
    },
    { new: true }
  );

  if (!updatedUser) {
    console.error(`User with UID: ${fireBaseUid} not found or already deleted`);
    throw new ApiError(httpStatus.NOT_FOUND, "User not found or deleted");
  }

  // console.log(`User with ID: ${fireBaseUid} activated successfully`);

  return res
    .status(httpStatus.OK)
    .json({ message: "User Activated successfully" });
});

module.exports = {
  loginUser,
  registerUser,
  health,
  generateToken,
  emailCheck,
  resetPasswordEmailCheck,
  activeUser,
};
