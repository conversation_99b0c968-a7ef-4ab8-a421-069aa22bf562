const {
  emitterEventNames,
  notificationCategories,
} = require("../constants/index");
const moment = require("moment-timezone");
const path = require("path");
const ejs = require("ejs");
const {
  appNotificationService,
  agendaService,
  subscriptionProductService,
} = require("../services");
const catchEventHandler = require("../utils/catchEventHandler");
const { agenda } = require("../services/agenda.service");
const { notificationService } = require("../microservices");
const { mailService } = require("../microservices");

const handleSubscriptionCreated = catchEventHandler(
  async (subscriptionData) => {
    const { stripeSubscriptionId, endDate } = subscriptionData;

    if (
      !subscriptionData.stripeSubscriptionId ||
      !subscriptionData.endDate ||
      !subscriptionData.subscriptionProduct ||
      !subscriptionData.user
    ) {
      console.error("❌ Invalid subscription data. Expiry job not scheduled.");
      return;
    }

    // console.log(
    //   `✅ Scheduling Subscription Expiry Job for ${subscriptionData.endDate} in event emitter`
    // );

    // await agendaService.scheduleExpireSubscription({
    //   stripeSubscriptionId,
    //   endDate,
    //   subscriptionProduct: subscriptionData.subscriptionProduct,
    //   user: subscriptionData.user,
    // });

    await subscriptionProductService.increaseSubscriberCount(
      subscriptionData.subscriptionProduct._id
    );

    // Send push notification
    await notificationService.sendToTopic(
      subscriptionData.user._id.toString(),
      {
        title: "Welcome to the subscribers club",
        body: "Subscription is Active",
        image: "",
      },
      {
        type: notificationCategories.SUBSCRIPTION,
      }
    );

    // Create in-app notification
    await appNotificationService.createNotification({
      title: "Welcome to the subscribers club",
      description: "Your Subscription is Active",
      type: notificationCategories.SUBSCRIPTION,
      targetUser: subscriptionData.user._id,
      scheduledAt: Date.now(),
      data: {
        subscriptionId: subscriptionData._id.toString(),
      },
    });

    // Send email notification
    try {
      // console.log(
      //   `📧 Sending subscription created email to ${subscriptionData.user.email}`
      // );

      // Format dates safely
      let startDate = "N/A";
      let nextBillingDate = "N/A";

      try {
        if (subscriptionData.startDate) {
          startDate = moment(subscriptionData.startDate).format("D MMM YYYY");
        }
      } catch (error) {
        console.error(
          `❌ Error formatting subscription start date: ${error.message}`
        );
      }

      try {
        if (subscriptionData.endDate) {
          nextBillingDate = moment(subscriptionData.endDate).format(
            "D MMM YYYY"
          );
        }
      } catch (error) {
        console.error(
          `❌ Error formatting subscription end date: ${error.message}`
        );
      }

      // Get amount safely
      let amount = "0.00";
      try {
        amount = parseFloat(
          subscriptionData.amount ||
            (subscriptionData.subscriptionProduct &&
              subscriptionData.subscriptionProduct.price) ||
            0
        ).toFixed(2);
      } catch (error) {
        console.error(
          `❌ Error formatting subscription amount: ${error.message}`
        );
      }

      // Render email template
      const emailHtml = await ejs.renderFile(
        path.join(__dirname, "../views/emails/subscriptionCreated.ejs"),
        {
          name: subscriptionData.user.name || "",
          email: subscriptionData.user.email,
          subscriptionName: subscriptionData.subscriptionProduct.name,
          amount: amount,
          billingCycle: subscriptionData.subscriptionProduct.billingCycle,
          startDate: startDate,
          nextBillingDate: nextBillingDate,
          maxSessions: subscriptionData.subscriptionProduct.maxSessions,
        }
      );

      // Send email
      await mailService.sendEmail({
        to: subscriptionData.user.email,
        subject: "Welcome to Money Owls Premium!",
        html: emailHtml,
      });

      // console.log(
      //   `✅ Subscription created email sent to ${subscriptionData.user.email}`
      // );
    } catch (error) {
      console.error(
        `❌ Error sending subscription created email: ${error.message}`
      );
    }
  }
);

const handleSubscriptionUpdated = catchEventHandler(
  // got the entire customer data with populated
  async (subscriptionData, nextBillingDate) => {
    const { stripeSubscriptionId, endDate } = subscriptionData;

    if (
      !subscriptionData.stripeSubscriptionId ||
      !subscriptionData.endDate ||
      !subscriptionData.subscriptionProduct ||
      !subscriptionData.user
    ) {
      console.error("❌ Invalid subscription data. Expiry job not scheduled.");
      return;
    }

    // console.log(
    //   `✅ Scheduling Subscription Expiry Job for ${subscriptionData.endDate.getTime()} in event emitter`
    // );

    // Send push notification to user
    await notificationService.sendToTopic(
      subscriptionData.user._id.toString(),
      {
        title: "Subscription Renewed",
        body: "Your subscription has been renewed.",
        image: "",
      },
      {
        type: notificationCategories.SUBSCRIPTION,
      }
    );

    await appNotificationService.createNotification({
      title: "Subscription Renewed",
      description: "Your subscription has been renewed.",
      type: notificationCategories.SUBSCRIPTION,
      targetUser: subscriptionData.user._id,
      scheduledAt: Date.now(),
      data: {
        subscriptionId: subscriptionData._id.toString(),
      },
    });

    // Send email notification
    try {
      // console.log(
      //   `📧 Sending subscription renewal email to ${subscriptionData.user.email}`
      // );

      // Format dates safely
      let renewalDate = "today";

      try {
        renewalDate = moment().format("D MMM YYYY");
      } catch (error) {
        console.error(`❌ Error formatting renewal date: ${error.message}`);
      }

      // Get amount safely
      let amount = "0.00";
      try {
        amount = parseFloat(
          subscriptionData.amount ||
            (subscriptionData.subscriptionProduct &&
              subscriptionData.subscriptionProduct.price) ||
            0
        ).toFixed(2);
      } catch (error) {
        console.error(
          `❌ Error formatting subscription amount: ${error.message}`
        );
      }

      // Render email template
      const emailHtml = await ejs.renderFile(
        path.join(__dirname, "../views/emails/subscriptionRenewed.ejs"),
        {
          name: subscriptionData.user.name || "",
          email: subscriptionData.user.email,
          subscriptionName: subscriptionData.subscriptionProduct.name,
          amount: amount,
          billingCycle: subscriptionData.subscriptionProduct.billingCycle,
          renewalDate: renewalDate,
          nextBillingDate: moment(nextBillingDate).format("D MMM YYYY"),
          maxSessions: subscriptionData.subscriptionProduct.maxSessions,
          // dashboardUrl: "https://moneyowls.co.uk/dashboard",
        }
      );

      // Send email
      await mailService.sendEmail({
        to: subscriptionData.user.email,
        subject: "Your Money Owls Subscription Has Been Renewed",
        html: emailHtml,
      });

      // console.log(
      //   `✅ Subscription renewal email sent to ${subscriptionData.user.email}`
      // );
    } catch (error) {
      console.error(
        `❌ Error sending subscription renewal email: ${error.message}`
      );
    }
  }
);

const handleSubscriptionReminder = catchEventHandler(async (data) => {
  // make it after 24 hrs
  try {
    const { userId, triggerDate } = data;

    // console.log(`🔔 Processing subscription reminder for user ${userId}`);

    // Determine notification content based on type

    const checkDate = moment().add(4, "days").toDate();

    const notificationDate = moment(triggerDate).toDate();

    await agenda.schedule(notificationDate, "send-subscription-reminder", {
      userId: userId,
    });

    await agenda.schedule(checkDate, "check-subscription-status", {
      userId: userId,
    });

    // console.log(
    //   `✅ Subscription reminder notification scheduled for user ${userId}`
    // );
  } catch (error) {
    console.error("❌ Error processing subscription reminder:", error);
  }
});

const handleSubscriptionPaymentFailed = catchEventHandler(
  async (subscriptionData) => {
    try {
      // console.log(
      //   `❌ Processing subscription payment failure for subscription ${subscriptionData.stripeSubscriptionId}`
      // );

      if (
        !subscriptionData ||
        !subscriptionData.user ||
        !subscriptionData.user._id
      ) {
        console.error(
          "❌ Invalid subscription data for payment failure notification"
        );
        return;
      }

      const userId = subscriptionData.user._id.toString();
      const subscriptionProduct = subscriptionData.subscriptionProduct;
      const productName = subscriptionProduct
        ? subscriptionProduct.name
        : "subscription";

      // Send push notification to user
      await notificationService.sendToTopic(
        userId,
        {
          title: "❗ Subscription Payment Failed",
          body: `⚠️ Your payment for ${productName} has failed. Please update your payment method to continue your subscription.`,
          image: "",
        },
        {
          type: notificationCategories.SUBSCRIPTION,
          subscriptionId: subscriptionData._id.toString(),
        }
      );

      // Create in-app notification
      await appNotificationService.createNotification({
        title: "❗ Subscription Payment Failed",
        description: `Your payment for ${productName} has failed. Please update your payment method to continue your subscription.`,
        type: notificationCategories.SUBSCRIPTION,
        targetUser: userId,
        scheduledAt: Date.now(),
        data: {
          subscriptionId: subscriptionData._id.toString(),
        },
      });

      // Send email notification
      if (subscriptionData.user.email) {
        try {
          // console.log(
          //   `📧 Sending subscription payment failed email to ${subscriptionData.user.email}`
          // );

          // Get amount safely
          let amount = "0.00";
          try {
            amount = parseFloat(
              subscriptionData.amount ||
                (subscriptionData.subscriptionProduct &&
                  subscriptionData.subscriptionProduct.price) ||
                0
            ).toFixed(2);
          } catch (error) {
            console.error(
              `❌ Error formatting payment failed amount: ${error.message}`
            );
          }

          // Get billing cycle safely
          const billingCycle =
            subscriptionProduct && subscriptionProduct.billingCycle
              ? subscriptionProduct.billingCycle
              : "month";

          // Render email template
          const emailHtml = await ejs.renderFile(
            path.join(
              __dirname,
              "../views/emails/subscriptionPaymentFailed.ejs"
            ),
            {
              name: subscriptionData.user.name || "",
              email: subscriptionData.user.email,
              subscriptionName: productName,
              amount: amount,
              billingCycle: billingCycle,
              updatePaymentUrl:
                "https://moneyowls.co.uk/dashboard/payment-methods",
            }
          );

          // Send email
          await mailService.sendEmail({
            to: subscriptionData.user.email,
            subject: "Action Required: Subscription Payment Failed",
            html: emailHtml,
          });

          // console.log(
          //   `✅ Subscription payment failed email sent to ${subscriptionData.user.email}`
          // );
        } catch (emailError) {
          console.error(
            `❌ Error sending subscription payment failed email: ${emailError.message}`
          );
        }
      }

      // console.log(
      //   `✅ Subscription payment failure notification sent to user ${userId}`
      // );
    } catch (error) {
      console.error("❌ Error processing subscription payment failure:", error);
    }
  }
);

const handleSubscriptionSessionReturned = catchEventHandler(async (data) => {
  const { appointment, userId, adviserId } = data;

  // console.log(
  //   `🔔 Processing subscription session returned notification for user ${userId}`
  // );

  try {
    // Create notification title and description for user
    const userTitle = "Subscription Session Returned";
    const userDescription =
      "You can now book a new appointment using your subscription.";

    // Send push notification to user
    await notificationService.sendToTopic(
      userId.toString(),
      {
        title: userTitle,
        body: userDescription,
        image: "",
      },
      {
        type: notificationCategories.SUBSCRIPTION,
        appointmentId: appointment._id.toString(),
      }
    );

    // Create in-app notification for user
    await appNotificationService.createNotification({
      title: userTitle,
      description: userDescription,
      type: notificationCategories.SUBSCRIPTION,
      targetUser: userId,
      scheduledAt: Date.now(),
      targetRole: null,
      isCreatedByAdmin: false,
      data: {
        appointmentId: appointment._id.toString(),
        adviserId: adviserId,
      },
    });

    // console.log(
    //   `✅ Subscription session returned notification sent to user ${userId}`
    // );
  } catch (error) {
    console.error(
      `❌ Error sending subscription session returned notification: ${error.message}`
    );
  }
});
// ✅ Register event listeners
module.exports = (emitter) => {
  emitter.on(emitterEventNames.SUBSCRIPTION_CREATED, handleSubscriptionCreated);
  emitter.on(emitterEventNames.SUBSCRIPTION_RENEWED, handleSubscriptionUpdated);
  emitter.on(
    emitterEventNames.SUBSCRIPTION_REMINDER,
    handleSubscriptionReminder
  );
  emitter.on(
    emitterEventNames.SUBSCRIPTION_PAYMENT_FAILED,
    handleSubscriptionPaymentFailed
  );
  emitter.on(
    emitterEventNames.SUBSCRIPTION_SESSION_RETURNED,
    handleSubscriptionSessionReturned
  );
};
