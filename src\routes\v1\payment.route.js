const express = require("express");
const validate = require("../../middlewares/validate");
const { firebaseAuth } = require("../../middlewares/firebaseAuth");
const { stripeController } = require("../../controllers");

const router = express.Router();

router.post("/card/add", firebase<PERSON><PERSON>("User"), stripeController.saveCard);

router.get("/cards", firebase<PERSON>uth("User"), stripeController.getAllSavedCards);

router.post(
  "/card/change",
  firebase<PERSON>uth("User"),
  stripeController.changeDefaultCard
); // in subscription

router.post(
  "/card/default",
  firebaseAuth("User"),
  stripeController.makeCardDefault
); // make the card default

module.exports = router;
