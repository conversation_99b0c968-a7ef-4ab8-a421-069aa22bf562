const httpStatus = require("http-status");
const ApiError = require("../utils/ApiError");
const catchAsync = require("../utils/catchAsync");
const { appointmentService, refundService } = require("../services");
const { getPaginateConfig } = require("../utils/queryPHandler");

// doubt
const getRefundPercentage = catchAsync(async (req, res) => {
  const [totalPaidAppointments, cancelledPaidAppointments] = await Promise.all([
    appointmentService.countAppointments({
      paymentStatus: "paid",
      mode: "payment", // Count only payment appointments
    }),
    appointmentService.countAppointments({
      paymentStatus: "paid", // Count only paid appointments
      status: "cancelled", // and those that are cancelled
      mode: "payment", // Count only payment appointments
    }),
  ]);

  if (!totalPaidAppointments) {
    return res.status(httpStatus.OK).json({
      success: true,
      message: "No paid appointments found",
      data: {
        totalPaidAppointments: 0,
        cancelledPaidAppointments: 0,
        cancelledPercentage: 0,
      },
    });
  }

  // Calculate the cancelled percentage based on paid appointments
  const cancelledPercentage = Number(
    ((cancelledPaidAppointments / totalPaidAppointments) * 100).toFixed(2)
  );

  return res.status(httpStatus.OK).json({
    success: true,
    message: "Refund percentage retrieved successfully",
    data: {
      totalPaidAppointments,
      cancelledPaidAppointments,
      cancelledPercentage,
    },
  });
});

const getRefunds = catchAsync(async (req, res) => {
  // Build filter object

  const { filters, options } = getPaginateConfig(req.query);

  if (req.user.__t === "Admin") {
    filters.isIntiatedByAdmin = true;
  }
  if (req.user.__t === "User") {
    filters.userId = req.user._id;
  }

  // Get refunds with pagination
  const refunds = await refundService.getAllRefunds(filters, options);

  // Get total count for pagination

  return res.status(httpStatus.OK).json({
    success: true,
    message: "Refunds retrieved successfully",
    data: refunds,
  });
});

module.exports = {
  getRefundPercentage,
  getRefunds,
};
