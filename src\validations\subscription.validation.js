const Joi = require("joi");
const { objectId } = require("./custom.validation");

/**
 * Validation schema for creating a subscription
 */
const createSubscription = {
  body: Joi.object().keys({
    productId: Joi.string()
      .custom(objectId)
      .required()
      .messages({
        "string.empty": "Product ID is required",
        "any.required": "Product ID is required",
        "string.pattern.base": "Product ID must be a valid MongoDB ObjectId",
      })
      .required(),
    cardId: Joi.string()
      .messages({
        "string.base": "Card ID must be a string",
      })
      .required(),
  }),
};

/**
 * Validation schema for cancelling a subscription
 */
const cancelSubscription = {
  body: Joi.object().keys({
    // No specific fields required for cancellation as it uses the user's active subscription
  }),
};

module.exports = {
  createSubscription,
  cancelSubscription,
};
