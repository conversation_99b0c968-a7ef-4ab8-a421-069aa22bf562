const { Query } = require("../models");

async function getAllQueries(filters = {}, options = {}) {
  let query = { ...filters };

  if (filters.status) {
    query.status = filters.status;
  }

  options.populate = [
    "appointment::bookingId",
    "raisedBy::name,profilePic,__t",
  ];

  const queries = await Query.paginate(query, options);

  return queries;
}

async function getQueryWhere(receivedQuery, populateFields = []) {
  let query = Query.find(receivedQuery);

  populateFields.forEach((field) => {
    query = query.populate(field);
  });

  const adviser = await query.exec();
  return adviser;
}

async function createQuery(queryData) {
  const query = new Query(queryData);
  return await query.save();
}

async function updateQuery(id, updateData) {
  const query = await Query.findByIdAndUpdate(id, updateData, {
    new: true,
    runValidators: true,
  });

  return query;
}

module.exports = {
  createQuery,
  getQueryWhere,
  getAllQueries,
  updateQuery,
};
