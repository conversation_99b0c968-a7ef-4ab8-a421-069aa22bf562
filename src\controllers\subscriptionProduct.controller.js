const httpStatus = require("http-status");
const ApiError = require("../utils/ApiError");
const catchAsync = require("../utils/catchAsync");
const { subscriptionProductService, stripeService } = require("../services");

const createSubscriptionProduct = catchAsync(async (req, res) => {
  const { name, description, price, billingCycle, maxSessions } = req.body;

  if (!name || !description || !price || !billingCycle || !maxSessions) {
    throw new ApiError(httpStatus.BAD_REQUEST, "All fields are required");
  }

  const newStripeProduct = await stripeService.createSubscriptionProduct(
    name,
    price,
    billingCycle,
    description
  );

  const productStripePrice = await stripeService.createStripePrice(
    price,
    billingCycle,
    newStripeProduct.id
  );

  const subscriptionProduct =
    await subscriptionProductService.createSubscriptionProduct(
      {
        ...req.body,
        stripePriceId: productStripePrice.id,
        stripeProductId: newStripeProduct.id,
        isActive: newStripeProduct.active,
      },
      req.user._id
    );

  return res.status(httpStatus.CREATED).json({
    message: "Subscription Product created successfully",
    subscriptionProduct,
  });
});

const getAllSubscriptionProducts = catchAsync(async (req, res) => {
  const subscriptionProducts =
    await subscriptionProductService.getAllSubscriptionProducts();

  return res.status(httpStatus.OK).json({ subscriptionProducts });
});

const getSubscriptionProductById = catchAsync(async (req, res) => {
  const { id } = req.params;
  const subscriptionProduct =
    await subscriptionProductService.getSubscriptionProductById(id);

  if (!subscriptionProduct) {
    throw new ApiError(httpStatus.NOT_FOUND, "Subscription Product not found");
  }

  return res.status(httpStatus.OK).json(subscriptionProduct);
});

const updateSubscriptionProduct = catchAsync(async (req, res) => {
  const { id } = req.params;
  const { name, description, isActive, price, billingCycle } = req.body;

  const existingSubscriptionProduct =
    await subscriptionProductService.getSubscriptionProductById(id);

  if (!existingSubscriptionProduct) {
    throw new ApiError(httpStatus.NOT_FOUND, "Subscription Product not found");
  }

  let stripeProductUpdate, stripePriceUpdate;

  try {
    // ✅ Update Stripe Product
    stripeProductUpdate = await stripeService.updateSubscriptionProduct(
      existingSubscriptionProduct.stripeProductId,
      { isActive, name, description }
    );

    if (!stripeProductUpdate.status) {
      throw new ApiError(
        httpStatus.INTERNAL_SERVER_ERROR,
        "Failed to update Stripe Product"
      );
    }

    // ✅ Handle Price Update: Stripe does NOT allow price modification, so we create a new price
    if (price && price !== existingSubscriptionProduct.price) {
      // ✅ Archive the old price in Stripe (optional)
      await stripeService.archiveStripePrice(
        existingSubscriptionProduct.stripePriceId
      );

      // ✅ Create a new Stripe Price
      stripePriceUpdate = await stripeService.createStripePrice(
        price,
        billingCycle || existingSubscriptionProduct.billingCycle,
        existingSubscriptionProduct.stripeProductId
      );

      if (!stripePriceUpdate.active) {
        throw new ApiError(
          httpStatus.INTERNAL_SERVER_ERROR,
          "Failed to create new Stripe Price"
        );
      }
    }
  } catch (error) {
    throw new ApiError(
      httpStatus.INTERNAL_SERVER_ERROR,
      error.message || "Stripe update failed"
    );
  }

  // ✅ Prepare update data (only include `stripePriceId` if we created a new one)
  const updateData = {
    ...req.body,
    ...(stripePriceUpdate ? { stripePriceId: stripePriceUpdate.id } : {}),
  };

  const updatedSubscriptionProduct =
    await subscriptionProductService.updateSubscriptionProduct(id, updateData);

  if (!updatedSubscriptionProduct) {
    throw new ApiError(httpStatus.NOT_FOUND, "Subscription Product not found");
  }

  return res.status(httpStatus.OK).json({
    message: "Subscription Product updated successfully",
    updatedSubscriptionProduct,
  });
});

const deleteSubscriptionProduct = catchAsync(async (req, res) => {
  const { id } = req.params;
  const deleted =
    await subscriptionProductService.deleteSubscriptionProduct(id);

  if (!deleted) {
    throw new ApiError(httpStatus.NOT_FOUND, "Subscription Product not found");
  }

  return res
    .status(httpStatus.OK)
    .json({ message: "Subscription Product deleted successfully" });
});

module.exports = {
  createSubscriptionProduct,
  getAllSubscriptionProducts,
  getSubscriptionProductById,
  updateSubscriptionProduct,
  deleteSubscriptionProduct,
};
