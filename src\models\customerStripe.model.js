const mongoose = require("mongoose");

const customerStripeSchema = new mongoose.Schema(
  {
    user: {
      type: mongoose.SchemaTypes.ObjectId,
      ref: "User",
      required: [true, "A customer must belong to a user"],
    },
    stripeCustomerId: {
      type: String,
      required: [true, "A customer must have a stripeCustomerId"],
    },
    stripeCardId: {
      type: [String],
    },
  },
  { timestamps: true }
);

const CustomerStripe = mongoose.model("Customer", customerStripeSchema);

module.exports = { CustomerStripe };
