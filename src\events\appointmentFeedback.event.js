const {
  emitterEventNames,
  notificationCategories,
  adviserFeedbackResponses,
} = require("../constants");
const { notificationService } = require("../microservices");
const { appNotificationService } = require("../services");
const catchEventHandler = require("../utils/catchEventHandler");

const handleAppointmentFeedbackFollowup = catchEventHandler(async (data) => {
  const { appointmentId, userId, adviserId, adviserName, feedbackType } = data;

  // Determine notification content based on feedback type
  let title, description;

  if (feedbackType === adviserFeedbackResponses.FOLLOW_UP_RECOMMENDED) {
    title = "Follow-Up Recommended";
    description = `${adviserName} has recommended a follow-up call based on your recent appointment. This can help address additional questions or concerns.`;
  } else if (
    feedbackType === adviserFeedbackResponses.NEEDS_REGULATED_GUIDANCE
  ) {
    title = "Regulated Financial Guidance Recommended";
    description = `${adviserName} suggests you may benefit from regulated financial guidance based on your recent appointment. This can provide more specific guidance for your situation.`;
  }

  await notificationService.sendToTopic(
    userId._id.toString(),
    {
      title,
      body: description,
      image: "",
    },
    {
      type: notificationCategories.FOLLOWUP,
      appointmentId: appointmentId.toString(),
    }
  );

  // Create and send notification
  await appNotificationService.createNotification({
    title,
    description,
    type: "follow-up",
    targetUser: userId._id,
    targetRole: null,
    scheduledAt: Date.now(),
    isCreatedByAdmin: false,
    data: {
      appointmentId,
      adviserId,
    },
  });
});

// Register event handlers
module.exports = (emitter) => {
  emitter.on(
    emitterEventNames.APPOINTMENT_FEEDBACK_FOLLOWUP,
    handleAppointmentFeedbackFollowup
  );
};
