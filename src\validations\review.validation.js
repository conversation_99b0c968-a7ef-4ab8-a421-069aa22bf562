const Joi = require("joi");

const { dbOptionsSchema, objectId } = require("./custom.validation");

const addReview = {
  params: Joi.object().keys({
    adviserId: Joi.string().custom(objectId).required(),
  }),
  body: Joi.object().keys({
    comment: Joi.string().required(),
    rating: Joi.number().min(0).max(5).optional().required(),
  }),
};

const appReview = {
  body: Joi.object().keys({
    rating: Joi.number().min(1).max(5).required(),
    review: Joi.string().max(300).required(),
  }),
};

const getAppFeedbacks = {
  query: Joi.object().keys({
    ...dbOptionsSchema,
    rating: Joi.number().min(0).max(5).optional(),
  }),
};

module.exports = {
  addReview,
  appReview,
  getAppFeedbacks,
};
