const express = require("express");
const router = express.Router();
const { firebaseAuth } = require("../../middlewares/firebaseAuth");
const { faqController } = require("../../controllers");

router.post("/", firebaseAuth("Admin"), faqController.createFaq);

router.get("/", faqController.getAllFaqs);

router.get("/:id", faqController.getFaqById);

router.patch("/update/:id", firebaseAuth("Admin"), faqController.updateFaq);

router.delete("/:id", firebaseAuth("Admin"), faqController.deleteFaq);

module.exports = router;
