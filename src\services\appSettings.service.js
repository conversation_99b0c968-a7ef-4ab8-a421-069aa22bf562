const httpStatus = require("http-status");
const { AppSettings } = require("../models");
const ApiError = require("../utils/ApiError");

const getSetting = async (key) => {
  const setting = await AppSettings.findOne({ key });

  if (!setting) {
    throw new ApiError(httpStatus.NOT_FOUND, `Setting "${key}" not found.`);
  }

  return setting.value;
};

const setSetting = async (key, value) => {
  const updatedSetting = await AppSettings.findOneAndUpdate(
    { key },
    { $set: { value } }, // explicit $set helps avoid edge case issues
    {
      new: true,
      upsert: true,
      runValidators: true, // if you add validation later
    }
  );
  return updatedSetting;
};

const deleteSetting = async (key) => {
  const deleted = await AppSettings.findOneAndDelete({ key });
  if (!deleted) {
    throw new ApiError(httpStatus.NOT_FOUND, `Setting "${key}" not found.`);
  }
  return deleted;
};

const getAllSettings = async () => {
  const settings = await AppSettings.find({});
  return settings.map((setting) => ({
    key: setting.key,
    value: setting.value,
  }));
};

module.exports = {
  getSetting,
  setSetting,
  deleteSetting,
  getAllSettings,
};
