<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Subscription Payment Failed</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 600px;
            margin: 20px auto;
            background-color: #fff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        .header {
            background-color: #ffffff;
            padding: 20px;
            text-align: center;
        }

        .logo {
            max-width: 150px;
            height: auto;
        }

        .content {
            padding: 30px;
        }

        .title {
            color: #00AA9D;
            margin-top: 0;
        }

        .info-box {
            background-color: #f9f9f9;
            border-left: 4px solid #00AA9D;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }

        .button {
            display: inline-block;
            background-color: #00AA9D;
            color: white;
            text-decoration: none;
            padding: 12px 25px;
            border-radius: 4px;
            margin: 20px 0;
            font-weight: bold;
        }

        .footer {
            background-color: #f5f5f5;
            padding: 20px;
            text-align: center;
            font-size: 12px;
            color: #666;
        }

        @media only screen and (max-width: 600px) {
            .container {
                width: 100%;
                margin: 0;
                border-radius: 0;
            }
        }

        @media only screen and (max-width: 600px) {
            .container {
                padding: 20px;
                margin: 0px;
            }

            .title {
                font-size: 22px;
            }

            .header,
            .footer {
                padding: 10px;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <img src="https://moneyowl.s3.eu-north-1.amazonaws.com/public/logos/final+logo+moneyowsl.png"
                alt="Money Owls Logo" class="logo">
        </div>

        <div class="content">
            <h2 class="title">Subscription Payment Failed</h2>

            <p>Hello <%= name %>,</p>

            <p>We're writing to inform you that your payment for the <strong>
                    <%= subscriptionName %>
                </strong> subscription has failed.</p>

            <div class="info-box">
                <p><strong>Subscription Details:</strong></p>
                <% if (typeof subscriptionName !=='undefined' && subscriptionName) { %>
                    <p>Plan: <%= subscriptionName %>
                    </p>
                    <% } %>
                        <% if (typeof amount !=='undefined' && amount) { %>
                            <p>Amount: £<%= amount %>
                            </p>
                            <% } %>
                                <% if (typeof billingCycle !=='undefined' && billingCycle) { %>
                                    <p>Billing Period: <%= billingCycle %>
                                    </p>
                                    <% } %>
            </div>

            <p>To continue enjoying your subscription benefits, please update your payment method in your account
                settings.</p>

            <a href="<%= updatePaymentUrl %>" class="button">Update Payment Method</a>

            <p>If you need any assistance, please don't hesitate to contact our support team at <a
                    href="mailto:<EMAIL>"><EMAIL></a>.</p>

            <p>Thank you for choosing Money Owls.</p>

            <p>Best regards,<br>The Money Owls Team</p>
        </div>

        <div class="footer">
            <p>&copy; <%= new Date().getFullYear() %> Money Owls. All rights reserved.</p>
            <p>This email was sent to <%= email %>.</p>
        </div>
    </div>
</body>

</html>