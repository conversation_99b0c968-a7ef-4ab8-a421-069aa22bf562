const {
  appointmentService,
  stripeService,
  customerSubscriptionService,
  refundService,
  appSettingsService,
  singleSessionProductService,
  userService,
  payoutService,
} = require("../services/index");
const eventEmitter = require("../events/eventEmitter");

const moment = require("moment");
const ApiError = require("../utils/ApiError");
const catchAsync = require("../utils/catchAsync");
const { getPaginateConfig } = require("../utils/queryPHandler");
const { Appointment, Adviser, User, WhiteBoard } = require("../models");
const { generateBookingId } = require("../utils/helper");
const httpStatus = require("http-status");
const mongoose = require("mongoose");

const { userTypes, appDefaults, emitterEventNames } = require("../constants");
const { s3Upload } = require("../microservices/fileUpload.service");

// Agenda is now handled through the event system

const createAppointment = catchAsync(async (req, res) => {
  const session = await mongoose.startSession();
  session.startTransaction();

  try {
    const { date, timeSlot, mode } = req.body;
    const { adviserId } = req.params;
    const { followUp } = req.query;
    const userId = req.user._id;
    const timezone = req.headers?.timezone || appDefaults.TIMEZONE;

    // Convert times to moment and validate
    const startTimeLocal = moment.tz(
      `${date} ${timeSlot.start}`,
      "YYYY-MM-DD HH:mm",
      timezone
    );
    const endTimeLocal = moment.tz(
      `${date} ${timeSlot.end}`,
      "YYYY-MM-DD HH:mm",
      timezone
    );
    const now = moment.tz(timezone);

    if (
      !startTimeLocal.isValid() ||
      !endTimeLocal.isValid() ||
      !startTimeLocal.isBefore(endTimeLocal)
    ) {
      throw new ApiError(httpStatus.BAD_REQUEST, "Invalid timeSlot format");
    }

    if (startTimeLocal.isBefore(now)) {
      throw new ApiError(
        httpStatus.BAD_REQUEST,
        "You cannot book an appointment in the past."
      );
    }

    const duration = endTimeLocal.diff(startTimeLocal, "minutes");
    const timeSlotStartInUTC = startTimeLocal.utc().toISOString();
    const timeSlotEndInUTC = endTimeLocal.utc().toISOString();
    const appointmentDateInUTC = startTimeLocal
      .clone()
      .startOf("day")
      .utc()
      .toISOString();

    // Adviser check
    const adviser = await Adviser.findOne({
      _id: adviserId,
      isDeleted: false,
      isVerified: true,
    }).session(session);

    if (!adviser) {
      throw new ApiError(httpStatus.NOT_FOUND, "Adviser not found");
    }

    // Check for overlapping slots (adviser + user)
    const checkOverlapQuery = {
      status: "scheduled",
      paymentStatus: { $in: ["paid", "processing", "free"] },
      $or: [
        {
          "timeSlot.start": { $lt: timeSlotEndInUTC },
          "timeSlot.end": { $gt: timeSlotStartInUTC },
        },
      ],
    };

    const [adviserOverlap, userOverlap] = await Promise.all([
      Appointment.findOne({ adviser: adviserId, ...checkOverlapQuery }).session(
        session
      ),
      Appointment.findOne({ user: userId, ...checkOverlapQuery }).session(
        session
      ),
    ]);

    if (adviserOverlap) {
      throw new ApiError(httpStatus.CONFLICT, "Time slot is already booked");
    }

    if (userOverlap) {
      throw new ApiError(
        httpStatus.CONFLICT,
        "You already have an appointment in this time slot."
      );
    }

    const bookingId = generateBookingId();
    let sessionPrice = 0;
    let commissionPercentage = 0;
    let paymentStatus = "processing"; // default for 'payment' mode

    // Pricing logic
    if (mode === "subscription") {
      const sub = await customerSubscriptionService.getCustomerSubscription({
        user: userId,
        endDate: { $gte: new Date() },
      });

      if (!sub || sub.remainingSessions <= 0 || !sub.isActive) {
        throw new ApiError(
          httpStatus.FORBIDDEN,
          "Invalid or expired subscription."
        );
      }

      commissionPercentage =
        await appSettingsService.getSetting("platformCommission");
      sessionPrice = sub.amount;
      paymentStatus = "paid";
      await customerSubscriptionService.reduceSessionsCount(userId);
    }

    if (mode === "free") {
      if (req.user.freeSessions <= 0) {
        throw new ApiError(
          httpStatus.FORBIDDEN,
          "Max free sessions completed."
        );
      }

      sessionPrice = 0;
      commissionPercentage = 0;
      paymentStatus = "free";
      await userService.reduceFreeSessions(userId);
    }

    if (mode === "payment") {
      const [commission, session] = await Promise.all([
        appSettingsService.getSetting("platformCommission"),
        singleSessionProductService.getSessionByDuration(duration),
      ]);
      commissionPercentage = commission;
      sessionPrice = session.price;
    }

    // Optional follow-up linkage
    let parentAppointment = null;
    if (followUp === "true") {
      const latestAppointment = await Appointment.findOne(
        { user: userId, adviser: adviserId, status: "completed" },
        null,
        { sort: { createdAt: -1 }, session }
      ).lean();
      parentAppointment = latestAppointment?._id || null;
    }

    const appointmentData = {
      date: appointmentDateInUTC,
      bookingId,
      timeSlot: {
        start: timeSlotStartInUTC,
        end: timeSlotEndInUTC,
      },
      adviser: adviserId,
      user: userId,
      parentAppointment,
      status: "scheduled",
      duration,
      paymentStatus,
      mode,
      amount: sessionPrice,
      commissionPercentage,
    };

    const [appointment] = await Appointment.create([appointmentData], {
      session,
    });

    if (!appointment) {
      throw new ApiError(
        httpStatus.BAD_REQUEST,
        "Appointment could not be created"
      );
    }

    // Emit Notifications
    const appointmentStartTime = moment(timeSlotStartInUTC).tz(timezone);
    const notificationTriggerTime = appointmentStartTime
      .clone()
      .subtract(10, "minutes");

    if (mode !== "payment") {
      eventEmitter.emit(emitterEventNames.APPOINTMENT_NOTIFICATIONS, {
        appointment,
        user: req.user,
        adviser,
        timezone,
      });
    }

    eventEmitter.emit(emitterEventNames.APPOINTMENT_REMINDER, {
      appointment,
      user: req.user,
      adviser,
      notificationTriggerTime,
      appointmentStartTime,
    });

    await session.commitTransaction();
    session.endSession();

    return res.status(httpStatus.CREATED).json({
      message: "Appointment created",
      data: appointment,
    });
  } catch (error) {
    await session.abortTransaction();
    session.endSession();

    throw new ApiError(
      httpStatus.INTERNAL_SERVER_ERROR,
      `Failed to create appointment: ${error.message}`
    );
  }
});

const cancelAppointmentUser = catchAsync(async (req, res) => {
  const { appointmentId } = req.params;
  const userId = req.user._id;

  if (!appointmentId) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Appointment ID is required");
  }

  // ✅ Cancel appointment if owned by the user
  const appointment = await appointmentService.findAndUpdate(
    { user: userId, _id: appointmentId },
    { status: "cancelled" },
    [
      { path: "user", select: "name email" },
      { path: "adviser", select: "name email" },
    ]
  );

  if (!appointment) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      "Appointment could not be cancelled"
    );
  }

  let refundResponse = null;

  switch (appointment.mode) {
    case "free":
      await userService.increaseFreeSessions(userId);
      break;

    case "subscription":
      try {
        const updatedSubscription =
          await customerSubscriptionService.increaseSessionsCount(
            appointment.user._id
          );

        // Emit only if session was incremented successfully
        if (updatedSubscription) {
          eventEmitter.emit(emitterEventNames.SUBSCRIPTION_SESSION_RETURNED, {
            appointment,
            userId,
            adviserId: appointment.adviser._id.toString(),
            adviserName: appointment.adviser.name,
            userName: appointment.user.name,
          });
        }
      } catch (err) {
        console.warn(
          `⚠️ Session return skipped — no active subscription for user ${appointment.user._id}: ${err.message}`
        );
      }
      break;

    case "payment":
      if (appointment.paymentIntent && appointment.paymentStatus === "paid") {
        refundResponse = await stripeService.refundPayment(
          appointment.paymentIntent,
          appointment._id.toString()
        );

        if (!refundResponse.success) {
          if (refundResponse.code === "charge_already_refunded") {
            throw new ApiError(
              httpStatus.BAD_REQUEST,
              "This charge has already been refunded."
            );
          }
          throw new ApiError(
            httpStatus.BAD_REQUEST,
            refundResponse.message || "Refund failed"
          );
        }

        // ✅ Record refund
        await refundService.createRefund({
          appointmentId: appointment._id,
          paymentIntentId: appointment.paymentIntent,
          refundId: refundResponse.refundId,
          amount: appointment.amount,
          reason: "User cancelled the appointment",
        });

        // ✅ Emit refund notification only if refund occurred
        // eventEmitter.emit(emitterEventNames.REFUND_INITIATED, {
        //   amount: appointment.amount,
        //   bookingId: appointment.bookingId,
        //   userId: appointment.user?._id || appointment.user,
        // });
      }
      break;
  }

  // ✅ Emit appointment cancellation notification
  eventEmitter.emit(emitterEventNames.APPOINTMENT_CANCELLED, {
    appointment,
    targetUserId: appointment.adviser._id.toString(),
    name: appointment.user.name,
    triggeredBy: "user",
  });

  // ✅ Final response
  return res.status(httpStatus.OK).json({
    message: "Appointment cancelled",
    refund: refundResponse ? refundResponse.refundId : "No refund",
    data: appointment,
  });
});

const updateAppointment = catchAsync(async (req, res) => {
  const { appointmentId } = req.params;
  const userId = req.user._id;
  const { appointmentData } = req.body;

  const appointment = await appointmentService.findAppointment({
    user: userId,
    _id: appointmentId,
  });

  if (!appointment) {
    throw new ApiError(httpStatus.NOT_FOUND, "appointment not found");
  }

  const updatedAppointment = await appointmentService.updateAppointment(
    appointmentId,
    appointmentData
  );

  if (!updatedAppointment) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      "Appointment could not be updated"
    );
  }

  return res.status(httpStatus.OK).json({
    message: "Appointment updated",
    data: updatedAppointment,
  });
});

const getAppointmentDetails = catchAsync(async (req, res) => {
  const id = req.params.id;
  const appointment = await appointmentService.getAppointmentDetails(id, [
    "user",
    "adviser",
  ]);

  if (!appointment) {
    throw new ApiError(httpStatus.NOT_FOUND, "Appointment not found");
  }

  if (
    appointment.user.toString() !== req.user._id.toString() &&
    appointment.adviser.toString() !== req.user._id.toString() &&
    req.user.__t === userTypes.ADMIN
  ) {
    throw new ApiError(
      httpStatus.UNAUTHORIZED,
      "This appointment doesn't belong to the requested user"
    );
  }

  return res.status(httpStatus.OK).json({
    message: "Appointment retrieved successfully",
    data: appointment,
  });
});

const rescheduleAppointment = catchAsync(async (req, res) => {
  const { date, timeSlot } = req.body;
  const { appointmentId } = req.params;
  const timezone = req?.headers?.timezone || appDefaults.TIMEZONE;

  const existingAppointment = await Appointment.findById(appointmentId)
    .populate("adviser", "name email")
    .populate("user", "name email");

  if (!existingAppointment) {
    throw new ApiError(httpStatus.NOT_FOUND, "Appointment not found");
  }

  if (
    existingAppointment.status === "completed" ||
    existingAppointment.status === "cancelled"
  ) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      "Only scheduled appointments can be rescheduled"
    );
  }

  const now = moment().utc();
  const existingStartTime = moment(existingAppointment.timeSlot.start);
  if (existingStartTime.diff(now, "minutes") < 30) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      "Appointments can only be rescheduled at least 30 minutes before the scheduled start time"
    );
  }

  if (!timeSlot || !timeSlot.start || !timeSlot.end) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Invalid timeSlot details");
  }

  let timeSlotStartInUTC, timeSlotEndInUTC, appointmentDateInUTC;

  try {
    const startTimeLocal = moment.tz(
      `${date} ${timeSlot.start}`,
      "YYYY-MM-DD HH:mm",
      timezone
    );
    const endTimeLocal = moment.tz(
      `${date} ${timeSlot.end}`,
      "YYYY-MM-DD HH:mm",
      timezone
    );

    if (
      !startTimeLocal.isValid() ||
      !endTimeLocal.isValid() ||
      !startTimeLocal.isBefore(endTimeLocal)
    ) {
      throw new Error("Invalid timeSlot format");
    }

    timeSlotStartInUTC = startTimeLocal.utc().toISOString();
    timeSlotEndInUTC = endTimeLocal.utc().toISOString();
    appointmentDateInUTC = startTimeLocal
      .clone()
      .startOf("day")
      .utc()
      .toISOString();
  } catch (error) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      `Invalid date or timeslot format: ${error}`
    );
  }

  // Parallel check for adviser and user overlapping appointments
  const [adviserOverlap, userOverlap] = await Promise.all([
    Appointment.findOne({
      _id: { $ne: appointmentId },
      adviser: existingAppointment.adviser,
      status: "scheduled",
      paymentStatus: { $in: ["paid", "processing", "free"] },
      $or: [
        {
          "timeSlot.start": { $lt: timeSlotEndInUTC },
          "timeSlot.end": { $gt: timeSlotStartInUTC },
        },
      ],
    }),
    Appointment.findOne({
      _id: { $ne: appointmentId },
      user: existingAppointment.user,
      status: "scheduled",
      paymentStatus: { $in: ["paid", "processing", "free"] },
      $or: [
        {
          "timeSlot.start": { $lt: timeSlotEndInUTC },
          "timeSlot.end": { $gt: timeSlotStartInUTC },
        },
      ],
    }),
  ]);

  if (adviserOverlap) {
    throw new ApiError(
      httpStatus.CONFLICT,
      "Time slot is already booked with another client for this adviser"
    );
  }

  if (userOverlap) {
    throw new ApiError(
      httpStatus.CONFLICT,
      "You already have another appointment in this time slot"
    );
  }

  // Update appointment with new details
  existingAppointment.date = appointmentDateInUTC;
  existingAppointment.timeSlot = {
    start: timeSlotStartInUTC,
    end: timeSlotEndInUTC,
  };
  existingAppointment.status = "scheduled";
  existingAppointment.adviserStatus = "pending";

  await existingAppointment.save();

  // Emit event for appointment rescheduled
  eventEmitter.emit(emitterEventNames.APPOINTMENT_RESCHEDULED, {
    appointment: existingAppointment,
    user: req.user,
    timezone,
  });

  return res.status(httpStatus.OK).json({
    message: "Appointment rescheduled",
    data: existingAppointment,
  });
});

const cancelAppointmentAdviser = catchAsync(async (req, res) => {
  const { cancellationReason } = req.body;
  const { appointmentId } = req.params;
  const adviserId = req.user.id;
  if (!appointmentId) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Appointment ID is required");
  }

  const appointment = await appointmentService.getAppointmentDetails(
    appointmentId,
    [
      { path: "user", select: "name email" },
      { path: "adviser", select: "name email" },
    ]
  );
  if (!appointment) {
    throw new ApiError(httpStatus.NOT_FOUND, "Appointment not found");
  }
  if (appointment.status === "cancelled") {
    return res.status(httpStatus.OK).json({
      message: "Appointment already cancelled",
      data: appointment,
    });
  }

  if (appointment.adviser._id.toString() !== adviserId.toString()) {
    throw new ApiError(
      httpStatus.FORBIDDEN,
      "You are not authorized to cancel this appointment"
    );
  }

  // Only update if not already cancelled/rejected
  if (
    appointment.status !== "cancelled" ||
    appointment.adviserStatus !== "rejected"
  ) {
    appointment.status = "cancelled";
    appointment.adviserStatus = "rejected";
  }

  appointment.adviserCancellationReason = cancellationReason;

  let refundResponse = null;

  switch (appointment.mode) {
    case "free":
      await userService.increaseFreeSessions(appointment.user._id);
      break;

    case "subscription":
      try {
        const updatedSubscription =
          await customerSubscriptionService.increaseSessionsCount(
            appointment.user._id
          );

        // Emit only if session was incremented successfully
        if (updatedSubscription) {
          eventEmitter.emit(emitterEventNames.SUBSCRIPTION_SESSION_RETURNED, {
            appointment,
            userId: appointment.user._id.toString(),
            adviserId: adviserId.toString(),
            adviserName: req.user.name,
            userName: appointment.user.name,
          });
        }
      } catch (err) {
        console.warn(
          `⚠️ Session return skipped — no active subscription for user ${appointment.user._id}: ${err.message}`
        );
      }
      break;

    case "payment":
      if (appointment.paymentIntent && appointment.paymentStatus === "paid") {
        refundResponse = await stripeService.refundPayment(
          appointment.paymentIntent,
          appointment._id.toString()
        );

        if (!refundResponse.success) {
          if (refundResponse.code === "charge_already_refunded") {
            throw new ApiError(
              httpStatus.BAD_REQUEST,
              "This charge has already been refunded."
            );
          }
          throw new ApiError(
            httpStatus.BAD_REQUEST,
            refundResponse.message || "Refund failed. Please try again later."
          );
        }

        await refundService.createRefund({
          appointmentId: appointment._id,
          paymentIntentId: appointment.paymentIntent,
          refundId: refundResponse.refundId,
          amount: appointment.amount,
          reason: "Adviser cancelled the appointment",
        });

        // ✅ Emit refund notification
        // eventEmitter.emit(emitterEventNames.REFUND_INITIATED, {
        //   amount: appointment.amount,
        //   bookingId: appointment.bookingId,
        //   userId: appointment.user?._id || appointment.user,
        // });
      }
      break;
  }

  // ✅ Save updates to appointment
  const updatedAppointment = await appointmentService.updateAppointment(
    appointmentId,
    appointment,
    { new: true }
  );

  // ✅ Emit cancellation event
  eventEmitter.emit(emitterEventNames.APPOINTMENT_CANCELLED, {
    appointment,
    targetUserId: appointment.user._id.toString(),
    name: req.user.name,

    triggeredBy: "adviser",
  });

  // ✅ Final response
  return res.status(httpStatus.OK).json({
    message: "Appointment cancelled by adviser",
    refund: refundResponse
      ? refundResponse.refundId
      : "No refund (free/subscription/unpaid)",
    data: updatedAppointment,
  });
});

const acceptAppointment = catchAsync(async (req, res) => {
  const { id } = req.params;

  const appointment = await appointmentService.getAppointmentDetails(id, [
    { path: "user", select: "name email" },
    { path: "adviser", select: "name email" },
  ]);

  if (!appointment) {
    throw new ApiError(httpStatus.NOT_FOUND, "Appointment not found");
  }

  if (appointment.adviser._id.toString() !== req.user._id.toString()) {
    throw new ApiError(
      httpStatus.FORBIDDEN,
      "You do not have permission to update this appointment"
    );
  }

  if (appointment.adviserStatus === "accepted") {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      "Appointment has already been accepted"
    );
  }
  if (appointment.adviserStatus === "rejected") {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      "You can't accept a rejected booking"
    );
  }
  if (appointment.status === "cancelled") {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      "You can't accept a cancelled booking"
    );
  }

  if (
    appointment.paymentStatus !== "paid" &&
    appointment.paymentStatus !== "free"
  ) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Payment is not completed");
  }
  //can check payment status to see if payment is pending or failed

  const now = new Date();
  const startTime = new Date(appointment.timeSlot.start);
  const diffInMinutes = (startTime - now) / (1000 * 60);

  if (diffInMinutes < 15) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      "You can only accept an appointment at least 15 minutes before the scheduled start time"
    );
  }
  let updateData;
  updateData = {
    adviserStatus: "accepted",
    status: "scheduled",
    adviserCancellationReason: "",
  };

  const updatedAppointment = await appointmentService.updateAppointment(
    id,
    updateData
  );

  // Trigger notification for appointment acceptance
  eventEmitter.emit(emitterEventNames.APPOINTMENT_ACCEPTED, {
    appointment,
    targetUserId: appointment.user._id.toString(),
    adviserName: appointment.adviser.name,
  });

  return res.status(httpStatus.OK).json({
    message: "Appointment accepted",
    data: updatedAppointment,
  });
});

const getAllAppointments = catchAsync(async (req, res) => {
  const userId = req.user._id;
  const timezone = req?.headers?.timezone || appDefaults.TIMEZONE;
  const userType = req.user.__t;

  let { options, filters } = getPaginateConfig(req.query);
  const search = req.query.search?.trim();
  let appointments;

  // Apply user-type-based filtering
  filters =
    userType === userTypes.ADVISER
      ? { ...filters, adviser: userId }
      : userType === userTypes.USER
        ? { ...filters, user: userId }
        : filters;

  if (search) {
  }
  appointments = await appointmentService.getAllAppointments(
    filters,
    options,
    req.user
  );

  if (!appointments?.results?.length || appointments.length === 0) {
    return res.status(httpStatus.OK).json({
      message: "No appointments found",
      data: {
        results: [],
        page: options.page,
        limit: options.limit,
        totalPages: appointments?.totalPages || 0,
        totalResults: appointments?.totalResults || 0,
      },
    });
  }

  // Convert to user's timezone if appointments exist
  appointments.results = appointments.results.map((appointment) => {
    const appointmentStart = moment
      .utc(appointment.timeSlot.start)
      .tz(timezone);
    const appointmentEnd = moment.utc(appointment.timeSlot.end).tz(timezone);

    return {
      ...appointment,
      timeSlot: {
        start: appointmentStart.format("HH:mm A"),
        end: appointmentEnd.format("HH:mm A"),
      },
      date: appointmentStart.format("YYYY-MM-DD"),
    };
  });

  return res.status(httpStatus.OK).json({
    message: "Appointments retrieved successfully",
    data: {
      results: appointments.results,
      page: appointments.page,
      limit: appointments.limit,
      totalPages: appointments.totalPages,
      totalResults: appointments.totalResults,
      timezone,
    },
  });
});

const getTodaysAppointments = catchAsync(async (req, res) => {
  const nowUtcStart = moment.utc().startOf("day").toDate(); // Start of today in UTC
  const nowUtcEnd = moment.utc().endOf("day").toDate(); // End of today in UTC
  let { options, filters } = getPaginateConfig(req.query);
  filters = {
    ...filters,
    adviserStatus: "accepted",
    status: "scheduled",
    "timeSlot.start": { $gte: nowUtcStart },
    "timeSlot.end": { $lte: nowUtcEnd },
  };

  const appointments = await appointmentService.getAllAppointments(
    filters,
    options
  );

  if (appointments.results.length === 0 || !appointments.results) {
    return res.status(httpStatus.OK).json({
      message: "No appointments found",
      data: {},
    });
  }
  return res.status(httpStatus.OK).json({
    message: "Appointments retrieved successfully",
    data: { appointments },
  });
});

const markAppointmentComplete = catchAsync(async (req, res) => {
  const { id } = req.params;

  const appointment = await appointmentService.getAppointmentDetails(id, [
    { path: "adviser", select: "name profilePic" },
  ]);

  if (!appointment) {
    throw new ApiError(httpStatus.NOT_FOUND, "Appointment not found.");
  }

  const userId = req.user._id.toString();
  const adviserId = appointment.adviser._id.toString();
  const appointmentUserId = appointment.user?.toString();

  if (appointment.status === "cancelled") {
    return res.status(httpStatus.OK).json({
      message: "Appointment cancelled",
      data: {
        appointment,
      },
    });
  }

  if (appointment.status === "completed") {
    return res.status(httpStatus.OK).json({
      message: "Appointment completed already",
      data: {
        appointment,
      },
    });
  }

  // ✅ Authorization check
  if (
    !appointment.user ||
    (adviserId !== userId && appointmentUserId !== userId)
  ) {
    throw new ApiError(
      httpStatus.UNAUTHORIZED,
      "Unauthorized access to appointment."
    );
  }

  // ✅ Eligibility check
  if (
    appointment.status !== "scheduled" ||
    appointment.adviserStatus !== "accepted"
  ) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      "Appointment is not eligible to complete."
    );
  }

  // ✅ Mark appointment as completed
  appointment.status = "completed";
  await appointment.save();

  // ✅ Trigger payout only if payment mode, status is paid and paymentIntent exists
  const shouldTriggerPayout =
    (appointment.mode === "payment" &&
      appointment.paymentStatus === "paid" &&
      appointment.paymentIntent) ||
    (appointment.mode === "subscription" &&
      appointment.paymentStatus === "paid");

  if (shouldTriggerPayout) {
    const existingPayout = await payoutService.getOnePayout({
      appointmentId: appointment._id,
    });

    if (!existingPayout) {
      eventEmitter.emit(emitterEventNames.ADVISER_PAYOUT, appointment);
    }
  }

  const deleteWhiteBoard = await WhiteBoard.deleteMany({
    appointmentId: appointment._id,
  });

  // If user didn't join the appointment, trigger a notification
  if (!appointment.hasUserJoined) {
    // console.log(`🔔 User did not join appointment ${appointment._id}`);

    // Emit event for user missed appointment
    eventEmitter.emit(emitterEventNames.USER_MISSED_APPOINTMENT, {
      appointment,
      userId: appointment.user.toString(),
      adviserId: appointment.adviser._id.toString(),
      adviserName: appointment.adviser.name,
    });
  }

  return res.status(httpStatus.OK).json({
    message:
      "Appointment marked as completed and payout process initiated if applicable.",
    data: {
      appointment,
    },
  });
});

const getAllAppointmentUser = catchAsync(async (req, res) => {
  const timezone = req?.headers?.timezone || appDefaults.TIMEZONE; // Default to IST
  const userId = req.params?.id;

  let { options, filters } = getPaginateConfig(
    { ...req.query, sortBy: "date" },
    userId
  );

  // Determine filter based on user type
  filters = { ...filters, user: new mongoose.Types.ObjectId(userId) };

  // Fetch all appointments
  const appointments = await appointmentService.getAllAppointments(
    filters,
    options
  );

  if (!appointments.results || appointments.results.length === 0) {
    return res.status(httpStatus.OK).json({
      message: "No appointments found",
      data: {
        results: [],
        page: options.page,
        limit: options.limit,
        totalPages: appointments.totalPages || 0,
        totalResults: appointments.totalResults || 0,
      },
    });
  }

  // Convert appointment date and time to the user's timezone
  appointments.results.forEach((appointment) => {
    const appointmentStart = moment
      .utc(appointment.timeSlot.start)
      .tz(timezone);
    const appointmentEnd = moment.utc(appointment.timeSlot.end).tz(timezone);

    appointment.timeSlot.start = appointmentStart.format("HH:mm A");
    appointment.timeSlot.end = appointmentEnd.format("HH:mm A");
    appointment.date = appointmentStart.format("YYYY-MM-DD"); // Adding date field
  });

  return res.status(httpStatus.OK).json({
    message: "Appointments retrieved successfully",
    data: {
      results: appointments.results,
      page: appointments.page,
      limit: appointments.limit,
      totalPages: appointments.totalPages,
      totalResults: appointments.totalResults,
      timezone,
    },
  });
});

const getAllAppointmentAdviser = catchAsync(async (req, res) => {
  const timezone = req?.headers?.timezone || appDefaults.TIMEZONE; // Default to IST
  const userId = req.params?.id;

  let { options, filters } = getPaginateConfig(
    { ...req.query, sortBy: "date" },
    userId
  );

  // Determine filter based on user type
  filters = { ...filters, adviser: new mongoose.Types.ObjectId(userId) };

  // Fetch all appointments
  const appointments = await appointmentService.getAllAppointments(
    filters,
    options
  );

  if (!appointments.results || appointments.results.length === 0) {
    return res.status(httpStatus.OK).json({
      message: "No appointments found",
      data: {
        results: [],
        page: options.page,
        limit: options.limit,
        totalPages: appointments.totalPages || 0,
        totalResults: appointments.totalResults || 0,
      },
    });
  }

  // Convert appointment date and time to the user's timezone
  appointments.results.forEach((appointment) => {
    const appointmentStart = moment
      .utc(appointment.timeSlot.start)
      .tz(timezone);
    const appointmentEnd = moment.utc(appointment.timeSlot.end).tz(timezone);

    appointment.timeSlot.start = appointmentStart.format("HH:mm A");
    appointment.timeSlot.end = appointmentEnd.format("HH:mm A");
    appointment.date = appointmentStart.format("YYYY-MM-DD"); // Adding date field
  });

  return res.status(httpStatus.OK).json({
    message: "Appointments retrieved successfully",
    data: {
      results: appointments.results,
      page: appointments.page,
      limit: appointments.limit,
      totalPages: appointments.totalPages,
      totalResults: appointments.totalResults,
      timezone,
    },
  });
});

const addNotes = catchAsync(async (req, res) => {
  const { id } = req.params;
  const userId = req.user._id;
  const note = req.body.note;

  const appointment = await appointmentService.findAppointment({
    adviser: userId,
    _id: id,
  });
  if (!appointment) {
    throw new ApiError(httpStatus.NOT_FOUND, "appointment not found");
  }

  const updatedAppointment = await appointmentService.updateAppointment(id, {
    note: note,
  });

  if (!updatedAppointment) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      "Appointment could not be updated"
    );
  }

  return res.status(httpStatus.OK).json({
    message: "Appointment updated",
    data: updatedAppointment,
  });
});

const toggleAppointmentReminder = catchAsync(async (req, res) => {
  const { id } = req.params;
  const userId = req.user._id;

  const appointment = await appointmentService.findOneAppointment({
    _id: id,
  });

  if (!appointment) {
    throw new ApiError(httpStatus.NOT_FOUND, "appointment not found");
  }

  if (appointment.user._id.toString() !== userId.toString()) {
    throw new ApiError(
      httpStatus.FORBIDDEN,
      "You do not have permission to update this appointment"
    );
  }

  const updatedAppointment = await appointmentService.updateAppointment(id, {
    isReminderSet: !appointment.isReminderSet,
  });

  if (!updatedAppointment) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      "Appointment could not be updated"
    );
  }

  return res.status(httpStatus.OK).json({
    message: "Appointment updated",
    data: updatedAppointment,
  });
});

const addWhiteboardScreenshot = catchAsync(async (req, res) => {
  const { appointmentId } = req.params;

  if (!appointmentId) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Appointment ID is required");
  }

  if (!req.file) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      "Whiteboard screenshot image is required"
    );
  }

  // Find the appointment
  const appointment = await appointmentService.findOneAppointment({
    _id: appointmentId,
  });

  if (!appointment) {
    throw new ApiError(httpStatus.NOT_FOUND, "Appointment not found");
  }

  // Check if the user is authorized to add a whiteboard screenshot

  const userId = req.user._id.toString();
  const isAdviser = req.user.__t === userTypes.ADVISER;
  const isUser = req.user.__t === userTypes.USER;

  if (
    (isAdviser && appointment.adviser._id.toString() !== userId) ||
    (isUser && appointment.user._id.toString() !== userId)
  ) {
    throw new ApiError(
      httpStatus.FORBIDDEN,
      "You are not authorized to add a whiteboard screenshot to this appointment"
    );
  }

  // Upload the whiteboard screenshot to S3
  let uploadedImage;
  try {
    [uploadedImage] = await s3Upload([req.file], "whiteboard_screenshots");
  } catch (error) {
    throw new ApiError(
      httpStatus.INTERNAL_SERVER_ERROR,
      `Failed to upload whiteboard screenshot: ${error.message}`
    );
  }

  // Update the appointment with the whiteboard screenshot key and URL
  const updatedAppointment = await appointmentService.updateAppointment(
    appointmentId,
    {
      whiteBoardImage: {
        key: uploadedImage.key,
        url: uploadedImage.url,
      },
    }
  );

  if (!updatedAppointment) {
    throw new ApiError(
      httpStatus.INTERNAL_SERVER_ERROR,
      "Failed to update appointment with whiteboard screenshot"
    );
  }

  return res.status(httpStatus.OK).json({
    message: "Whiteboard screenshot added successfully",
    data: updatedAppointment,
  });
});

const getAppointmentPayoutStatus = catchAsync(async (req, res) => {
  const { appointmentId } = req.params;

  if (!appointmentId) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Appointment ID is required");
  }

  // Check if the appointment exists
  const appointment = await appointmentService.findOneAppointment({
    _id: appointmentId,
  });

  if (!appointment) {
    throw new ApiError(httpStatus.NOT_FOUND, "Appointment not found");
  }

  // Check if the user is authorized to view this appointment's payout status
  const userId = req.user._id.toString();
  const isAdviser = req.user.__t === userTypes.ADVISER;
  const isUser = req.user.__t === userTypes.USER;
  const isAdmin = req.user.__t === userTypes.ADMIN;

  if (
    !isAdmin &&
    ((isAdviser && appointment.adviser.toString() !== userId) ||
      (isUser && appointment.user.toString() !== userId))
  ) {
    throw new ApiError(
      httpStatus.FORBIDDEN,
      "You are not authorized to view this appointment's payout status"
    );
  }

  // Get the payout record for this appointment
  const payout = await payoutService.getOnePayout({ appointmentId });

  // Prepare the response object
  // const responseData = {
  //   appointmentId,
  //   appointment: {
  //     status: appointment.status,
  //     paymentStatus: appointment.paymentStatus,
  //     mode: appointment.mode,
  //   },
  // };

  // // If no payout record exists
  // if (!payout) {
  //   return res.status(httpStatus.OK).json({
  //     message: "No payout record found for this appointment",
  //     data: {
  //       ...responseData,
  //       payoutStatus: "not_initiated",
  //       stripeStatus: null,
  //     },
  //   });
  // }

  // // Add payout information to the response
  // responseData.payoutId = payout._id;
  // responseData.payoutStatus = payout.status;
  // responseData.amount = payout.amount;
  // responseData.stripeTransferId = payout.stripeTransferId;
  // responseData.createdAt = payout.createdAt;
  // responseData.updatedAt = payout.updatedAt;

  // If there's a Stripe transfer ID, get the status directly from Stripe
  let stripeTransferDetails = null;
  if (payout.stripeTransferId) {
    try {
      const stripeTransferResult = await stripeService.retrieveTransfer(
        payout.stripeTransferId
      );
      if (stripeTransferResult.status) {
        stripeTransferDetails = {
          id: stripeTransferResult.data.id,
          status: stripeTransferResult.data.status,
          amount: stripeTransferResult.data.amount / 100, // Convert from cents
          created: new Date(stripeTransferResult.data.created * 1000), // Convert from Unix timestamp
          destination: stripeTransferResult.data.destination,
          destinationPayment: stripeTransferResult.data.destination_payment,
          sourceType: stripeTransferResult.data.source_type,
          transferGroup: stripeTransferResult.data.transfer_group,
          balanceTransaction: stripeTransferResult.data.balance_transaction,
        };
      }
    } catch (error) {
      console.error(`Error fetching Stripe transfer details: ${error.message}`);
      // Continue without Stripe details if there's an error
    }
  }

  return res.status(httpStatus.OK).json({
    message: "Payout status retrieved successfully",
    data: {
      // ...responseData,
      stripeStatus: stripeTransferDetails,
    },
  });
});

module.exports = {
  createAppointment,
  updateAppointment,
  cancelAppointmentUser,
  getAppointmentDetails,
  getAllAppointments,
  rescheduleAppointment,
  cancelAppointmentAdviser,
  acceptAppointment,
  getTodaysAppointments,
  markAppointmentComplete,
  getAllAppointmentUser,
  getAllAppointmentAdviser,
  addNotes,
  toggleAppointmentReminder,
  addWhiteboardScreenshot,
  getAppointmentPayoutStatus,
};
