const mongoose = require("mongoose");

const chatRoomSchema = new mongoose.Schema(
  {
    participants: {
      type: [{ type: mongoose.Schema.Types.ObjectId, ref: "Users" }],
    },
    lastMessage: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Message",
    }, // Last message in the chats
    unreadMessageCount: {
      type: Map,
      of: Number,
      default: {},
    },
  },
  { timestamps: true }
);

const ChatRoom = mongoose.model("ChatRoom", chatRoomSchema);

module.exports = { ChatRoom };
