const admin = require("firebase-admin");
const config = require("../config/config");

const serviceAccount = JSON.parse(config.firebase_secret);
serviceAccount.private_key = serviceAccount.private_key.replace(/\\n/g, "\n");

const appName = "moneyowl";

if (!admin.apps.some((app) => app.name === appName)) {
  admin.initializeApp(
    {
      credential: admin.credential.cert(serviceAccount),
      databaseURL: "https://moneyowl-c4d05-default-rtdb.firebaseio.com/",
    },
    appName
  );
}

const firestoreDb = admin.app(appName).firestore();

firestoreDb.settings({
  ignoreUndefinedProperties: true,
});

const realtimeDb = admin.app(appName).database();

module.exports = { firestoreDb, realtimeDb, admin };
