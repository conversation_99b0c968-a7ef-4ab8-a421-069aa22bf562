const mongoose = require("mongoose");
const { paginate } = require("./plugins/paginate");

const customersubscriptionSchema = new mongoose.Schema(
  {
    user: {
      type: mongoose.SchemaTypes.ObjectId,
      ref: "User",
      required: true,
    },
    subscriptionProduct: {
      type: mongoose.SchemaTypes.ObjectId,
      ref: "SubscriptionProduct",
      required: true,
    },
    stripeSubscriptionId: { type: String, required: true, unique: true },
    startDate: { type: Date, required: true },
    endDate: { type: Date, required: true },
    isActive: { type: Boolean, default: true },
    remainingSessions: {
      type: Number,
      required: true,
      min: [0, "Sessions cannot be less than 0"],
      max: [1, "You cannot have more than 1 session remaining."],
    },
    paymentIntent: { type: String },
    paymentStatus: { type: String },
    amount: { type: Number },
    status: { type: String },
    renewed: { type: Boolean, default: false },
  },
  { timestamps: true }
);
customersubscriptionSchema.plugin(paginate);
customersubscriptionSchema.index(
  { user: 1, stripeSubscriptionId: 1 },
  { unique: true }
);
const CustomerSubscription = mongoose.model(
  "CustomerSubscription",
  customersubscriptionSchema
);
module.exports = { CustomerSubscription };
