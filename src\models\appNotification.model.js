const mongoose = require("mongoose");

const { paginate } = require("./plugins/paginate");
const { userTypes, notificationCategories } = require("../constants");

const appNotificationSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      required: true,
    },
    description: {
      type: String,
      trim: true,
    },
    scheduledAt: {
      type: Date,
      required: true,
      default: new Date(),
    },
    data: {
      type: Object,
      default: null,
    },
    targetRole: {
      type: String,
      default: null,
      enum: [...Object.values(userTypes), null],
    },
    targetUser: {
      type: mongoose.SchemaTypes.ObjectId,
      ref: "User",
      default: null,
    },
    isCreatedByAdmin: {
      type: Boolean,
      default: false,
    },
    isRead: {
      type: Boolean,
      default: false,
    },
    readBy: [
      {
        type: mongoose.SchemaTypes.ObjectId,
        ref: "User",
      },
    ],
    type: {
      type: String,
      required: true,
      enum: [...Object.values(notificationCategories), null],
    },
    image: {
      key: String,
      url: String,
    },
    isDeletedByAdmin: {
      type: Boolean,
      default: false,
    },
  },
  { timestamps: true }
);

appNotificationSchema.plugin(paginate);
appNotificationSchema.index({ targetUser: 1, targetRole: 1 });
const AppNotification = mongoose.model(
  "AppNotification",
  appNotificationSchema
);

module.exports = { AppNotification };
