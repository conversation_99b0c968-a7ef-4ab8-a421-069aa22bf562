const express = require("express");

const validate = require("../../middlewares/validate");
const { firebaseAuth } = require("../../middlewares/firebaseAuth");
const { adviser<PERSON>ontroller, stripeController } = require("../../controllers");

const { adviserValidation } = require("../../validations");

const { fileUploadService } = require("../../microservices");

const router = express.Router();
//no validations done

router.patch(
  "/update-profile/:id?",
  fileUploadService.multerUpload.single("profilePic"),
  firebase<PERSON><PERSON>("Adviser , Admin"),
  adviserController.updateProfile
);

//filter - dateAvailable-yyyy-mm-dd , specialization , experience , averageRating
router.get(
  "/getAll",
  firebaseAuth("User , Admin"),
  validate(adviserValidation.getAdviserOptions),
  adviserController.getAllAdvisers
);

router.get("/details/:id?", firebase<PERSON><PERSON>("All"), adviserController.getAdviser);

router.get(
  "/avaliability/:id?",
  firebase<PERSON>uth("Adviser,Admin"),
  adviserController.getAdviserAvaliability
);

router.patch(
  "/update-avaliability",
  firebaseAuth("Adviser"),
  adviserController.updateAvailability
);

router.get(
  "/get-reviews/:id?", //?rating=
  firebaseAuth("All"),
  validate(adviserValidation.getOptions),
  adviserController.getReviews
);

router.post(
  "/upload-docs",
  fileUploadService.multerUpload.array("documents", 1),
  firebaseAuth("Adviser"),
  adviserController.uploadDocuments
);

router.get(
  "/get-docs/:id?",
  firebaseAuth("Adviser,Admin"),
  adviserController.getDocuments
);

router.delete(
  "/delete-docs/:id",
  firebaseAuth("Adviser"),
  adviserController.deleteDocuments
);

router.get(
  "/notifications",
  firebaseAuth("Adviser"),
  adviserController.getAllNotifications
);

router.get(
  "/adviser-leaderboard",
  firebaseAuth("All"),
  adviserController.getLeaderBoard
);

//make it get
router.post(
  "/avaliable/",
  firebaseAuth("All"),
  adviserController.getAvailableAdvisers
);

router.delete(
  "/delete/:id?",
  firebaseAuth("Adviser,Admin"),
  adviserController.deleteAdviser
);
router.patch(
  "/activate/:id?",
  firebaseAuth("Adviser,Admin"),
  adviserController.activeAdviser
);

// Get weekly target stats for an adviser
router.get(
  "/weekly-target-stats/:adviserId?",
  firebaseAuth("Adviser,Admin"),
  adviserController.getWeeklyTargetStats
);

router.get(
  "/get/commission",
  // firebaseAuth("Adviser"),
  adviserController.getCommissionPercentage
);

module.exports = router;
