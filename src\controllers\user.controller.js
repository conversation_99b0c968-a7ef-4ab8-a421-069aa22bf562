const httpStatus = require("http-status");
const {
  goalService,
  adviserService,
  appointmentService,
  userService,
  reviewService,
  appNotificationService,
  customerSubscriptionService,
  agendaService,
  singleSessionProductService,
} = require("../services/index");
const moment = require("moment");
const ApiError = require("../utils/ApiError");
const catchAsync = require("../utils/catchAsync");
const { getPaginateConfig } = require("../utils/queryPHandler");
const { pushGoal } = require("../services/user.service");
const { fileUploadService, notificationService } = require("../microservices");
const { Appointment, Goal, User, Users } = require("../models");
const {
  userTypes,
  appDefaults,
  notificationCategories,
  emitterEventNames,
} = require("../constants");
const { default: mongoose } = require("mongoose");
const eventEmitter = require("../events/eventEmitter");

const updateProfile = catchAsync(async (req, res) => {
  const userId = req.user.__t === userTypes.USER ? req.user._id : req.params.id;
  let updateData = req.body;

  if (req.file) {
    // Upload the new profile image
    const [profilePic] = await fileUploadService
      .s3Upload([req.file], "profile_picture")
      .catch((e) => {
        throw new ApiError(
          httpStatus.INTERNAL_SERVER_ERROR,
          `Failed to upload documents : ${e.message}`
        );
      });

    const currentUser = await userService.getUserById(userId);

    // Delete the old profile image from S3 if it exists
    if (currentUser && currentUser.profilePic && currentUser.profilePic.key) {
      const oldPicKey = currentUser.profilePic.key;
      eventEmitter.emit(emitterEventNames.PROFILE_PIC_CLEANUP, { oldPicKey });
      // console.log(`🔔 Profile picture cleanup event emitted for: ${oldPicKey}`);
    }

    updateData = { ...updateData, profilePic };
  }
  const user = await userService.updateUser(userId, updateData);
  if (!user) {
    throw new ApiError(httpStatus.NOT_FOUND, "User not found");
  }

  return res.status(httpStatus.OK).json({
    message: "Profile updated successfully.",
    data: user,
  });
});

const getUserdetails = catchAsync(async (req, res) => {
  let query = { _id: req.params.id || req.user._id, isDeleted: false };

  // If the user is an ADMIN, allow fetching other users by ID
  if (req.user.__t === userTypes.ADMIN) {
    if (!req.params.id) {
      throw new ApiError(httpStatus.BAD_REQUEST, "No user ID provided");
    }
    query = { _id: req.params.id }; // Admins can fetch any user, including deleted ones
  }

  // ✅ Pass query directly
  const user = await userService.getUserDetails(query);

  return res.status(httpStatus.OK).json({
    message: "User found",
    data: user,
  });
});

//added auto increment amount
const createGoal = catchAsync(async (req, res) => {
  const { name, targetAmount, targetDate, monthlyContribution } = req.body;
  const userId = req.user._id;
  const amount = monthlyContribution;

  // Ensure targetAmount is greater than monthlyContribution
  if (targetAmount <= monthlyContribution) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      "targetAmount can't be less than or equal to monthlyContribution"
    );
  }

  // Convert targetDate to a Date object
  const targetDateObj = new Date(targetDate);
  const currentDate = new Date();

  if (targetDateObj <= currentDate) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      "The target date must be in the future."
    );
  }

  // Calculate the actual months difference between currentDate and targetDate
  const monthDifference =
    (targetDateObj.getFullYear() - currentDate.getFullYear()) * 12 +
    (targetDateObj.getMonth() - currentDate.getMonth());

  if (monthDifference <= 0) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      "The target date must be at least one month in the future."
    );
  }

  // Calculate required months to reach the goal based on monthlyContribution
  const requiredMonths = Math.ceil(targetAmount / monthlyContribution);

  // Check if the targetDate provides enough months
  if (monthDifference < requiredMonths) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      `The target will take at least ${Math.floor(requiredMonths / 12)} years and ${
        requiredMonths % 12
      } months. Either increase the contribution or extend the target date.`
    );
  }

  // Calculate minimum required monthly contribution based on target and time
  const minRequiredMonthlyContribution = Math.ceil(
    targetAmount / monthDifference
  );

  // Throw error if current monthly contribution is insufficient
  if (monthlyContribution < minRequiredMonthlyContribution) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      `To reach your goal of ${targetAmount} by ${targetDateObj.toDateString()}, you need to contribute at least ${minRequiredMonthlyContribution} per month.`
    );
  }

  // Calculate completed percentage
  const completedPercentage = (amount / targetAmount) * 100;

  // Create goal
  const goal = await goalService.createGoal(
    {
      name,
      amount,
      targetAmount,
      targetDate,
      monthlyContribution,
      completedPercentage,
    },
    userId
  );

  if (!goal) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Failed to create goal");
  }

  // Update user with the goal
  const updatedUser = await pushGoal(userId, goal._id);
  if (!updatedUser) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      "Failed to update user with goal"
    );
  }

  // Schedule next contribution via Agenda
  agendaService.scheduleNextContribution(goal);

  // Return response
  return res.status(httpStatus.CREATED).json({
    message: "Goal created",
    data: goal,
    requiredMonths,
  });
});

const updateGoal = catchAsync(async (req, res) => {
  const { goalId } = req.params;
  const userId = req.user._id;
  const goalData = req.body;

  const goal = await goalService.getGoalById(goalId);
  if (!goal) {
    throw new ApiError(httpStatus.NOT_FOUND, "no goal with such id exists");
  }
  if (goal.userId.toString() !== userId.toString()) {
    throw new ApiError(
      httpStatus.UNAUTHORIZED,
      "You are not allowed to update this goal"
    );
  }

  const updatedGoal = await goalService.updateGoal(goalId, goalData);
  if (!updatedGoal) {
    throw new ApiError(httpStatus.BAD_REQUEST, "failed to update the goal");
  }
  return res
    .status(httpStatus.OK)
    .json({ status: "success", data: updatedGoal });
});

const deleteGoal = catchAsync(async (req, res) => {
  try {
    const { goalId } = req.params;
    const userId = req.user.id;

    const goal = await goalService.getGoalById(goalId);

    if (!goal) {
      throw new ApiError(httpStatus.NOT_FOUND, "Goal not found");
    }

    const user = await userService.getUserById(userId);

    if (
      !user ||
      !user.goals.includes(goalId) ||
      goal.userId.toString() !== userId.toString()
    ) {
      throw new ApiError(
        httpStatus.UNAUTHORIZED,
        "You are not authorized to delete this goal"
      );
    }

    try {
      await Promise.all([
        userService.updateUser(userId, { $pull: { goals: goalId } }),
        goalService.deleteGoal(goalId),
      ]);
    } catch (error) {
      throw new ApiError(
        httpStatus.BAD_REQUEST,
        `unable to delete the goal : ${error}`
      );
    }

    // Send a success response
    return res
      .status(httpStatus.OK)
      .json({ message: "Goal deleted successfully" });
  } catch (error) {
    throw error;
  }
});

const getAllGoals = catchAsync(async (req, res) => {
  const userId =
    req.user.__t === userTypes.USER ? req.user._id : req.params.userId;
  let filters = {};
  filters = { ...filters, userId: userId };
  const goals = await Goal.find({ userId: userId });

  if (!goals) {
    return res.status(httpStatus.OK).json({
      message: "Goals not found for user",
      data: [],
    });
  }

  return res.status(httpStatus.OK).json({
    message: "Goals retrieved successfully",
    data: goals,
  });
});

const getGoal = catchAsync(async (req, res) => {
  const userId = req.user._id;
  const { goalId } = req.params;
  const goal = await goalService.getGoalById(goalId, userId);

  return res.status(httpStatus.OK).json({
    message: "Goal retrieved successfully",
    data: goal,
  });
});

const addReview = catchAsync(async (req, res) => {
  const { adviserId } = req.params;
  const { comment, rating } = req.body;
  const userId = req.user._id;

  const completedAppointments = await appointmentService.findAppointment({
    user: userId,
    adviser: adviserId,
    status: "completed",
  });

  if (!completedAppointments || completedAppointments.length === 0) {
    throw new ApiError(
      httpStatus.UNAUTHORIZED,
      "You are not allowed to add a review currently"
    );
  }

  // Count existing reviews the user has given to this adviser
  const existingReviews = await reviewService.countReviews({
    user: userId,
    adviser: adviserId,
  });

  if (existingReviews >= completedAppointments.length) {
    throw new ApiError(
      httpStatus.FORBIDDEN,
      "You have already reviewed all your completed appointments with this adviser"
    );
  }

  const review = await reviewService.createReview({
    adviser: adviserId,
    user: userId,
    rating,
    comment,
  });

  if (!review) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Failed to create review");
  }

  const updatedAdviser = await adviserService.updateAdviser(
    adviserId,
    {
      $push: { reviews: review._id },
    },
    { new: true }
  );

  const averageRating = await adviserService.calculateAvgRating(
    updatedAdviser._id
  );

  await adviserService.updateAdviser(
    adviserId,
    { averageRating: averageRating || 0 },
    { new: true }
  );

  return res.status(httpStatus.OK).json({
    message: "Review added successfully",
    data: review,
  });
}); // adviser review

const getReviews = catchAsync(async (req, res) => {
  const id = req.params.id ? req.params.id : req.user._id;
  let { options, filters } = getPaginateConfig(req.query);

  filters = {
    ...filters,
    user: new mongoose.Types.ObjectId(id),
  };

  const user = await userService.getUserDetails({ _id: id });
  if (!user) {
    throw new ApiError(httpStatus.NOT_FOUND, "User not found");
  }

  const reviews = await reviewService.getAllReviews(filters, options);

  if (!reviews || reviews.results.length < 1) {
    return res.status(httpStatus.OK).json({
      message: "No reviews found for this User",
      data: reviews,
    });
  }

  // Return the reviews
  return res.status(httpStatus.OK).json({
    message: "Reviews retrieved successfully",
    data: reviews,
  });
});

const createFinancialHealth = catchAsync(async (req, res) => {
  const userId = req.user._id;

  const data = {
    ...req.body,
    userId: userId,
  };

  const checkIfExists = await userService.getFinancialData(userId);

  if (checkIfExists) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      "Financial health data already exists"
    );
  }

  const financialHealth = await userService.createFinancialHealth(data);

  if (!financialHealth) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      "failed to create financialhealth "
    );
  }
  const updatedUser = await userService.addFinancialHealthToUser(
    userId,
    financialHealth._id
  );
  if (!updatedUser) {
    throw new ApiError(httpStatus.BAD_REQUEST, "failed to update the user");
  }
  return res.status(httpStatus.OK).json({
    message: "financial health record created ",
    data: {
      financialHealth,
    },
  });
});

const getFinancialHealth = catchAsync(async (req, res) => {
  const userId = req.query?.id || req.user._id; // Get the logged-in user ID

  // Check if financial health data exists for the user
  const existingFinancialHealth = await userService.getFinancialData(userId);

  if (!existingFinancialHealth) {
    throw new ApiError(
      httpStatus.NOT_FOUND,
      "Financial health data not found for this user"
    );
  }

  return res.status(httpStatus.OK).json({
    message: "Financial Health Data Fetched",
    data: {
      financialHealth: existingFinancialHealth,
    },
  });
});

const updateFinancialHealth = catchAsync(async (req, res) => {
  const userId = req.user._id; // Get the logged-in user ID

  // Update the financial health data
  const updatedFinancialHealth = await userService.updateFinancialHealth(
    userId,
    req.body
  );

  if (!updatedFinancialHealth) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      "Failed to update financial health data"
    );
  }

  return res.status(httpStatus.OK).json({
    message: "Financial Health Updated",
    data: {
      financialHealth: updatedFinancialHealth,
    },
  });
});

const getPieChartData = catchAsync(async (req, res) => {
  const userId = req.user._id;

  const financialData = await userService.getFinancialData(userId);

  if (!financialData) {
    return res.status(httpStatus.OK).json({
      message: "Financial data not found for user",
      data: {},
    });
  }

  const {
    savings,
    investments,
    debt,
    monthlyExpenses: expenses,
    pensions,
    monthlySavings,
  } = financialData;

  // Calculate the total to determine proportions
  const total = savings + investments + debt + expenses + pensions;

  // Prepare data for the pie chart
  const chartData = [
    { name: "Savings", value: savings + monthlySavings },
    { name: "Investments", value: investments },
    { name: "Debt", value: debt },
    { name: "Expenses", value: expenses },
    { name: "Pensions", value: pensions },
  ].map((item) => ({
    ...item,
    percentage: ((item.value / total) * 100).toFixed(2),
  }));

  return res.status(httpStatus.OK).json({
    message: "Financial data for pie chart retrieved successfully",
    data: chartData,
  });
});

const getAllNotifications = catchAsync(async (req, res) => {
  const id = req.user._id;
  let { options, filters } = getPaginateConfig(req.query);

  if (req.query.isRead !== undefined) {
    filters.isRead = req.query.isRead === "true";
  }

  const notifications = await appNotificationService.getNotifications(
    filters,
    options,
    id,
    req.user.__t
  );

  if (!notifications.results || notifications.results.length === 0) {
    return res.status(httpStatus.OK).json({
      message: "No Notifications found",
      data: {},
    });
  }

  return res.status(httpStatus.OK).json({
    message: "Notifications fetched successfully",
    data: notifications,
  });
});

const toggleFavouriteAdviser = catchAsync(async (req, res) => {
  const id = req.user._id;
  const { adviserId } = req.params;

  if (!adviserId) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Adviser Id is required");
  }

  const updatedUser = await userService.toggleFavorite(id, adviserId);

  if (!updatedUser) {
    throw new ApiError(httpStatus.NOT_FOUND, "User not found");
  }

  const isFavorite = updatedUser.favorites.includes(adviserId);

  return res.status(httpStatus.OK).json({
    message: isFavorite
      ? "Adviser added to favourites"
      : "Adviser removed from favourites",
    data: updatedUser,
  });
});

const getAllFavourites = catchAsync(async (req, res) => {
  const id = req.user._id;

  const favorites = await userService.getFavorites(id);

  return res.status(httpStatus.OK).json({
    message: "Favorites fetched",
    data: favorites,
  });
});

const getAvailableSlots = catchAsync(async (req, res) => {
  const { date, duration } = req.body; // Duration in minutes
  const { adviserId } = req.params;
  const timezone = req?.headers?.timezone || appDefaults.TIMEZONE;

  // Fetch adviser details
  const adviser = await adviserService.getAdviserDetails({ _id: adviserId });
  if (!adviser) {
    throw new ApiError(httpStatus.NOT_FOUND, "Adviser not found");
  }

  const userDate = moment.tz(date, "YYYY-MM-DD", timezone);
  const requestedDay = userDate.format("dddd");

  const availableDay = adviser.availability.find(
    (day) => day.day === requestedDay
  );
  if (!availableDay || !availableDay.timeSlots.length) {
    return res.status(200).json({
      message: `No availability for ${requestedDay}`,
      availableSlots: [],
    });
  }

  const now = moment.tz(timezone);

  let availableSlots = [];

  for (const slot of availableDay.timeSlots) {
    // Combine the userDate's YYYY-MM-DD with slot UTC time parts to build full UTC datetime strings
    const baseDate = userDate.format("YYYY-MM-DD");
    const startDateTimeStr = `${baseDate}T${slot.start.replace("Z", "")}Z`;
    const endDateTimeStr = `${baseDate}T${slot.end.replace("Z", "")}Z`;

    // Parse as UTC moments
    const startUtc = moment.utc(startDateTimeStr);
    const endUtc = moment.utc(endDateTimeStr);

    // Convert to local timezone
    let startLocal = startUtc.clone().tz(timezone);
    let endLocal = endUtc.clone().tz(timezone);

    // Handle overnight availability crossing midnight
    if (endLocal.isBefore(startLocal)) {
      endLocal = endLocal.add(1, "day");
    }

    // Skip invalid slots
    if (!startLocal.isValid() || !endLocal.isValid()) {
      // console.warn(`Invalid slot times: start=${slot.start}, end=${slot.end}`);
      continue;
    }

    let slotStartTime = startLocal.clone();
    while (slotStartTime.isBefore(endLocal)) {
      const slotEndTime = slotStartTime.clone().add(duration, "minutes");
      if (slotEndTime.isAfter(endLocal)) break;

      const slotStartDateTime = moment.tz(
        `${userDate.format("YYYY-MM-DD")} ${slotStartTime.format("HH:mm")}`,
        "YYYY-MM-DD HH:mm",
        timezone
      );

      const minAllowedTime = now.clone().add(15, "minutes");

      const isToday = userDate.isSame(now, "day");
      const isFutureSlot = slotStartDateTime.isAfter(minAllowedTime);

      if (!isToday || isFutureSlot) {
        availableSlots.push({
          start: slotStartTime.format("HH:mm"),
          end: slotEndTime.format("HH:mm"),
          duration,
        });
      }
      slotStartTime = slotEndTime.clone();
    }
  }

  // Filter out overlapping appointments
  const appointments = await Appointment.find({
    adviser: adviserId,
    status: "scheduled",
    paymentStatus: { $in: ["paid", "processing", "free"] },
    "timeSlot.start": {
      $gte: moment(userDate).startOf("day").utc().toISOString(),
      $lt: moment(userDate).endOf("day").utc().toISOString(),
    },
  });

  availableSlots = availableSlots.filter((slot) => {
    const slotStartTime = moment.tz(
      `${userDate.format("YYYY-MM-DD")} ${slot.start}`,
      "YYYY-MM-DD HH:mm",
      timezone
    );
    const slotEndTime = moment.tz(
      `${userDate.format("YYYY-MM-DD")} ${slot.end}`,
      "YYYY-MM-DD HH:mm",
      timezone
    );

    return !appointments.some((appointment) => {
      const appointmentStartTime = moment
        .utc(appointment.timeSlot.start)
        .tz(timezone);
      const appointmentEndTime = moment
        .utc(appointment.timeSlot.end)
        .tz(timezone);

      return (
        slotStartTime.isBetween(
          appointmentStartTime,
          appointmentEndTime,
          null,
          "[)"
        ) ||
        slotEndTime.isBetween(
          appointmentStartTime,
          appointmentEndTime,
          null,
          "(]"
        ) ||
        (appointmentStartTime.isBetween(
          slotStartTime,
          slotEndTime,
          null,
          "[)"
        ) &&
          appointmentEndTime.isBetween(slotStartTime, slotEndTime, null, "(]"))
      );
    });
  });

  if (availableSlots.length === 0) {
    return res.status(httpStatus.NOT_FOUND).json({
      message: "No available slots remaining",
    });
  }

  return res.status(httpStatus.OK).json({
    message: `Slots available for ${requestedDay}`,
    date: userDate.format("YYYY-MM-DD"),
    day: requestedDay,
    availableSlots,
    timezone,
  });
});

const checkReferralCode = catchAsync(async (req, res) => {
  const referralCode = req.body.code;
  const referredBy = await User.findOne({
    referralCode: referralCode,
    isDeleted: false,
  });

  if (!referredBy) {
    return res
      .status(httpStatus.OK)
      .json({ valid: false, message: "Invalid referral code" });
  }
  return res
    .status(httpStatus.OK)
    .json({ valid: true, message: "Valid referral code" });
});

const callDeclined = catchAsync(async (req, res) => {
  const { appointmentId } = req.params;

  if (!appointmentId) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Appointment ID is required");
  }

  // Get the appointment details
  const appointment = await Appointment.findById(appointmentId)
    .populate("user", "name")
    .populate("adviser", "name");

  if (!appointment) {
    throw new ApiError(httpStatus.NOT_FOUND, "Appointment not found");
  }

  // Determine who declined the call and who should receive the notification
  let recipientId, senderName, recipientName;

  if (req.user.__t === userTypes.USER) {
    // User declined the call, notify the adviser
    recipientId = appointment.adviser._id.toString();
    senderName = appointment.user.name;
    recipientName = appointment.adviser.name;
  } else if (req.user.__t === userTypes.ADVISER) {
    // Adviser declined the call, notify the user
    recipientId = appointment.user._id.toString();
    senderName = appointment.adviser.name;
    recipientName = appointment.user.name;
  } else {
    throw new ApiError(
      httpStatus.FORBIDDEN,
      "Only users or advisers can decline calls"
    );
  }

  // Format the appointment date and time
  const appointmentDate = moment(appointment.date).format("MMMM Do, YYYY");
  const appointmentTime = moment(appointment.timeSlot.start).format("h:mm A");

  // Send notification to the recipient
  try {
    await notificationService.sendToTopic(
      recipientId,
      {
        title: "Call Declined",
        body: `${senderName} has declined the video call `,
        image: "",
      },
      {
        type: notificationCategories.VIDEOCALL,
        appointmentId: appointmentId,
      }
    );

    return res.status(httpStatus.OK).json({
      success: true,
      message: "Call declined notification sent successfully",
    });
  } catch (error) {
    throw new ApiError(
      httpStatus.INTERNAL_SERVER_ERROR,
      "Failed to send call declined notification"
    );
  }
});

const callEntered = catchAsync(async (req, res) => {
  const { appointmentId } = req.params;

  const appointment = await appointmentService.findOneAppointment({
    _id: appointmentId,
  });
  if (!appointment) {
    throw new ApiError(httpStatus.NOT_FOUND, "Appointment not found");
  }
  if (appointment.user._id.toString() !== req.user._id.toString()) {
    throw new ApiError(httpStatus.UNAUTHORIZED, "Unauthorized");
  }
  appointment.hasUserJoined = true;
  if (!appointment.userJoinTime) {
    appointment.userJoinTime = new Date();
  }
  await appointment.save();
  return res.status(httpStatus.OK).json({
    message: "Call entered successfully",
    data: appointment,
  });
});

const deleteUser = catchAsync(async (req, res) => {
  const userId = req.params.id || req.user._id;
  const deleteUser = await userService.deleteUser(userId);

  if (req.user.__t === userTypes.ADMIN) {
    const blockedUser = await userService.blockUser(
      { _id: userId },
      { isBlockedByAdmin: true }
    );

    const notificationData = {
      title: "Account Blocked",
      description: "Your account has been Blocked by the Admin.",
      type: notificationCategories.BLOCKED,
      targetUser: userId,
      image: "",
    };
    await appNotificationService.sendNotification(
      notificationData,
      userId.toString()
    );
  }
  if (!deleteUser) {
    throw new ApiError(httpStatus.NOT_FOUND, "User not found");
  }
  return res
    .status(httpStatus.OK)
    .json({ message: "User deleted successfully" });
});

const activeUserAdminONly = catchAsync(async (req, res) => {
  const userId = req.params.id;
  const updatedUser = await User.findOneAndUpdate(
    { _id: userId },
    {
      isBlockedByAdmin: false,
      isDeleted: false,
    },
    { new: true }
  );

  if (!updatedUser) {
    throw new ApiError(httpStatus.NOT_FOUND, "User not found or deleted");
  }

  return res
    .status(httpStatus.OK)
    .json({ message: "User Activated successfully" });
});

module.exports = {
  updateProfile,
  getUserdetails,
  createFinancialHealth,
  updateFinancialHealth,
  createGoal,
  updateGoal,
  deleteGoal,
  getAllGoals,
  getGoal,
  // addToFavourites,
  // removeFromFavourites,
  toggleFavouriteAdviser,
  getAllFavourites,
  getAvailableSlots, //not complete road block
  addReview,
  getPieChartData,
  getAllNotifications,
  getFinancialHealth,
  deleteUser,
  checkReferralCode,
  callDeclined,
  getReviews,
  callEntered,
  activeUserAdminONly,
};
