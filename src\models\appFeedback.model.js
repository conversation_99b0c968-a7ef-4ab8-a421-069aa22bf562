const mongoose = require("mongoose");
const { paginate } = require("./plugins/paginate");

const userFeedbackSchema = new mongoose.Schema(
  {
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    rating: {
      type: Number,
      required: true,
    },
    review: {
      type: String,
      default: null,
    },
  },
  { timestamps: true, versionKey: false }
);
userFeedbackSchema.plugin(paginate);
const UserAppFeedback = mongoose.model("UserFeedback", userFeedbackSchema);
module.exports = { UserAppFeedback };
