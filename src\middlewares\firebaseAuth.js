const admin = require("firebase-admin");
const httpStatus = require("http-status");
const ApiError = require("../utils/ApiError");
const config = require("../config/config");

// const serviceAccount = require("../../firebase-service-secret.json");
const { authService } = require("../services");
const serviceAccount = JSON.parse(config.firebase_secret);
serviceAccount.private_key = serviceAccount.private_key.replace(/\\n/g, "\n");

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
});

const firebaseAuth =
  (allowUserType = "All") =>
  async (req, res, next) => {
    try {
      // Get the token from the Authorization header
      const token = req.headers?.authorization?.split(" ")[1];

      if (!token) {
        throw new ApiError(httpStatus.UNAUTHORIZED, "Please Authenticate!");
      }

      // Verify the Firebase ID token
      const payload = await admin.auth().verifyIdToken(token, true);
      // Retrieve the user from the database by their Firebase UID
      const user = await authService.getUserByFirebaseUId(payload.uid);
      if (!user) {
        const allowedPaths = [
          "/register",
          "/register-adviser",
          "/register-admin",
        ];

        // Allow registration routes to proceed if the user doesn't exist
        if (allowedPaths.includes(req.path)) {
          // console.log("/register includes");
          req.newUser = payload;
          req.routeType = allowUserType; // Set user type for further processing
          return next();
        } else {
          throw new ApiError(
            httpStatus.NOT_FOUND,
            "User doesn't exist. Please create an account."
          );
        }
      }

      if (user.isBlockedByAdmin) {
        const allowedPaths = ["/activate-account"];
        if (allowedPaths.includes(req.path)) {
          // console.log("/activate-account includes");
          throw new ApiError(
            httpStatus.FORBIDDEN,
            "Account is Blocked by admin , Cant activate account"
          );
        }
      }

      if (user.isDeleted) {
        const allowedPaths = ["/activate-account"];
        if (allowedPaths.includes(req.path)) {
          // console.log("/activate-account includes");
          req.user = payload;
          req.routeType = allowUserType;
          return next();
        } else {
          throw new ApiError(httpStatus.FORBIDDEN, "account is deactivated");
        }
      }

      // Sanitize the allowed user types by trimming any spaces
      const allowedRoles = allowUserType.split(",").map((role) => role.trim());

      // Handle user role restrictions
      if (!allowedRoles.includes(user.__t) && allowUserType !== "All") {
        throw new ApiError(
          httpStatus.FORBIDDEN,
          "Sorry, but you can't access this."
        );
      }

      // Attach the user to the request object for subsequent middleware
      req.user = user;
      return next();
    } catch (err) {
      if (err.code === "auth/id-token-expired") {
        return next(
          new ApiError(httpStatus.UNAUTHORIZED, "Session has expired")
        );
      }

      if (err.code === "auth/user-not-found") {
        return next(
          new ApiError(
            httpStatus.UNAUTHORIZED,
            "Firebase user does not exist. Please create an account."
          )
        );
      }
      console.error("FirebaseAuthError:", err);
      return next(err);
    }
  };

module.exports = { firebaseAuth };
