const { Revenue } = require("../models/index");
const moment = require("moment");
const createRevenue = async (revenueData) => {
  return await Revenue.create(revenueData);
};

const getRevenueById = async (revenueId) => {
  return await Revenue.findById(revenueId);
};

const getRevenue = async (filter) => {
  return await Revenue.findOne(filter);
};

const getRevenueByAdviser = async (adviserId) => {
  return await Revenue.find({ adviserId }).sort({ createdAt: -1 });
};

const getTotalRevenue = async (startDate, endDate) => {
  const result = await Revenue.aggregate([
    {
      $match: {
        status: "collected",
        date: { $gte: new Date(startDate), $lte: new Date(endDate) }, // Filter by the date range
      },
    },
    {
      $group: {
        _id: null,
        totalRevenue: { $sum: "$totalAmount" }, // Sum totalAmount for the matched documents
      },
    },
  ]);

  return result.length ? result[0].totalRevenue : 0;
};

const getAllRevenueRecords = async () => {
  return await Revenue.find().sort({ createdAt: -1 });
};

const getPendingRevenue = async () => {
  return await Revenue.find({ status: "pending" }).sort({ createdAt: -1 });
};

const getCollectedRevenue = async () => {
  return await Revenue.find({ status: "collected" }).sort({ createdAt: -1 });
};

const updateRevenue = async (filter, updateData) => {
  return await Revenue.findOneAndUpdate(filter, updateData, { new: true });
};

const deleteRevenue = async (revenueId) => {
  return await Revenue.findByIdAndDelete(revenueId);
};

const getRevenueInRange = async (startDate, endDate) => {
  const revenueRecords = await Revenue.aggregate([
    {
      $match: {
        createdAt: { $gte: startDate, $lte: endDate },
        status: "collected",
      },
    },
    {
      $group: {
        _id: null,
        netTotal: { $sum: "$netAmount" }, // Net amount after Stripe fees
        grossTotal: { $sum: "$totalAmount" }, // Gross total amount
        stripeFees: { $sum: "$stripeFee" },
      },
    },
  ]);

  return revenueRecords[0] || { total: 0 };
};

async function getAllFees(filters = {}, options = {}, populate = true) {
  const query = { ...filters };

  if (filters.search) {
    query.$or = [
      { user: { $regex: filters.search, $options: "i" } },
      { adviser: { $regex: filters.search, $options: "i" } },
      { feeId: { $regex: filters.search, $options: "i" } },
    ];
    delete query.search;
  }
  query.sourceType = "appointment";
  if (populate && query.sourceType) {
    options.populate = [
      "appointmentId::adviser,user,duration", // This will populate the entire appointment
      "appointmentId.adviser::name",
      "appointmentId.user::name",
    ];
  }
  options.project = {
    _id: 1,
    feeId: 1,
    appointmentId: 1,
    status: 1,
    totalAmount: 1,
    commissionPercentage: 1,
    createdAt: 1,
    invoiceUrl: 1,
  };

  const revenue = await Revenue.paginate(query, options);

  return revenue;
}

// const revenueRecords = await Revenue.aggregate([
//   {
//     $match: {
//       status: "collected",
//     },
//   },
//   {
//     $project: {
//       commissionEarned: {
//         $multiply: [
//           "$totalAmount",
//           { $divide: ["$commissionPercentage", 100] },
//         ],
//       },
//     },
//   },
//   {
//     $group: {
//       _id: null,
//       totalCommission: { $sum: "$commissionEarned" },
//     },
//   },
// ]);

async function getMonthlySubscriptionRevenueWithComparison(
  year = moment().year()
) {
  const startOfYear = moment().year(year).startOf("year").toDate();
  const endOfYear = moment().year(year).endOf("year").toDate();

  const monthly = await Revenue.aggregate([
    {
      $match: {
        sourceType: "subscription",
        status: "collected",
        createdAt: {
          $gte: startOfYear,
          $lte: endOfYear,
        },
      },
    },
    {
      $group: {
        _id: { month: { $month: "$createdAt" } },
        total: { $sum: "$totalAmount" },
      },
    },
    {
      $sort: { "_id.month": 1 },
    },
  ]);

  // console.log("📊 Aggregated Monthly Subscription Revenue:", monthly);

  const monthlyData = Array(12).fill(0);
  monthly.forEach((entry) => {
    const { month } = entry._id;
    monthlyData[month - 1] = entry.total;
  });

  const currentMonthIndex = moment().month(); // 0-indexed
  const lastMonthIndex = currentMonthIndex - 1;

  const currentValue = monthlyData[currentMonthIndex] || 0;
  const lastValue = lastMonthIndex >= 0 ? monthlyData[lastMonthIndex] || 0 : 0;
  const difference = currentValue - lastValue;

  const percentageChange =
    lastValue === 0
      ? currentValue > 0
        ? 100
        : 0
      : parseFloat(((difference / lastValue) * 100).toFixed(2));

  const direction = difference >= 0 ? "growth" : "loss";

  const comparison = {
    currentMonth: moment().format("MMMM"),
    lastMonth:
      lastMonthIndex >= 0
        ? moment().subtract(1, "months").format("MMMM")
        : null,
    currentValue,
    lastValue,
    difference,
    percentageChange,
    direction,
  };

  return {
    year,
    monthlyRevenue: monthlyData,
    comparison,
  };
}

const getTotalNetRevenue = async (filters = {}) => {
  // Default to collected status if not specified
  const query = {
    status: "collected",
    ...filters,
  };

  const result = await Revenue.aggregate([
    { $match: query },
    {
      $group: {
        _id: null,
        totalNetRevenue: { $sum: "$netAmount" },
        totalGrossRevenue: { $sum: "$totalAmount" },
        totalStripeFees: { $sum: "$stripeFee" },
        count: { $sum: 1 },
      },
    },
  ]);

  if (result.length === 0) {
    return {
      totalNetRevenue: 0,
      totalGrossRevenue: 0,
      totalStripeFees: 0,
      count: 0,
    };
  }

  return {
    totalNetRevenue: parseFloat(result[0].totalNetRevenue.toFixed(2)),
    totalGrossRevenue: parseFloat(result[0].totalGrossRevenue.toFixed(2)),
    totalStripeFees: parseFloat(result[0].totalStripeFees.toFixed(2)),
    count: result[0].count,
  };
};

const getRevenueInRangeForYear = async (year) => {
  const start = moment().year(year).startOf("year").toDate();
  const end = moment().year(year).endOf("year").toDate();

  // Fetch revenue data between two dates with all relevant fields
  return Revenue.aggregate([
    { $match: { createdAt: { $gte: start, $lte: end }, status: "collected" } },
    {
      $group: {
        _id: { $month: "$createdAt" },
        netTotal: { $sum: "$netAmount" }, // Net amount after Stripe fees
        grossTotal: { $sum: "$totalAmount" }, // Gross total amount
        stripeFees: { $sum: "$stripeFee" }, // Stripe fees directly from the data
      },
    },
    { $sort: { _id: 1 } }, // Sort by month (1 = ascending)
  ]);
};

module.exports = {
  createRevenue,
  getRevenueById,
  getRevenueByAdviser,
  getTotalRevenue,
  getAllRevenueRecords,
  getPendingRevenue,
  getCollectedRevenue,
  updateRevenue,
  deleteRevenue,
  getRevenueInRange,
  getAllFees,
  getRevenue,
  getMonthlySubscriptionRevenueWithComparison,
  getTotalNetRevenue,
  getRevenueInRangeForYear,
};
