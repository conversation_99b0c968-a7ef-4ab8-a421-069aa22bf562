const express = require("express");
const { chatController } = require("../../controllers");
const router = express.Router();
const { firebaseAuth } = require("../../middlewares/firebaseAuth");
const { fileUploadService } = require("../../microservices");
const { chatValidation } = require("../../validations");
const validate = require("../../middlewares/validate");

router.post(
  "/rooms",
  firebaseAuth("Adviser,User"),
  validate(chatValidation.createChatRoomValidation),
  chatController.createChatRoom
);

router.post(
  "/send",
  firebaseAuth("Adviser,User"),
  fileUploadService.multerUpload.single("attachment"),
  validate(chatValidation.sendMessageValidation),
  chatController.sendMessage
);

router.get(
  "/rooms/:chatRoomId/messages",
  firebaseAuth("Adviser,User,Admin"),
  chatController.getMessages
);

router.get(
  "/allrooms",
  firebaseAuth("Adviser,User,Admin"),
  chatController.getChatRooms
);

router.post(
  "/room/exists",
  firebaseAuth("Adviser,User"),
  // validate(chatValidation.getOrCreateChatRoomValidation),
  chatController.getOrCreateChatRoomIdForOneToOne
);

router.get(
  "/users",
  firebaseAuth("Adviser,User"),
  chatController.getUserstoChat
);

router.get(
  "/room/get/:id", //appointment id
  firebaseAuth("Admin"),
  chatController.getAppointmentChatRoom
);
router.get(
  "/messages/get/:id", //chat room id
  firebaseAuth("Admin"),
  chatController.getMessagesForAdmin
);
module.exports = router;
