const { CustomerSubscription } = require("../models");
const moment = require("moment");

async function createCustomerSubscription(
  userId,
  subscriptionProductId,
  stripeSubscriptionId,
  startDate,
  endDate,
  paymentStatus,
  paymentIntent,
  isActive,
  remainingSessions,
  amount,
  status
) {
  const subscription = new CustomerSubscription({
    user: userId,
    subscriptionProduct: subscriptionProductId,
    stripeSubscriptionId,
    startDate,
    endDate,
    paymentStatus,
    paymentIntent,
    isActive,
    remainingSessions,
    amount,
    status,
  });
  const savedSubscription = await subscription.save();
  return savedSubscription;
}

async function cancelCustomerSubscription(stripeSubscriptionId) {
  const subscription = await CustomerSubscription.findOneAndUpdate(
    { stripeSubscriptionId },
    { isActive: false },
    { new: true }
  );
  return subscription;
}

async function getCustomerSubscription(filter) {
  const subscription = await CustomerSubscription.findOne(filter)
    .sort({ createdAt: -1 })
    .populate({ path: "user", select: "name email" })

    .populate({
      path: "subscriptionProduct",
      select: "name price description billingCycle",
    });
  return subscription;
}

async function getAllCustomerSubscription(filter) {
  const subscription = await CustomerSubscription.find(filter)
    .sort({ createdAt: -1 })
    .populate({ path: "user", select: "name email" })

    .populate({
      path: "subscriptionProduct",
      select: "name price description billingCycle",
    });
  return subscription;
}

async function getAllCustomerSubscriptions(filter, options = {}) {
  options.populate = ["user::name,email", "subscriptionProduct::*"];
  const subscriptions = await CustomerSubscription.paginate(filter, options);
  return subscriptions;
}

async function updateCustomerSubscription(id, updateData) {
  const subscription = await CustomerSubscription.findByIdAndUpdate(
    id,
    updateData,
    { new: true, runValidators: true }
  );
  return subscription;
}

async function reduceSessionsCount(userId) {
  const subscription = await CustomerSubscription.findOneAndUpdate(
    { user: userId, remainingSessions: { $gt: 0 } }, // Ensure sessions are greater than 0
    { $inc: { remainingSessions: -1 } }, // Decrement remainingSessions by 1
    { new: true, runValidators: true }
  );

  if (!subscription) {
    throw new Error("No active subscription found or sessions exhausted.");
  }

  return subscription;
}

async function increaseSessionsCount(userId) {
  // Fetch the latest active subscription for the user, with product populated
  const subscription = await CustomerSubscription.findOne({
    user: userId,
    isActive: true,
  })
    .sort({ createdAt: -1 })
    .populate("subscriptionProduct");

  if (!subscription) throw new Error("No active subscription found.");
  if (
    !subscription.subscriptionProduct ||
    typeof subscription.subscriptionProduct.maxSessions !== "number"
  ) {
    throw new Error("Subscription product or maxSessions not found.");
  }

  const current = subscription.remainingSessions || 0;
  const max = subscription.subscriptionProduct.maxSessions;

  if (current < max) {
    subscription.remainingSessions = current + 1;
    await subscription.save();
  }

  return subscription;
}

const getSubcriptionRevenueInRange = async (startDate, endDate) => {
  const subscriptionRevenue = await CustomerSubscription.aggregate([
    {
      $match: {
        createdAt: { $gte: startDate, $lte: endDate },
        paymentStatus: "succeeded",
      },
    },
    {
      $group: {
        _id: null,
        count: { $sum: 1 },
      },
    },
  ]);

  const count = subscriptionRevenue[0]?.count || 0;
  const total = count * 53.75;

  return { total };
};

async function getMonthlyRevenueWithComparison(year = moment().year()) {
  const monthly = await CustomerSubscription.aggregate([
    {
      $match: {
        paymentStatus: "succeeded",
        startDate: {
          $gte: moment().year(year).startOf("year").toDate(),
          $lte: moment().year(year).endOf("year").toDate(),
        },
      },
    },
    {
      $group: {
        _id: { month: { $month: "$startDate" } },
        total: { $sum: "$amount" },
      },
    },
    { $sort: { "_id.month": 1 } },
  ]);

  const monthlyData = Array(12).fill(0);
  monthly.forEach((entry) => {
    const { month } = entry._id;
    monthlyData[month - 1] = entry.total;
  });

  const currentMonthIndex = moment().month(); // 0-indexed
  const lastMonthIndex = currentMonthIndex - 1;

  const currentValue = monthlyData[currentMonthIndex] || 0;
  const lastValue = lastMonthIndex >= 0 ? monthlyData[lastMonthIndex] || 0 : 0;
  const difference = currentValue - lastValue;
  const percentageChange =
    lastValue === 0
      ? 100
      : parseFloat(((difference / lastValue) * 100).toFixed(2));
  const direction = difference >= 0 ? "growth" : "loss";

  const comparison = {
    currentMonth: moment().format("MMMM"),
    lastMonth:
      lastMonthIndex >= 0
        ? moment().subtract(1, "months").format("MMMM")
        : null,
    currentValue,
    lastValue,
    difference,
    percentageChange,
    direction,
  };

  return {
    year,
    monthlyRevenue: monthlyData,
    comparison,
  };
}

async function hasActiveSubscription(userId) {
  const subscription = await CustomerSubscription.findOne({
    user: userId,
    isActive: true,
    endDate: { $gte: new Date() },
  });

  return !!subscription; // Convert to boolean
}

async function getActiveSubscribersGroupedByState() {
  const results = await CustomerSubscription.aggregate([
    {
      $match: {
        isActive: true,
        endDate: { $gte: new Date() },
      },
    },
    {
      $lookup: {
        from: "users",
        localField: "user",
        foreignField: "_id",
        as: "userInfo",
      },
    },
    {
      $unwind: "$userInfo",
    },
    {
      $addFields: {
        stateForGroupBy: { $ifNull: ["$userInfo.address.state", "NA"] },
      },
    },
    {
      $group: {
        _id: "$stateForGroupBy",
        activeSubscribers: { $sum: 1 },
      },
    },
    {
      $sort: {
        activeSubscribers: -1,
      },
    },
  ]);
  return results;
}

module.exports = {
  createCustomerSubscription,
  cancelCustomerSubscription,
  getCustomerSubscription,
  getAllCustomerSubscriptions,
  updateCustomerSubscription,
  reduceSessionsCount,
  increaseSessionsCount,
  getSubcriptionRevenueInRange,
  getMonthlyRevenueWithComparison,
  hasActiveSubscription,
  getActiveSubscribersGroupedByState,
  getAllCustomerSubscription,
};
