const { emitterEventNames, notificationCategories } = require("../constants");

const { appNotificationService, adviserStripeService } = require("../services");
const {
  mailService,
  notificationService,
  fileUploadService,
} = require("../microservices");
const ejs = require("ejs");
const path = require("path");

const catchEventHandler = require("../utils/catchEventHandler");

const handleAdviserVerified = catchEventHandler(async (user) => {
  // Send in-app notification
  const adviserNotificationData = {
    title: "Your Account is verified ✅",
    description:
      "Congratulations Your account is now verified by the platform. You are now live on our application",
    type: notificationCategories.VERIFICATION,
    targetUser: user._id.toString(),
    image: "",
  };
  await appNotificationService.createAndSendNotification(
    adviserNotificationData,
    user._id.toString()
  );

  // Send email notification
  try {
    // Render email template
    const emailHtml = await ejs.renderFile(
      path.join(__dirname, "../views/emails/adviserVerification.ejs"),
      {
        name: user.name || "",
        email: user.email,
      }
    );

    // Send email
    await mailService.sendEmail({
      to: user.email,
      subject: "Congratulations! Your Money Owls Account is Verified",
      html: emailHtml,
    });
  } catch (error) {
    console.error(
      "❌ Error sending adviser verification email:",
      error.message
    );
  }
});

const handleAdviserDocumentRejected = catchEventHandler(async (data) => {
  // Extract data from the event payload
  const { user, rejectReason } = data;

  // Send in-app notification
  const adviserNotificationData = {
    title: "Document Verification Update",
    description:
      "Your document has been reviewed and could not be approved. Please check your email for details.",
    type: notificationCategories.GENERAL,
    targetUser: user._id.toString(),
    image: "",
  };
  // console.log("🔔 Document rejection notification triggered");
  await appNotificationService.createAndSendNotification(
    adviserNotificationData,
    user._id.toString()
  );

  // Send email notification
  try {
    // console.log("📧 Sending document rejection email to", user.email);

    // Render email template
    const emailHtml = await ejs.renderFile(
      path.join(__dirname, "../views/emails/documentRejection.ejs"),
      {
        name: user.name || "",
        email: user.email,
        documentType: "Verification Document",
        rejectReason:
          rejectReason ||
          "The document does not meet our verification requirements.",
      }
    );

    // Send email
    await mailService.sendEmail({
      to: user.email,
      subject: "Money Owls - Document Verification Update",
      html: emailHtml,
    });

    // console.log("✅ Document rejection email sent successfully to", user.email);
  } catch (error) {
    console.error("❌ Error sending document rejection email:", error.message);
  }
});

const handleStripeConnectedAccountVerified = catchEventHandler(
  async ({ stripeAccountId }) => {
    // console.log(`🔔 Stripe Connected Account Verified: ${stripeAccountId}`);

    // Fetch current record to check if onboarding is already completed
    const existingAccount = await adviserStripeService.getOne({
      stripeAccountId,
    });

    if (!existingAccount) {
      console.error(
        `❌ Could not find AdviserStripe record for stripeAccountId: ${stripeAccountId}`
      );
      return;
    }

    if (existingAccount.hasCompletedOnboarding) {
      // Already processed - skip duplicate notification
      // console.log(
      //   `⚠️ Onboarding already completed for ${stripeAccountId}, skipping`
      // );
      return;
    }

    // Update the hasCompletedOnboarding flag to true
    const updatedAccount = await adviserStripeService.update(
      { stripeAccountId },
      { hasCompletedOnboarding: true }
    );

    if (updatedAccount) {
      // console.log(
      //   `✅ Updated hasCompletedOnboarding flag for adviser: ${updatedAccount.adviser._id}`
      // );

      // Send notification to the adviser
      const adviserNotificationData = {
        title: "Your Stripe Account Verified ✅",
        description:
          "Your Stripe account has been verified. You can now start receiving payments 🎉",
        type: notificationCategories.VERIFICATION,
        targetUser: updatedAccount.adviser._id.toString(),
        image: "",
      };

      await appNotificationService.createAndSendNotification(
        adviserNotificationData,
        updatedAccount.adviser._id.toString()
      );
    }
  }
);

const handleDocumentCleanup = catchEventHandler(async (data) => {
  const { rejectedDocuments } = data;

  if (
    !rejectedDocuments ||
    !Array.isArray(rejectedDocuments) ||
    rejectedDocuments.length === 0
  ) {
    // console.log("No rejected documents to clean up");
    return;
  }

  // Process each rejected document asynchronously
  const deletePromises = rejectedDocuments.map(async (rejectedDoc) => {
    try {
      await fileUploadService.s3Delete(rejectedDoc.key);

      return true;
    } catch (error) {
      return false;
    }
  });

  // Wait for all deletions to complete
  const results = await Promise.all(deletePromises);
  const successCount = results.filter((result) => result).length;
  console.log(
    `🧹 Document cleanup completed: ${successCount}/${rejectedDocuments.length} documents deleted`
  );
});

/**
 * Handle profile picture cleanup event
 * This event is triggered when an adviser/user updates their profile picture
 * It deletes the old profile picture from S3
 */
const handleProfilePicCleanup = catchEventHandler(async (data) => {
  const { oldPicKey } = data;

  if (!oldPicKey) {
    return;
  }

  try {
    await fileUploadService.s3Delete(oldPicKey);
  } catch (error) {
    console.error(
      `❌ Failed to delete old profile picture from S3: ${error.message}`
    );
  }
});

module.exports = (emitter) => {
  emitter.on(emitterEventNames.ADVISER_VERIFIED, handleAdviserVerified);
  emitter.on(
    emitterEventNames.DOCUMENT_REJECTED,
    handleAdviserDocumentRejected
  );
  emitter.on(
    emitterEventNames.STRIPE_CONNECTED_ACCOUNT_VERIFIED,
    handleStripeConnectedAccountVerified
  );
  emitter.on(emitterEventNames.DOCUMENT_CLEANUP, handleDocumentCleanup);
  emitter.on(emitterEventNames.PROFILE_PIC_CLEANUP, handleProfilePicCleanup);
};
