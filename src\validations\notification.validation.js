const Joi = require("joi");
const { objectId, dbOptionsSchema } = require("./custom.validation");

const sendNotificationValidation = {
  body: Joi.object({
    targetUser: Joi.string().custom(objectId).allow("").messages({
      "string.base": "Target User must be a string.",
    }),
    targetRole: Joi.string()
      .valid("All", "User", "Adviser")
      .allow("")
      .messages({
        "string.base": "Target Role must be a string.",
        "any.only": "Target Role must be one of: All, User, or Adviser",
      }),
    title: Joi.string().required().trim().messages({
      "any.required": "Title is required.",
      "string.empty": "Title cannot be empty.",
    }),
    description: Joi.string().required().trim().messages({
      "any.required": "Description is required.",
      "string.empty": "Description cannot be empty.",
    }),
  })
    .custom((value, helpers) => {
      const hasValidTargetUser =
        value.targetUser && value.targetUser.length > 0;
      const hasValidTargetRole =
        value.targetRole &&
        ["All", "User", "Adviser"].includes(value.targetRole);

      if (!hasValidTargetUser && !hasValidTargetRole) {
        return helpers.error("custom.targetRequired");
      }

      return value;
    })
    .messages({
      "custom.targetRequired":
        "Either a valid targetUser or targetRole (All, User, or Adviser) must be provided",
    }),
};

const getNotificationValidation = {
  query: Joi.object({
    targetRole: Joi.string()
      .valid("All", "User", "Adviser")
      .allow("")
      .messages({
        "any.only": "Target Role must be one of: All, User, or Adviser",
        "string.base": "Target Role must be a string",
      }),
    type: Joi.string().valid("deleted"),
    ...dbOptionsSchema,
  }).messages({
    "object.unknown": "Invalid query parameter provided",
  }),
};

module.exports = { sendNotificationValidation, getNotificationValidation };
