<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Refund Initiated</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      color: #333;
      margin: 0;
      padding: 0;
      background-color: #f9f9f9;
    }

    .container {
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
      background-color: #ffffff;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .header {
      background-color: #ffffff;
      padding: 20px;
      text-align: center;
    }

    .logo {
      max-width: 150px;
      height: auto;
    }

    .title {
      color: #00AA9D;
      margin-top: 0;
      margin-bottom: 20px;
      text-align: center;
    }

    .info-box {
      background-color: #f0f7f7;
      border-left: 4px solid #00AA9D;
      padding: 15px;
      margin: 20px 0;
      border-radius: 4px;
    }

    .info-box p {
      margin: 5px 0;
    }

    .info-box strong {
      color: #00AA9D;
      display: inline-block;
      width: 150px;
    }

    .amount {
      font-size: 24px;
      font-weight: bold;
      color: #00AA9D;
      text-align: center;
      margin: 20px 0;
    }

    .steps {
      background-color: #f8f9fa;
      padding: 15px;
      border-radius: 4px;
      margin: 20px 0;
    }

    .steps ol {
      margin: 0;
      padding-left: 20px;
    }

    .steps li {
      margin-bottom: 10px;
    }

    .footer {
      margin-top: 30px;
      text-align: center;
      font-size: 12px;
      color: #666;
      border-top: 1px solid #e0e0e0;
      padding-top: 20px;
    }

    a {
      color: #00AA9D;
      text-decoration: none;
    }

    a:hover {
      text-decoration: underline;
    }

    @media only screen and (max-width: 600px) {
      .container {
        padding: 20px;
        margin: 0px;
      }

      .title {
        font-size: 22px;
      }

      .header,
      .footer {
        padding: 10px;
      }
    }
  </style>
</head>

<body>
  <div class="container">
    <div class="header">
      <img src="https://moneyowl.s3.eu-north-1.amazonaws.com/public/logos/final+logo+moneyowsl.png"
        alt="Money Owls Logo" class="logo">
    </div>

    <h2 class="title">Refund Initiated</h2>

    <p>Hello <%= name %>,</p>

    <p>We're writing to confirm that a refund has been initiated for your appointment.</p>

    <div class="amount">£<%= amount %>
    </div>

    <div class="info-box">
      <p><strong>Refund Details:</strong></p>
      <% if (typeof bookingId !=='undefined' && bookingId) { %>
        <p><strong>Booking ID:</strong>
          <%= bookingId %>
        </p>
        <% } %>
          <% if (typeof amount !=='undefined' && amount) { %>
            <p><strong>Amount:</strong> £<%= amount %>
            </p>
            <% } %>
              <p><strong>Status:</strong> Processing</p>
    </div>

    <p>Your refund is now being processed. Please note the following:</p>

    <div class="steps">
      <ol>
        <li>Refunds typically take 5-10 business days to appear in your account</li>
        <li>The timing depends on your payment method and financial institution</li>
        <li>You'll receive another notification when the refund is complete</li>
      </ol>
    </div>

    <p>If you have any questions or need assistance, please contact our support team at <a
        href="mailto:<EMAIL>"><EMAIL></a>.</p>

    <p>Thank you for your understanding.</p>

    <p>Best regards,<br>The Money Owls Team</p>

    <div class="footer">
      <p>© <%= new Date().getFullYear() %> Money Owls. All rights reserved.</p>
      <p>
        <a href="https://moneyowls.co.uk/privacy-policy">Privacy Policy</a> |
        <a href="https://moneyowls.co.uk/terms-of-service">Terms of Service</a>
      </p>
    </div>
  </div>
</body>

</html>