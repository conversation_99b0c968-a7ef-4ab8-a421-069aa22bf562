const mongoose = require("mongoose");
const { paginate } = require("./plugins/paginate");

const querySchema = new mongoose.Schema(
  {
    ticketId: {
      type: String,
      required: true,
      unique: true,
    },
    query: {
      type: String,
      required: true,
      maxlength: 300,
      trim: true,
    },
    raisedBy: {
      type: mongoose.SchemaTypes.ObjectId,
      ref: "Users",
      required: true,
    },
    appointment: {
      type: mongoose.SchemaTypes.ObjectId,
      ref: "Appointment",
      default: null,
    },
    adminMessage: {
      type: String,
      maxlength: 300,
      trim: true,
    },
    status: {
      type: String,
      enum: ["unresolved", "resolved"],
      required: true,
      default: "unresolved",
    },
  },
  { timestamps: true }
);

querySchema.plugin(paginate);

const Query = mongoose.model("Query", querySchema);

module.exports = {
  Query,
};
