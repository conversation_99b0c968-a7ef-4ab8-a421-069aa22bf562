const Joi = require("joi");
const dotenv = require("dotenv");
dotenv.config(); // Load .env file

// schema of env files for validation
const envVarsSchema = Joi.object()
  .keys({
    NODE_ENV: Joi.string()
      .valid("test", "development", "production")
      .required(),
    PORT: Joi.number().default(8082),
    // Prod/Dev keys (always required)
    MONGODB_URL: Joi.string().required(),
    AWS_S3_SECRET_ACCESS_KEY: Joi.string().required(),
    AWS_S3_REGION: Joi.string().required(),
    AWS_S3_ACCESS_KEY_ID: Joi.string().required(),
    AWS_S3_BUCKET: Joi.string().required(),
    FIREBASE_SECRET: Joi.string().required(),
    RESEND_API_KEY: Joi.string().required(),
    // Test keys (only required for test)
    MONGODB_TEST_URL: Joi.string().optional(),
    STRIPE_TEST_SECRET_KEY: Joi.string().optional(),
    STRIPE_TEST_WEBHOOK_SECRET: Joi.string().optional(),
    STRIPE_TEST_CONNECT_WEBHOOK_SECRET: Joi.string().optional(),
    // Prod keys
    STRIPE_SECRET_KEY: Joi.string().required(),
    STRIPE_WEBHOOK_SECRET: Joi.string().required(),
    STRIPE_CONNECT_WEBHOOK_SECRET: Joi.string().required(),
  })
  .unknown();

const { value: envVars, error } = envVarsSchema
  .prefs({ errors: { label: "key" } })
  .validate(process.env);

if (error) {
  throw new Error(`Config validation error: ${error.message}`);
}

// Utility for test/prod switch
const isTest = envVars.NODE_ENV === "development";

// Mongo URL logic
const mongooseUrl = isTest ? envVars.MONGODB_URL : envVars.MONGODB_URL_LIVE;

// Stripe logic
const stripeConfig = isTest
  ? {
      secretKey: envVars.STRIPE_TEST_SECRET_KEY,
      webhookSecret: envVars.STRIPE_TEST_WEBHOOK_SECRET,
      connectWebhookSecret: envVars.STRIPE_TEST_CONNECT_WEBHOOK_SECRET,
    }
  : {
      secretKey: envVars.STRIPE_SECRET_KEY,
      webhookSecret: envVars.STRIPE_WEBHOOK_SECRET,
      connectWebhookSecret: envVars.STRIPE_CONNECT_WEBHOOK_SECRET,
    };

module.exports = {
  env: envVars.NODE_ENV,
  port: envVars.PORT,
  CHAT_NOTIFICATION_MS_THRESHOLD: envVars.MS_THRESHOLD,

  firebase_secret: envVars.FIREBASE_SECRET,
  twilio: {
    sid: envVars.TWILIO_SID,
    phone: envVars.TWILIO_PHONE,
    authToken: envVars.TWILIO_AUTH_TOKEN,
  },
  aws: {
    s3: {
      name: envVars.AWS_S3_BUCKET,
      region: envVars.AWS_S3_REGION,
      accessKeyId: envVars.AWS_S3_ACCESS_KEY_ID,
      secretAccessKey: envVars.AWS_S3_SECRET_ACCESS_KEY,
    },
  },
  mongoose: {
    url: mongooseUrl,
    options: {},
  },
  agora: {
    customerKey: envVars.AGORA_CUSTOMER_ID,
    customerSecret: envVars.AGORA_CUSTOMER_CERTIFICATE,
    appId: envVars.AGORA_APP_ID,
    appCertificate: envVars.AGORA_APP_CERTIFICATE,
    location: envVars.AGORA_SERVER_LOCATION,
    whiteboard: {
      appId: envVars.AGORA_WHITEBOARD_APP_ID,
      secretKey: envVars.AGORA_WHITEBOARD_SECRET_KEY,
      sdkToken: envVars.AGORA_WHITEBOARD_SDK_TOKEN,
      accessKey: envVars.AGORA_WHITEBOARD_ACCESS_KEY,
    },
  },
  stripe: stripeConfig,
  resend: {
    apiKey: envVars.RESEND_API_KEY,
  },
};
