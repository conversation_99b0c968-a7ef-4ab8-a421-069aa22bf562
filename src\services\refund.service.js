const { Refund } = require("../models");
const moment = require("moment");

async function getAllRefunds(filters = {}, options = {}) {
  options.populate = [
    "appointmentId::user,adviser,bookingId",
    "appointmentId.adviser::name",
    "appointmentId.user::name",
  ];
  options.project = {
    _id: 1,
    appointmentId: 1,
    amount: 1,
    createdAt: 1,
    status: 1,
  };
  const refunds = await Refund.paginate(filters, options);
  return refunds;
}

async function getRefundWhere(receivedQuery, populateFields = []) {
  let query = Refund.find(receivedQuery);

  populateFields.forEach((field) => {
    query = query.populate(field);
  });

  const refund = await query.exec();
  return refund;
}

async function createRefund(refundData) {
  const refund = new Refund(refundData);
  return await refund.save();
}

async function updateRefund(refundId, updateData) {
  const refund = await Refund.findOneAndUpdate({ _id: refundId }, updateData, {
    new: true,
    runValidators: true,
  });

  return refund;
}

async function countRefunds(filters = {}) {
  return await Refund.countDocuments(filters);
}

async function getOne(receivedQuery, populateFields = []) {
  let query = Refund.findOne(receivedQuery);

  populateFields.forEach((field) => {
    query = query.populate(field);
  });

  const refund = await query.exec();
  return refund;
}

async function getTotalRefunds(filters = {}) {
  // Default to succeeded status if not specified
  const query = {
    status: "succeeded",
    ...filters,
  };

  const result = await Refund.aggregate([
    { $match: query },
    {
      $group: {
        _id: null,
        totalRefundAmount: { $sum: "$amount" },
        count: { $sum: 1 },
      },
    },
  ]);

  if (result.length === 0) {
    return {
      totalRefundAmount: 0,
      count: 0,
    };
  }

  return {
    totalRefundAmount: parseFloat(result[0].totalRefundAmount.toFixed(2)),
    count: result[0].count,
  };
}

async function getMonthlyRefundsForYear(year) {
  const start = new Date(year, 0, 1); // January 1st of the specified year
  const end = new Date(year, 11, 31, 23, 59, 59); // December 31st of the specified year

  // Get monthly refund data
  const result = await Refund.aggregate([
    {
      $match: {
        createdAt: { $gte: start, $lte: end },
        status: "succeeded",
      },
    },
    {
      $group: {
        _id: { $month: "$createdAt" },
        total: { $sum: "$amount" },
      },
    },
    { $sort: { _id: 1 } }, // Sort by month (1 = ascending)
  ]);

  return result;
}
const getYearlyRefundData = async (year) => {
  const start = moment().year(year).startOf("year").toDate();
  const end = moment().year(year).endOf("year").toDate();

  // Aggregate refund data for the given year
  const result = await Refund.aggregate([
    {
      $match: {
        createdAt: { $gte: start, $lte: end },
      },
    },
    {
      $group: {
        _id: null,
        totalRefundAmount: { $sum: "$totalAmount" },
      },
    },
  ]);

  return result[0] || { totalRefundAmount: 0 };
};

module.exports = {
  createRefund,
  getRefundWhere,
  getAllRefunds,
  updateRefund,
  countRefunds,
  getOne,
  getTotalRefunds,
  getMonthlyRefundsForYear,
};
