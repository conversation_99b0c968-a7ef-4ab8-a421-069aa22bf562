const Agenda = require("agenda");
const config = require("../config/config");
const logger = require("../config/logger");
const {
  Goal,
  AppNotification,
  CustomerSubscription,
  SubscriptionProduct,
  Appointment,
  User,
  Adviser,
} = require("../models/index");
const { sendToTopic } = require("../microservices/notification.service");
const { notificationCategories, emitterEventNames } = require("../constants");
const { capitalizeEachWord } = require("../utils/helper");
const eventEmitter = require("../events/eventEmitter");

const { default: mongoose } = require("mongoose");

const agenda = new Agenda({
  db: { address: config.mongoose.url },
});

//////////////////////// Define Jobs //////////////////////

// // Define job for appointment end notification
// agenda.define(/^appointment-end-notification-.*$/, async (job) => {
//   const {
//     topic,
//     title,
//     description,
//     type,
//     appointmentId,
//     targetUser,
//     image = "",
//   } = job.attrs.data;

//   if (
//     !topic ||
//     !title ||
//     !description ||
//     !type ||
//     !appointmentId ||
//     !targetUser
//   ) {
//     console.log(
//       "Missing required fields in the appointment end notification job. Skipping..."
//     );
//     return job.remove();
//   }

//   // Check if the appointment still exists and is still active
//   const appointment = await Appointment.findById(appointmentId);
//   if (!appointment) {
//     console.log(`Appointment not found: ${appointmentId}. Removing job.`);
//     return job.remove();
//   }

//   if (appointment.status !== "scheduled") {
//     console.log(
//       `Appointment ${appointmentId} is no longer scheduled. Skipping notification.`
//     );
//     return job.remove();
//   }

//   try {
//     // Send notification to the adviser
//     await sendToTopic(
//       topic,
//       {
//         title,
//         body: description,
//         image: "",
//       },
//       {
//         type,
//         appointmentId: appointmentId.toString(),
//       }
//     );

//     console.log(
//       `✅ Appointment end notification sent to adviser for appointment ${appointmentId}`
//     );
//   } catch (error) {
//     console.error(
//       `❌ Failed to send appointment end notification: ${error.message}`
//     );
//   } finally {
//     // Remove the job after it completes
//     await job.remove();
//     console.log(
//       `🧹 Appointment end notification job for appointment ${appointmentId} removed.`
//     );
//   }
// });

// Define job to mark appointment as cancelled if adviser hasn't joined by end time
agenda.define("appointment-auto-cancel", async (job) => {
  const { appointmentId } = job.attrs.data;

  if (!appointmentId) {
    // console.log("❌ Missing appointmentId in auto-cancel job. Skipping...");
    return job.remove();
  }

  // console.log(
  //   `🔍 Checking auto-cancel conditions for appointment ${appointmentId}`
  // );

  const appointment =
    await Appointment.findById(appointmentId).populate("adviser");
  if (!appointment) {
    // console.log(`❌ Appointment not found: ${appointmentId}. Removing job.`);
    return job.remove();
  }

  if (appointment.status !== "scheduled") {
    // console.log(
    //   `ℹ️ Appointment ${appointmentId} already ${appointment.status}. Skipping auto-cancel.`
    // );
    return job.remove();
  }

  const adviserName = appointment.adviser?.name || "the adviser";
  let cancelReason = "";
  let customMessage = "";

  if (appointment.adviserStatus !== "accepted") {
    cancelReason = "Adviser did not accept the appointment";
    customMessage = `The appointment with ${adviserName} was cancelled because they did not accept it in time.`;
  } else if (
    appointment.adviserStatus === "accepted" &&
    !appointment.hasAdviserJoined
  ) {
    cancelReason = "Adviser did not join the call";
    customMessage = `The scheduled call with ${adviserName} was cancelled as they did not join the session.`;
  }

  if (!cancelReason) {
    // console.log(`✅ No need to cancel appointment ${appointmentId}`);
    return job.remove();
  }

  try {
    appointment.status = "cancelled";
    appointment.systemCancellationReason = cancelReason;
    await appointment.save();

    // console.log(
    //   `❗ Appointment ${appointmentId} auto-cancelled: ${cancelReason}`
    // );

    if (appointment.user) {
      const userId = appointment.user.toString();

      await sendToTopic(
        userId,
        {
          title: `Appointment Cancelled - ${appointment.bookingId}`,
          body: customMessage,
          image: "",
        },
        {
          type: notificationCategories.APPOINTMENTS,
          appointmentId: appointmentId.toString(),
        }
      );

      await AppNotification.create({
        title: `Appointment Cancelled - ${appointment.bookingId}`,
        description: customMessage,
        type: notificationCategories.APPOINTMENTS,
        targetUser: userId,
        scheduledAt: Date.now(),
        isCreatedByAdmin: false,
        data: { appointmentId: appointmentId.toString() },
      });

      eventEmitter.emit(emitterEventNames.SYSTEM_CANCELLATION, {
        appointment,
        targetUserId: appointment.user,
        reason: cancelReason,
      });

      // console.log(
      //   `📢 Notified user about cancellation of appointment ${appointmentId}`
      // );
    }
  } catch (error) {
    console.error(
      `❌ Failed to auto-cancel appointment ${appointmentId}: ${error.message}`
    );
  } finally {
    await job.remove();
    // console.log(`🧹 Removed auto-cancel job for appointment ${appointmentId}`);
  }
});

// Define the job to update a goal's monthly contribution

agenda.define("update monthly goal contribution", async (job) => {
  const { goalId } = job.attrs.data;
  try {
    await updateMonthlyContributions(goalId);
  } catch (error) {
    logger.error(
      `Error updating monthly contributions for goal ${goalId}: ${error.message}`
    );
  }
});

agenda.define("schedule notification", async (job) => {
  const triggeredAt = new Date().toISOString();

  const { topic, title, description, type, targetUser, image, appointmentId } =
    job.attrs.data;

  // console.log(
  //   `📅 [Agenda Triggered] schedule notification @ ${triggeredAt} for appointment id ${appointmentId}`
  // );
  if (
    !topic ||
    !title ||
    !description ||
    !type ||
    !targetUser ||
    !appointmentId
  ) {
    // console.log("⚠️ Missing required fields in the job data. Skipping...");
    return job.remove();
  }

  const appointment = await Appointment.findById(appointmentId);
  if (!appointment) {
    // console.log(`⚠️ Appointment not found: ${appointmentId}. Job removed.`);
    return job.remove();
  }

  if (topic.toString() === appointment.user.toString()) {
    if (appointment.isReminderSet === false) {
      // console.log("⏩ Reminder skipped: User has disabled reminders.");
      return job.remove();
    }
  }

  if (appointment.status !== "scheduled") {
    // console.log("⏩ Reminder skipped: Appointment is not scheduled.");
    return job.remove();
  }

  if (appointment.adviserStatus !== "accepted") {
    // console.log("⏩ Reminder skipped: Adviser has not accepted.");
    return job.remove();
  }

  if (!["paid", "free"].includes(appointment.paymentStatus)) {
    // console.log("⏩ Reminder skipped: Invalid payment status.");
    return job.remove();
  }

  const adviser = await Adviser.findById(appointment.adviser);
  if (adviser?.isDeleted) {
    // console.log("⏩ Reminder skipped: Adviser is deleted.");
    if (targetUser.toString() === appointment.adviser.toString()) {
      return job.remove();
    }
    await sendToTopic(
      appointment.user.toString(),
      {
        title: "Sorry for the inconvenience.",
        body: "The adviser for your appointment is no longer available. You'll be refunded. Sorry for the inconvenience.",
        image: "",
      },
      {
        type: notificationCategories.GENERAL,
      }
    );
    return job.remove();
  }

  try {
    // console.log(`📨 Sending notification to topic: ${topic}`);
    await sendToTopic(
      topic,
      {
        title,
        body: description,
        image: "",
      },
      {
        type,
      }
    );

    const notificationData = {
      title,
      description,
      type,
      targetUser,
      targetRole: null,
      scheduledAt: Date.now(),
      isCreatedByAdmin: false,
      image,
    };

    const newNotification = new AppNotification(notificationData);
    await newNotification.save();

    // console.log(`✅ Notification sent & saved for topic "${topic}"`);
  } catch (error) {
    console.error(
      `❌ Failed to send notification to topic "${topic}": ${error.message}`
    );
  } finally {
    await job.remove();
    // console.log(`🧹 Agenda job for topic "${topic}" removed.`);
  }
});

agenda.define("expire subscription", async (job) => {
  const { stripeSubscriptionId, subscriptionProduct, userId } = job.attrs.data;

  // console.log(
  //   `🚀 Running subscription expiry job for ${stripeSubscriptionId} (with 5-minute buffer)`
  // );

  try {
    // Step 1: Fetch current subscription
    const currentSubscription = await CustomerSubscription.findOne({
      stripeSubscriptionId,
    });

    if (!currentSubscription) {
      console.warn(`⚠️ Subscription not found for ID: ${stripeSubscriptionId}`);
      return job.remove();
    }

    // Step 2: Check if it’s already renewed or inactive
    const now = new Date();
    const originalEndDate = currentSubscription.endDate;

    if (
      currentSubscription.endDate > now || // Already renewed
      currentSubscription.status === "cancelled"
    ) {
      // console.log(
      //   `🔁 Subscription ${stripeSubscriptionId} is either cancelled or renewed (endDate: ${currentSubscription.endDate}). Skipping expiry.`
      // );
      return job.remove();
    }

    // Step 3: Expire the subscription
    const expireSubscription = await CustomerSubscription.findOneAndUpdate(
      { stripeSubscriptionId },
      { new: true }
    );

    // Step 4: Decrease subscriber count
    const decreaseSubscriberCount = await SubscriptionProduct.findByIdAndUpdate(
      subscriptionProduct._id,
      { $inc: { subscriberCount: -1 } },
      { new: true, runValidators: true }
    );

    // Step 5: Notify user
    const user = await User.findById(userId);
    if (!user || user.isDeleted) {
      // console.log(
      //   `[EXPIRE-SUBSCRIPTION] User ${userId} is deleted or invalid. Skipping notification.`
      // );
      return job.remove();
    }

    await sendToTopic(
      userId.toString(),
      {
        title: "Subscription Expired",
        body: "Your subscription has expired.",
        image: "",
      },
      {
        type: notificationCategories.GENERAL,
      }
    );

    const notificationData = {
      title: "Subscription Expired",
      description: "Your subscription has expired.",
      type: notificationCategories.GENERAL,
      targetUser: userId,
      scheduledAt: Date.now(),
      isCreatedByAdmin: false,
    };

    await new AppNotification(notificationData).save();

    // console.log(`✅ Subscription expired for ${userId}`);
    // console.log(`✅ count decreased for ${subscriptionProduct._id}`);
  } catch (error) {
    console.error(
      `❌ Failed to expire subscription ${stripeSubscriptionId}:`,
      error.message
    );
  }
});

// to send reminder to take subscription
agenda.define("check-subscription-status", async (job) => {
  const { userId } = job.attrs.data;
  // console.log(job.attrs.data, "<<<<<<@@@");
  // console.log(`[CHECK-SUBSCRIPTION-STATUS] Job started for user ${userId}`);

  try {
    const user = await User.findById(new mongoose.Types.ObjectId(userId));
    if (user.isDeleted) {
      // console.log(
      //   `[CHECK-SUBSCRIPTION-STATUS] User ${userId} is deleted. Removing job.`
      // );
      return job.remove();
    }
    // console.log(
    //   `[CHECK-SUBSCRIPTION-STATUS] Checking active subscription for user ${userId}`
    // );
    const hasSubscription = await CustomerSubscription.findOne({
      user: userId,
      isActive: true,
      endDate: { $gte: new Date() },
    });

    if (!hasSubscription) {
      // console.log(
      //   `[CHECK-SUBSCRIPTION-STATUS] No active subscription found for user ${userId}`
      // );

      const user = await User.findById(userId);
      // console.log("[CHECK-SUBSCRIPTION-STATUS] Found user:", user._id);

      // console.log(
      //   `[CHECK-SUBSCRIPTION-STATUS] Sending notification to user ${userId}`
      // );
      await sendToTopic(
        userId.toString(),
        {
          title: `${capitalizeEachWord(user.name)}, don't miss out on premium benefits`,
          body: "Enhance your experience with our subscription plans. Unlock unlimited access today!",
          image: "",
        },
        {
          type: notificationCategories.PROMOTIONAL,
        }
      );

      // console.log(
      //   `[CHECK-SUBSCRIPTION-STATUS] Saving notification in DB for user ${userId}`
      // );

      // const notificationData = {
      //   title: `${user.name} , don't miss out on premium benefits`,
      //   description:
      //     "Enhance your experience with our subscription plans. Unlock unlimited access today!",
      //   type: notificationCategories.PROMOTIONAL,
      //   targetUser: userId,
      //   targetRole: null,
      //   scheduledAt: Date.now(),
      //   isCreatedByAdmin: false,
      // };

      // const newNotification = new AppNotification(notificationData);
      // await newNotification.save();

      // console.log(
      //   `✅ [CHECK-SUBSCRIPTION-STATUS] Reminder notification sent to user ${userId}`
      // );
    } else {
      // console.log(
      //   `✅ [CHECK-SUBSCRIPTION-STATUS] User ${userId} already has an active subscription. No reminder sent.`
      // );
      return job.remove();
    }
  } catch (error) {
    console.error(
      `❌ [CHECK-SUBSCRIPTION-STATUS] Error for user ${userId}:`,
      error.message,
      error
    );
  } finally {
    await job.remove();
    // console.log(`[CHECK-SUBSCRIPTION-STATUS] Job removed for user ${userId}`);
  }
});

agenda.define("send-subscription-reminder", async (job) => {
  const { userId } = job.attrs.data;

  // console.log(`[SEND-SUBSCRIPTION-REMINDER] Job started for user ${userId}`);

  try {
    // console.log(
    //   `[SEND-SUBSCRIPTION-REMINDER] Checking active subscription for user ${userId}`
    // );
    const user = await User.findById(new mongoose.Types.ObjectId(userId));
    if (user.isDeleted) {
      // console.log(
      //   `[SEND-SUBSCRIPTION-REMINDER] User ${userId} is deleted. Removing job.`
      // );
      return job.remove();
    }

    const hasSubscription = await CustomerSubscription.findOne({
      user: userId,
      isActive: true,
      endDate: { $gte: new Date() },
    });

    if (!hasSubscription) {
      // console.log(
      //   `[SEND-SUBSCRIPTION-REMINDER] No active subscription found for user ${userId}`
      // );

      const user = await User.findById(userId);
      // console.log("[SEND-SUBSCRIPTION-REMINDER] Found user:", user._id);

      // console.log(
      //   `[SEND-SUBSCRIPTION-REMINDER] Sending notification to user ${userId}`
      // );
      await sendToTopic(
        userId.toString(),
        {
          title: `${capitalizeEachWord(user.name)}, Enhance Your Experience with a Subscription`,
          body: "Get premium features with our subscription plans. Subscribe now for the best experience!",
          image: "",
        },
        {
          type: notificationCategories.PROMOTIONAL,
        }
      );

      // console.log(
      //   `✅ [SEND-SUBSCRIPTION-REMINDER] Reminder notification sent to user ${userId}`
      // );
    } else {
      // console.log(
      //   `✅ [SEND-SUBSCRIPTION-REMINDER] User ${userId} already has an active subscription. No reminder sent.`
      // );
      return job.remove();
    }
  } catch (error) {
    console.error(
      `❌ [SEND-SUBSCRIPTION-REMINDER] Error for user ${userId}:`,
      error.message,
      error
    );
  } finally {
    await job.remove();
    // console.log(`[SEND-SUBSCRIPTION-REMINDER] Job removed for user ${userId}`);
  }
});

//////////////////////// Job Scheduling Services //////////////////////

const scheduleNextContribution = async (goal) => {
  if (goal.isCompleted) {
    // console.log(`Goal ${goal._id} is completed. Stopping contributions.`);
    return;
  }
  const lastContributionDate = goal.lastContributionDate || goal.createdAt;
  const nextRunDate = new Date(lastContributionDate);

  nextRunDate.setMonth(nextRunDate.getMonth() + 1);

  const lastDayOfNextMonth = new Date(
    nextRunDate.getFullYear(),
    nextRunDate.getMonth() + 1,
    0
  ).getDate();

  nextRunDate.setDate(
    Math.min(lastContributionDate.getDate(), lastDayOfNextMonth)
  );

  // goal.lastContributionDate = nextRunDate;
  await goal.save();

  // Schedule the job with Agenda
  await agenda.schedule(nextRunDate, "update monthly goal contribution", {
    goalId: goal._id,
  });

  // console.log(
  //   `Scheduled next contribution update for goal ${goal._id} at ${nextRunDate}`
  // );
};

async function scheduleNotification(
  scheduleTime,
  { topic, title, description, type, appointmentId, targetUser, image = "" }
) {
  // console.log("Scheduling notification with the following details:");
  // console.log(`- Schedule Time: ${scheduleTime}`);
  // console.log(`- Topic: ${topic}`);
  // console.log(`- Title: ${title}`);
  // console.log(`- Description: ${description}`);
  // console.log(`- type: ${type}`);
  // console.log(`- appointmentId: ${appointmentId}`);
  // console.log(`- targetUser: ${targetUser}`);

  await agenda.schedule(scheduleTime, "schedule notification", {
    topic,
    title,
    description,
    type,
    appointmentId,
    targetUser,
    image,
  });
}

async function updateMonthlyContributions(goalId) {
  const goal = await Goal.findById(goalId);

  if (!goal) {
    // console.log(
    //   `Goal ${goalId} not found. Cancelling scheduled contributions.`
    // );
    return;
  }
  // console.log(`Updating goal ${goalId}: current amount is ${goal.amount}`);

  if (goal && !goal.isCompleted) {
    goal.amount += goal.monthlyContribution;
    goal.completedPercentage = (goal.amount / goal.targetAmount) * 100;

    // Check if the goal has been reached or exceeded
    if (goal.amount >= goal.targetAmount) {
      goal.amount = goal.targetAmount;
      goal.isCompleted = true;
      goal.completedPercentage = 100;
    }
    goal.lastContributionDate = new Date();

    await goal.save();
    // console.log(`Goal ${goalId} updated: current amount is ${goal.amount}`);

    // Schedule the next contribution update
    await scheduleNextContribution(goal);
  }
}

async function scheduleExpireSubscription(customerSubscription) {
  if (
    !customerSubscription.endDate ||
    !customerSubscription.stripeSubscriptionId ||
    !customerSubscription.subscriptionProduct ||
    !customerSubscription.user
  ) {
    console.error("❌ Invalid subscription data. Expiry not scheduled.");
    return;
  }

  // Add 5 minutes to the endDate
  const endDate = new Date(customerSubscription.endDate);
  endDate.setMinutes(endDate.getMinutes() + 5);

  if (isNaN(endDate.getTime())) {
    console.error("❌ Invalid endDate for subscription expiration.");
    return;
  }

  // console.log(
  //   `✅ Scheduling subscription expiry for ${endDate} (original endDate + 5 minutes)`
  // );

  await agenda.schedule(endDate, "expire subscription", {
    stripeSubscriptionId: customerSubscription.stripeSubscriptionId,
    subscriptionProduct: customerSubscription.subscriptionProduct,
    userId: customerSubscription.user._id,
  });
}

async function scheduleAppointmentAutoCancel(appointmentId, endTime) {
  if (!appointmentId || !endTime) {
    console.error("❌ Invalid data for scheduling appointment auto-cancel.");
    return;
  }

  const jobName = "appointment-auto-cancel";

  // Cancel any existing job for this specific appointmentId
  const existingJobs = await agenda.jobs({
    name: jobName,
    "data.appointmentId": appointmentId.toString(),
  });

  if (existingJobs.length > 0) {
    // console.log(
    //   `🗑️ Removing existing auto-cancel job for appointment ${appointmentId}`
    // );
    await agenda.cancel({
      name: jobName,
      "data.appointmentId": appointmentId.toString(),
    });
  }

  // console.log(
  //   `📅 Scheduling auto-cancel job for appointment ${appointmentId} at ${endTime}`
  // );

  // Schedule the job
  await agenda.schedule(endTime, jobName, {
    appointmentId: appointmentId.toString(),
  });

  // console.log(`✅ Auto-cancel job scheduled for appointment ${appointmentId}`);
}

//////////////////////// Start Agenda //////////////////////

// Start agenda
(async function () {
  await agenda.start();
  logger.info("Agenda service has been setup! 🎉");
})();

// Schedule a job to auto-cancel an appointment if adviser doesn't join

module.exports = {
  scheduleNextContribution,
  scheduleNotification,
  agenda,
  scheduleExpireSubscription,
  scheduleAppointmentAutoCancel,
};
