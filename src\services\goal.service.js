const { Goal } = require("../models");

async function createGoal(goalData, userId) {
  const goal = new Goal({ ...goalData, userId });
  const savedGoal = await goal.save();
  return savedGoal;
}

async function updateGoal(goalId, goalData) {
  const updatedGoal = await Goal.findByIdAndUpdate(goalId, goalData, {
    new: true,
    runValidators: true,
  });
  return updatedGoal;
}

async function deleteGoal(goalId) {
  const goal = await Goal.findByIdAndDelete(goalId);
  return true;
}

async function getAllGoals(filter, options = {}) {
  const goals = await Goal.paginate(filter, options);
  return goals;
}

async function getGoalById(goalId) {
  const goal = await Goal.findById(goalId);
  return goal;
}

module.exports = {
  createGoal,
  updateGoal,
  deleteGoal,
  getAllGoals,
  getGoalById,
};
