const { ChatRoom, Message, Users } = require("../models");
const { realtimeDb } = require("../config/firebase.realtimedb");
const ApiError = require("../utils/ApiError");
const httpStatus = require("http-status");
const mongoose = require("mongoose");
const { sendToTopic } = require("../microservices/notification.service");
const { appNotificationService } = require(".");
const { notificationCategories } = require("../constants");
const config = require("../config/config");

const createChatRoom = async (participants) => {
  try {
    // Ensure exactly 2 participants for private chat
    if (participants.length !== 2) {
      throw new ApiError(
        httpStatus.BAD_REQUEST,
        "A private chat must have exactly 2 participants."
      );
    }

    // Fetch participant details
    const participantDetails = await Users.find({
      _id: { $in: participants },
    }).select("name profilePic");

    const participantsWithDetails = participantDetails.map((participant) => ({
      id: participant._id.toString(),
      name: participant.name,
      image: participant.profilePic ? participant.profilePic.url : null,
    }));

    // Create chat room
    const chatRoom = await ChatRoom.create({
      participants: participants.map((participant) => participant.toString()),
    });

    // Prepare data for the real-time database
    const realtimeDbData = {
      participants: participantsWithDetails,
      createdAt: new Date().toISOString(),
    };

    await realtimeDb
      .ref(`chatRooms/${chatRoom._id.toString()}`)
      .set(realtimeDbData);

    return chatRoom;
  } catch (error) {
    throw new ApiError(
      httpStatus.INTERNAL_SERVER_ERROR,
      `Failed to create chat room: ${error.message}`
    );
  }
};

//notification triggered
const RECENT_THRESHOLD_MS = config.CHAT_NOTIFICATION_MS_THRESHOLD; // 120 seconds
const sendMessage = async (
  senderId,
  content = null,
  attachments,
  replyTo = null,
  chatRoomId = null
) => {
  try {
    if (!chatRoomId) {
      throw new ApiError(httpStatus.BAD_REQUEST, "Chat room ID is required.");
    }

    // Load room + sender + last message in parallel
    const [chatRoom, senderDetails, lastMsg] = await Promise.all([
      ChatRoom.findById(chatRoomId).populate("participants", "name profilePic"),
      Users.findById(senderId).select("name profilePic __t"),
      Message.findOne({ chat: chatRoomId, sender: senderId })
        .sort({ createdAt: -1 })
        .select("createdAt"),
    ]);
    // console.log(lastMsg);
    if (!chatRoom) {
      throw new ApiError(httpStatus.NOT_FOUND, "Chat room not found.");
    }
    if (!senderDetails) {
      throw new ApiError(httpStatus.NOT_FOUND, "Sender not found.");
    }

    // Identify recipient once
    const recipient = chatRoom.participants.find(
      (p) => p._id.toString() !== senderId.toString()
    );
    if (!recipient) {
      throw new ApiError(httpStatus.NOT_FOUND, "Recipient not found.");
    }
    const recipientId = recipient._id;

    const now = new Date();
    const isTooSoon = lastMsg && now - lastMsg.createdAt < RECENT_THRESHOLD_MS;

    // console.log(isTooSoon);
    // Create the message document
    const message = await Message.create({
      chat: chatRoomId,
      sender: senderId,
      content,
      attachments,
      replyTo,
      createdAt: now,
    });

    // Build realtime payload
    const profileImage = senderDetails.profilePic
      ? {
          key: senderDetails.profilePic.key,
          url: senderDetails.profilePic.url,
        }
      : null;

    const realtimeMessageData = {
      chatRoomId,
      senderId,
      content,
      attachments,
      replyTo: replyTo ? replyTo.toString() : null,
      createdAt: now.toISOString(),
      name: senderDetails.name,
      profileImage,
    };

    // Gather all writes (some conditionally)
    const tasks = [
      // Firebase writes
      realtimeDb
        .ref(`chatRooms/${chatRoomId}/messages/${message._id}`)
        .set(realtimeMessageData),
      realtimeDb.ref(`chatRooms/${chatRoomId}`).update({
        lastMessageId: message._id.toString(),
        lastMessageTimestamp: now.toISOString(),
      }),

      // MongoDB unread + lastMessage
      ChatRoom.findByIdAndUpdate(chatRoomId, {
        lastMessage: message._id,
        $inc: { [`unreadMessageCount.${recipientId}`]: 1 },
      }),

      // FCM push
      sendToTopic(
        recipientId.toString(),
        {
          title: "New Message",
          body: !isTooSoon
            ? `${senderDetails.name} sent you a message`
            : content,
        },
        {
          chatRoomId: chatRoomId.toString(),
          name: senderDetails.name,
          profileImage: profileImage?.url || "",
          type: notificationCategories.CHAT,
          message: content,
          // countIncrease: isTooSoon ? "0" : "1",
        },

        { groupKey: `chat_${chatRoomId}` }
      ),

      // Only create in-app notification if not too soon
      // !isTooSoon &&
      //   appNotificationService.createNotification({
      //     title: "New Message",
      //     description: `${senderDetails.name} sent you a message`,
      //     targetUser: recipientId,
      //     type: notificationCategories.CHAT,
      //     data: {
      //       chatRoomId,
      //       name: senderDetails.name,
      //       profileImage: profileImage?.url || null,
      //       message: content,
      //     },
      //     scheduledAt: now.getTime(),
      //     isCreatedByAdmin: false,
      //   }),
    ];

    // Execute all tasks in parallel, filtering out any falsey (i.e. skipped notification)
    await Promise.all(tasks.filter(Boolean));

    return message;
  } catch (err) {
    throw new ApiError(
      httpStatus.INTERNAL_SERVER_ERROR,
      `Failed to send message: ${err.message}`
    );
  }
};

const getMessages = async (chatRoomId, userId, page = 1, limit = 10) => {
  const session = await mongoose.startSession();
  session.startTransaction();
  try {
    if (!mongoose.Types.ObjectId.isValid(chatRoomId)) {
      throw new ApiError(httpStatus.BAD_REQUEST, "Invalid chatRoomId format");
    }

    const filters = {
      chat: new mongoose.Types.ObjectId(chatRoomId),
      $or: [
        { sender: { $ne: new mongoose.Types.ObjectId(userId.toString()) } }, // Include messages from other users
        { $and: [{ sender: userId }, { isDeletedForMe: { $ne: true } }] }, // Exclude user's deleted messages
      ],
    };

    const options = {
      page,
      limit,
      populate: [
        "sender::_id,name,profilePic",
        "replyTo::_id,content",
        "chat::_id",
      ],
      sortBy: "createdAt",
      sortOrder: "desc",
    };

    const [chatRoom, messages] = await Promise.all([
      ChatRoom.findById(chatRoomId).select("_id participants").session(session),

      Message.paginate(filters, options),
    ]);

    if (!chatRoom) {
      throw new ApiError(httpStatus.NOT_FOUND, "Chat room not found");
    }

    await Promise.all([
      Message.updateMany(
        { chat: chatRoomId, readBy: { $ne: userId } },
        { $addToSet: { readBy: userId } },
        { session }
      ),
      ChatRoom.findByIdAndUpdate(
        chatRoomId,
        { $set: { [`unreadMessageCount.${userId}`]: 0 } },
        { session }
      ),
    ]);
    await session.commitTransaction();
    session.endSession();
    return messages;
  } catch (error) {
    await session.abortTransaction();
    session.endSession();
    throw new ApiError(
      httpStatus.INTERNAL_SERVER_ERROR,
      `Failed to fetch messages: ${error.message}`
    );
  }
};

const getChatRoomsForUser = async (userId) => {
  try {
    const chatRooms = await ChatRoom.find({
      participants: { $in: [userId] },
    })
      .populate("participants", "name profilePic")
      .populate({
        path: "lastMessage",
        select: "content attachments createdAt",
      })
      .lean();

    // Sort chat rooms based on the latest message timestamp (newest first)
    chatRooms.sort((a, b) => {
      if (!a.lastMessage || !b.lastMessage) return 0;
      return (
        new Date(b.lastMessage.createdAt) - new Date(a.lastMessage.createdAt)
      );
    });

    const processedChatRooms = chatRooms.map((room) => {
      const userUnreadCount = room.unreadMessageCount?.[userId.toString()] || 0;

      return {
        ...room,
        lastMessage: room.lastMessage || { content: "", attachments: [] },
        unreadMessageCount: userUnreadCount, // only for current user
      };
    });

    return processedChatRooms;
  } catch (error) {
    throw new Error(`Failed to get chat rooms: ${error.message}`);
  }
};

const getOrCreateOneToOneChatRoom = async (user1Id, user2Id) => {
  try {
    let chatRoom = await ChatRoom.findOne({
      participants: { $all: [user1Id, user2Id] },
    });

    if (!chatRoom) {
      chatRoom = await ChatRoom.create({
        participants: [user1Id.toString(), user2Id.toString()],
      });

      const user1 = await Users.findById(user1Id).select("name profilePic");
      const user2 = await Users.findById(user2Id).select("name profilePic");

      const participantsWithDetails = [
        {
          id: user1._id,
          name: user1.name || "Unknown",
          image: user1.profilePic ? user1.profilePic.url : null,
        },
        {
          id: user2._id,
          name: user2.name || "Unknown",
          image: user2.profilePic ? user2.profilePic.url : null,
        },
      ];

      const realtimeDbData = {
        participants: participantsWithDetails,
        createdAt: new Date().toISOString(),
      };

      await realtimeDb
        .ref(`chatRooms/${chatRoom._id.toString()}`)
        .set(realtimeDbData);
    }

    return chatRoom._id;
  } catch (error) {
    throw new ApiError(
      httpStatus.INTERNAL_SERVER_ERROR,
      `Failed to get or create chat room: ${error.message}`
    );
  }
};

const deleteChatRoom = async (chatRoomId, userId) => {
  try {
    // Find the chat room by ID
    const chatRoom = await ChatRoom.findById(chatRoomId);
    if (!chatRoom) {
      throw new ApiError(httpStatus.NOT_FOUND, "Chat room not found");
    }

    // Ensure the requesting user is a participant in this chat room
    if (!chatRoom.participants.includes(userId.toString())) {
      throw new ApiError(
        httpStatus.FORBIDDEN,
        "You are not a participant in this chat room"
      );
    }

    // Delete chat room and all associated messages
    await ChatRoom.findByIdAndDelete(chatRoomId);
    await Message.deleteMany({ chat: chatRoomId });

    return { message: "Chat room deleted successfully", success: true };
  } catch (error) {
    throw new ApiError(
      httpStatus.INTERNAL_SERVER_ERROR,
      `Failed to delete chat room: ${error.message}`
    );
  }
};

const deleteMessage = async (messageId, userId) => {
  const message = await Message.findById(messageId);
  if (!message) {
    throw new ApiError(httpStatus.NOT_FOUND, "Message not found");
  }
  if (message.sender.toString() !== userId.toString()) {
    throw new ApiError(
      httpStatus.FORBIDDEN,
      "You are not authorized to delete this message"
    );
  }
  // await Message.findByIdAndDelete(messageId);
  message.isDeletedForMe = true;
  await realtimeDb
    .ref(`chatRooms/${message.chat}/messages/${message._id}`)
    .update({ isDeletedForMe: true });
  await message.save();
  return { message: "Message deleted successfully", success: true };
};

const getChatRoomForUserAndAdviser = async (userId, adviserId) => {
  try {
    // Fetch the chat room where both the user and adviser are participants
    const chatRoom = await ChatRoom.findOne({
      participants: { $all: [userId, adviserId] }, // Both user and adviser must be in the room
    })
      .populate("participants", "name profilePic") // Populate participants info
      .populate({
        path: "lastMessage",
        select: "content attachments createdAt",
      })
      .lean();

    if (!chatRoom) {
      return { message: "No chat room found between user and adviser." };
    }

    return {
      ...chatRoom,
      lastMessage: chatRoom.lastMessage || { content: "", attachments: [] },
    };
  } catch (error) {
    throw new Error(`Failed to get chat room: ${error.message}`);
  }
};

const getMessagesForAdmin = async (chatRoomId, page = 1, limit = 10) => {
  try {
    if (!mongoose.Types.ObjectId.isValid(chatRoomId)) {
      throw new ApiError(httpStatus.BAD_REQUEST, "Invalid chatRoomId format");
    }

    const filters = {
      chat: new mongoose.Types.ObjectId(chatRoomId),
    };

    const options = {
      page,
      limit,
      populate: [
        "sender::_id,name,profilePic",
        "replyTo::_id,content",
        "chat::_id",
      ],
      sortBy: "createdAt",
      sortOrder: "desc",
    };

    // Fetch chat room and messages (admin should be able to view all messages)
    const messages = await Message.paginate(filters, options);

    return messages;
  } catch (error) {
    throw new ApiError(
      httpStatus.INTERNAL_SERVER_ERROR,
      `Failed to fetch messages: ${error.message}`
    );
  }
};

module.exports = {
  createChatRoom,
  sendMessage,
  getMessages,
  getChatRoomsForUser,
  getOrCreateOneToOneChatRoom,
  deleteChatRoom,
  deleteMessage,
  getChatRoomForUserAndAdviser,
  getMessagesForAdmin,
};
