const { emitterEventNames, notificationCategories } = require("../constants");
const {
  appNotificationService,
  userService,
  customerSubscriptionService,
  stripeService,
  refundService,
} = require("../services");
const catchEventHandler = require("../utils/catchEventHandler");
const { mailService } = require("../microservices");
const ejs = require("ejs");
const path = require("path");

const handleRefundInitiated = catchEventHandler(async (data) => {
  const { amount, bookingId, userId } = data;

  // Create notification title and description
  const title = `Refund initiated`;
  let description = `A refund of £${amount} has been initiated for your booking ${bookingId}`;

  // Create and send notification
  await appNotificationService.createAndSendNotification(
    {
      title,
      description,
      type: notificationCategories.GENERAL,
      targetUser: userId,
      isCreatedByAdmin: false,
      metadata: {
        amount: amount,
        bookingId: bookingId,
      },
    },
    userId.toString()
  );

  // Get user details
  const user = await userService.getUserById(userId);
  if (!user) {
    console.error(`❌ User not found for refund notification: ${userId}`);
    return;
  }

  // Send email notification
  try {
    // console.log(`📧 Sending refund initiated email to ${user.email}`);

    // Format amount
    const formattedAmount = parseFloat(amount).toFixed(2);

    // Render email template
    const emailHtml = await ejs.renderFile(
      path.join(__dirname, "../views/emails/refundInitiated.ejs"),
      {
        name: user.name || "",
        email: user.email,
        amount: formattedAmount,
        bookingId: bookingId,
      }
    );

    // Send email
    await mailService.sendEmail({
      to: user.email,
      subject: "Money Owls - Refund Initiated",
      html: emailHtml,
    });

    // console.log(`✅ Refund initiated email sent to ${user.email}`);
  } catch (error) {
    console.error(`❌ Error sending refund initiated email: ${error.message}`);
  }

  // console.log(`✅ Refund initiated notification sent to user ${userId}`);
});

const handleRefundSucceeded = catchEventHandler(async (data) => {
  const { amount, bookingId, userId } = data;

  // Create notification title and description
  const title = "Refund Processed";
  let description = `A refund of £${amount} has been processed for your booking ${bookingId}`;

  // Create and send notification
  await appNotificationService.createAndSendNotification(
    {
      title,
      description,
      type: notificationCategories.GENERAL,
      targetUser: userId,
      isCreatedByAdmin: false,
      metadata: {
        amount: amount,
        bookingId: bookingId,
      },
    },
    userId.toString()
  );

  // Get user details
  const user = await userService.getUserById(userId);
  if (!user) {
    console.error(`❌ User not found for refund notification: ${userId}`);
    return;
  }

  // Send email notification
  try {
    // console.log(`📧 Sending refund success email to ${user.email}`);

    // Format amount
    const formattedAmount = parseFloat(amount).toFixed(2);

    // Render email template
    const emailHtml = await ejs.renderFile(
      path.join(__dirname, "../views/emails/refundSucceeded.ejs"),
      {
        name: user.name || "",
        email: user.email,
        amount: formattedAmount,
        bookingId: bookingId,
      }
    );

    // Send email
    await mailService.sendEmail({
      to: user.email,
      subject: "Money Owls - Refund Processed",
      html: emailHtml,
    });

    // console.log(`✅ Refund success email sent to ${user.email}`);
  } catch (error) {
    console.error(`❌ Error sending refund success email: ${error.message}`);
  }

  // console.log(`✅ Refund notification sent to user ${userId}`);
});

const handleSystemCancellation = catchEventHandler(async (data) => {
  const { appointment, targetUserId, reason } = data;

  try {
    switch (appointment.mode) {
      case "free":
        await userService.increaseFreeSessions(targetUserId);
        break;

      case "subscription":
        await customerSubscriptionService.increaseSessionsCount(targetUserId);
        break;

      case "payment":
        if (appointment.paymentIntent && appointment.paymentStatus === "paid") {
          const refundResponse = await stripeService.refundPayment(
            appointment.paymentIntent,
            appointment._id
          );

          if (!refundResponse.success) {
            if (refundResponse.code === "charge_already_refunded") {
              throw new Error("This charge has already been refunded.");
            }
            throw new Error(refundResponse.message || "Refund failed.");
          }

          // Record refund
          const refund = await refundService.createRefund({
            appointmentId: appointment._id,
            paymentIntentId: appointment.paymentIntent,
            refundId: refundResponse.refundId,
            amount: appointment.amount,
            isIntiatedBySystem: true,
            reason: reason,
          });

          // console.log("Refund created:", refund);
        }
        break;
    }

    // console.log(
    //   `✅ System cancellation handler completed for appointment ${appointment._id}`
    // );
  } catch (err) {
    console.error(`❌ Failed in SYSTEM_CANCELLATION handler: ${err.message}`);
  }
});

// Register event handlers
module.exports = (emitter) => {
  emitter.on(emitterEventNames.REFUND_INITIATED, handleRefundInitiated);
  emitter.on(emitterEventNames.REFUND_SUCCEEDED, handleRefundSucceeded);
  emitter.on(emitterEventNames.SYSTEM_CANCELLATION, handleSystemCancellation);
};
