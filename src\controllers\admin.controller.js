const httpStatus = require("http-status");
const {
  adviserService,
  appointmentService,
  userService,
  customerSubscriptionService,
  appNotificationService,
  authService,
  stripeService,
  appSettingsService,
  revenueService,
  adviserStripeService,
} = require("../services/index");
const ApiError = require("../utils/ApiError");
const catchAsync = require("../utils/catchAsync");
const { getPaginateConfig } = require("../utils/queryPHandler");
const { fileUploadService, mailService } = require("../microservices");
const ejs = require("ejs");
const path = require("path");
const moment = require("moment");
const { Users, Admin, Adviser } = require("../models");
const { getAuth } = require("firebase-admin/auth");
const { notificationCategories } = require("../constants");
const { userTypes } = require("../constants");
const { generateStrongPassword } = require("../utils/helper");
const { emitterEventNames, appDefaults } = require("../constants");
const eventEmitter = require("../events/eventEmitter");

const getCount = catchAsync(async (req, res) => {
  const [users, advisers, futureAppointments] = await Promise.all([
    userService.countUsers(),
    adviserService.countAdvisers(),
    appointmentService.countFutureAppointments(),
  ]);

  return res.status(httpStatus.OK).json({
    message: "Counted successfully",
    data: { users, advisers, futureAppointments },
  });
});

const updateProfile = catchAsync(async (req, res) => {
  const userId = req.user.id;
  let updateData = { ...req.body };

  // Fetch current user details before updating
  const currentUser = await Admin.findById(userId);
  if (!currentUser) {
    throw new ApiError(httpStatus.NOT_FOUND, "Admin not found");
  }

  if (req.file) {
    // Upload the new profile image
    const [profilePic] = await fileUploadService
      .s3Upload([req.file], "profile_picture")
      .catch((e) => {
        throw new ApiError(
          httpStatus.INTERNAL_SERVER_ERROR,
          `Failed to upload documents : ${e.message}`
        );
      });

    // Delete the old profile image from S3 if it exists
    if (currentUser.profilePic?.key) {
      await fileUploadService.s3Delete(currentUser.profilePic.key).catch(() => {
        console.log(
          "Failed to delete previous profile picture:",
          currentUser.profilePic.key
        );
      });
    }

    updateData.profilePic = profilePic;
  }

  // Update admin profile
  const admin = await Admin.findByIdAndUpdate(userId, updateData, {
    new: true,
    runValidators: true,
  });

  if (!admin) {
    throw new ApiError(httpStatus.NOT_FOUND, "Admin not found after update");
  }

  return res.status(httpStatus.OK).json({
    message: "Profile updated successfully.",
    data: admin,
  });
});

const getProfile = catchAsync(async (req, res) => {
  const userId = req.user.id;

  const admin = await Admin.findById(userId);
  if (!admin) {
    throw new ApiError(httpStatus.NOT_FOUND, "Admin not found");
  }

  return res.status(httpStatus.OK).json({
    message: "Profile found successfully.",
    data: admin,
  });
});

const getAllUsers = catchAsync(async (req, res) => {
  const { options, filters } = getPaginateConfig(req.query);
  const users = await userService.getUsersWithDetails(filters, options);

  if (!users.results.length) {
    throw new ApiError(httpStatus.OK, "NO users found");
  }

  return res.status(httpStatus.OK).json({
    message: "Users retrieved successfully",
    data: users,
  });
});

// const getAllAdvisers = catchAsync(async (req, res) => {
//   let { options, filters } = getPaginateConfig(req.query);
//   const advisers = await adviserService.getAllAdvisers(filters, options);
//   if (!advisers.results || advisers.results.length === 0) {
//     throw new ApiError(httpStatus.NOT_FOUND, "NO advisers found");
//   }
//   res.status(httpStatus.OK).json({
//     message: "advisers retrieved successfully",
//     data: advisers,
//   });
// });

const getAppointmentDetails = catchAsync(async (req, res) => {
  const { id } = req.params;

  if (!id) {
    throw new ApiError(httpStatus.BAD_REQUEST, "No id found in params");
  }

  const appointment = await appointmentService.getAppointmentDetails(id, [
    "user",
    "adviser",
  ]);
  if (!appointment) {
    throw new ApiError(httpStatus.NOT_FOUND, "No Appointment found");
  }
  return res.status(httpStatus.OK).json({
    message: "Appointment retrieved successfully",
    data: appointment,
  });
});

const getAdviserDocs = catchAsync(async (req, res) => {
  const adviserId = req.params.id;
  if (!adviserId) {
    throw new ApiError(httpStatus.BAD_REQUEST, "No adviser id found in params");
  }
  const adviser = await adviserService.getAdviserDetails({ _id: adviserId });
  if (!adviser) {
    throw new ApiError(httpStatus.NOT_FOUND, "Adviser not found");
  }

  return res.status(httpStatus.OK).send({
    message: "Documents fetched successfully",
    documents: adviser.documents,
  });
});

const approveAdviserDoc = catchAsync(async (req, res) => {
  const { adviserId, docId } = req.params;

  const adviser = await Adviser.findById(adviserId);

  if (!adviser) {
    throw new ApiError(httpStatus.NOT_FOUND, "Adviser not found");
  }

  const document = adviser.documents.find(
    (doc) => doc._id.toString() === docId.toString()
  );

  if (!document) {
    throw new ApiError(httpStatus.NOT_FOUND, "Document not found");
  }

  // Check if the document is already approved
  if (document.status === "approved") {
    return res.status(httpStatus.OK).json({
      message: "Document is already approved",
      data: document,
    });
  }

  const unapprovedDocs = adviser.documents.filter(
    (doc) => doc.status !== "approved"
  );

  const isLastPendingDoc = unapprovedDocs.length === 1;

  // If this is the last document pending approval, check Stripe onboarding

  if (isLastPendingDoc) {
    const stripeAdviser = await adviserStripeService.getOne({
      adviser: adviserId,
    });

    if (!stripeAdviser?.stripeAccountId) {
      throw new ApiError(
        httpStatus.BAD_REQUEST,
        "Stripe account not found. Cannot approve final document."
      );
    }
    const stripeAccount = await stripeService.checkOnboardStatus(
      stripeAdviser.stripeAccountId
    );
    if (
      !stripeAccount?.charges_enabled ||
      !stripeAccount?.payouts_enabled ||
      !stripeAccount?.details_submitted ||
      (!stripeAccount.requirements.currently_due.length === 0 &&
        !stripeAccount.requirements.eventually_due.length === 0)
    ) {
      throw new ApiError(
        httpStatus.BAD_REQUEST,
        "Stripe onboarding not completed. Cannot approve final document."
      );
    }
  }

  // Now approve the document
  document.status = "approved";

  // Check if now all documents are approved
  const allDocumentsApproved = adviser.documents.every(
    (doc) => doc.status === "approved"
  );

  if (allDocumentsApproved) {
    adviser.isVerified = true;

    eventEmitter.emit(emitterEventNames.ADVISER_VERIFIED, adviser);
  }

  await adviser.save();

  return res.status(httpStatus.OK).json({
    message: "Document verified successfully",
    data: document,
  });
});

const rejectAdviserDoc = catchAsync(async (req, res) => {
  const { adviserId, docId } = req.params;
  const { rejectReason } = req.body;

  const adviser = await Adviser.findById(adviserId);
  if (!adviser) {
    throw new ApiError(httpStatus.NOT_FOUND, "Adviser not found");
  }

  const document = adviser.documents.find(
    (doc) => doc._id.toString() === docId.toString()
  );
  if (!document) {
    throw new ApiError(httpStatus.NOT_FOUND, "Document not found");
  }

  document.status = "rejected";
  document.rejectReason = rejectReason;

  adviser.isVerified = false;

  await adviser.save();

  // Emit event with document details and rejection reason
  eventEmitter.emit(emitterEventNames.DOCUMENT_REJECTED, {
    user: adviser,
    rejectReason,
  });

  return res.status(httpStatus.OK).json({
    message: "Document rejected successfully",
    data: document,
  });
});

const getGenderStats = catchAsync(async (req, res) => {
  const genderStats = await Users.aggregate([
    { $match: { __t: { $ne: "Admin" }, isDeleted: false } },
    {
      $group: {
        _id: "$gender",
        count: { $sum: 1 },
      },
    },
    {
      $project: {
        _id: 0,
        gender: "$_id",
        count: 1,
      },
    },
  ]);

  if (!genderStats || genderStats.length === 0) {
    throw new ApiError(httpStatus.NOT_FOUND, "No gender data found");
  }

  // Calculate total users and gender counts in one pass
  const totalUsers = genderStats.reduce((total, item) => total + item.count, 0);
  const genderData = genderStats.reduce(
    (acc, item) => {
      if (
        item.gender === "male" ||
        item.gender === "female" ||
        item.gender === "other"
      ) {
        acc[item.gender] = {
          count: item.count,
          percentage: ((item.count / totalUsers) * 100).toFixed(2),
        };
      }
      return acc;
    },
    {
      male: { count: 0, percentage: 0 },
      female: { count: 0, percentage: 0 },
      other: { count: 0, percentage: 0 },
    }
  );

  return res.status(httpStatus.OK).json({
    success: true,
    message: "Gender statistics fetched successfully.",
    data: genderData,
  });
});

const getAgeStats = catchAsync(async (req, res) => {
  // Fetch all users where __t is not "Admin"
  const users = await userService.getAllWithoutAdmin();
  if (!users || users.length === 0) {
    return res.status(httpStatus.NOT_FOUND).json({
      success: false,
      message: "No users found",
    });
  }
  const ageGroups = {
    "18-24": 0,
    "25-34": 0,
    "35-44": 0,
    "45-54": 0,
    "55-64": 0,
    "65+": 0,
  };
  const calculateAge = (dob) => {
    const today = new Date();
    const birthDate = new Date(dob);
    let age = today.getFullYear() - birthDate.getFullYear();
    const m = today.getMonth() - birthDate.getMonth();
    if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  };

  users.forEach((user) => {
    if (!user.dob) return; // Skip users without a date of birth
    const age = calculateAge(user.dob);

    if (age >= 18 && age <= 24) ageGroups["18-24"]++;
    else if (age >= 25 && age <= 34) ageGroups["25-34"]++;
    else if (age >= 35 && age <= 44) ageGroups["35-44"]++;
    else if (age >= 45 && age <= 54) ageGroups["45-54"]++;
    else if (age >= 55 && age <= 64) ageGroups["55-64"]++;
    else if (age >= 65) ageGroups["65+"]++;
  });

  // Calculate percentages
  const totalUsers = users.length;
  const ageGroupPercentages = Object.fromEntries(
    Object.entries(ageGroups).map(([key, count]) => [
      key,
      ((count / totalUsers) * 100).toFixed(2),
    ])
  );

  // Send response
  return res.status(httpStatus.OK).json({
    success: true,
    message: "Age statistics fetched successfully.",
    data: {
      totalUsers,
      ageGroups: ageGroupPercentages,
    },
  });
});

const sendNotification = catchAsync(async (req, res) => {
  const { targetUser, targetRole, title, description } = req.body;

  let image = "";
  if (req.file) {
    // ✅ Application layer file size check
    if (req.file.size > 1 * 1024 * 1024) {
      throw new ApiError(httpStatus.BAD_REQUEST, "Image size must be ≤ 1MB");
    }
    [image] = await fileUploadService
      .s3Upload([req.file], "admin_notification_images")
      .catch((e) => {
        throw new ApiError(
          httpStatus.INTERNAL_SERVER_ERROR,
          `Failed to upload documents : ${e.message}`
        );
      });
  }

  const data = {
    title: title,
    description: description,
    type: notificationCategories.GENERAL,
    targetRole: targetRole ? targetRole : null,
    targetUser: targetUser ? targetUser : null,
    isCreatedByAdmin: true,
    image: image,
  };

  const notification = await appNotificationService.createAndSendNotification(
    data,
    targetRole ? targetRole : targetUser
  );

  if (!notification) {
    throw new ApiError(
      httpStatus.INTERNAL_SERVER_ERROR,
      "Error creating and sending appointment notification"
    );
  }

  return res.status(httpStatus.OK).json({
    success: true,
    message: "Notification created and sent successfully",
    data: notification,
  });
});

const getSentNotification = catchAsync(async (req, res) => {
  let { options, filters } = getPaginateConfig(req.query);
  filters.isCreatedByAdmin = true;

  const notifications = await appNotificationService.getNotifications(
    filters,
    options,
    null,
    req.user.__t
  );

  if (!notifications.results || notifications.results.length === 0) {
    return res.status(httpStatus.OK).json({
      message: "No Notifications found",
      data: {},
    });
  }
  return res.status(httpStatus.OK).json({
    message: "Notifications fetched successfully",
    data: notifications,
  });
});

const deleteSentNotifications = catchAsync(async (req, res) => {
  const { id } = req.params;

  const deletedNotification =
    await appNotificationService.markDeletedForAdmin(id);

  return res.status(200).json({
    success: true,
    message: "Notification marked as deleted for admin",
    data: deletedNotification,
  });
});

const createAccount = catchAsync(async (req, res) => {
  const { email, phone, name } = req.body;

  if (!req.headers.type) {
    throw new ApiError(httpStatus.BAD_REQUEST, "user type is required.");
  }
  if (
    req.headers?.type.toString() !== userTypes.ADVISER &&
    req.headers?.type.toString() !== userTypes.USER
  ) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Type not allowed");
  }
  let profilePic = null;

  try {
    const generatedPassword = generateStrongPassword();
    const userRecord = await getAuth().createUser({
      email,
      emailVerified: false,
      phoneNumber: phone,
      password: generatedPassword,
      displayName: name,
      disabled: false,
    });

    if (req.file) {
      [profilePic] = await fileUploadService
        .s3Upload([req.file], "profile_picture")
        .catch((e) => {
          throw new ApiError(
            httpStatus.INTERNAL_SERVER_ERROR,
            `Failed to upload documents : ${e.message}`
          );
        });
    }

    const userObj = {
      firebaseUid: userRecord.uid,
      email: userRecord.email,
      phone: userRecord.phoneNumber,
      name: userRecord.displayName,
      isEmailVerified: userRecord.emailVerified,
      firebaseSignInProvider:
        userRecord?.providerData?.[0]?.providerId || "unknown",
      ...req.body,
      profilePic: profilePic,
    };

    // 🛠️ Fix: Parse address if it comes as a string
    if (userObj.address && typeof userObj.address === "string") {
      try {
        userObj.address = JSON.parse(userObj.address);
      } catch (err) {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          "Invalid JSON format for address"
        );
      }
    }
    let user;

    switch (req.headers.type.toString()) {
      case "User":
        user = await authService.createUser(userObj);
        eventEmitter.emit(emitterEventNames.USER_SIGNED_UP, user);

        // Emit event to send login credentials via email
        eventEmitter.emit(emitterEventNames.SEND_LOGIN_CREDENTIALS, {
          email: userObj.email,
          name: userObj.name,
          password: generatedPassword,
          userType: "User",
        });

        break;

      case "Adviser":
        const timezone = req.headers?.timezone || appDefaults.TIMEZONE;
        if (!timezone) {
          return res
            .status(httpStatus.BAD_REQUEST)
            .send({ message: "Timezone header is missing" });
        }

        if (userObj.availability) {
          if (typeof userObj.availability === "string") {
            try {
              userObj.availability = JSON.parse(userObj.availability);
            } catch (error) {
              throw new ApiError(
                httpStatus.BAD_REQUEST,
                `Invalid JSON format for availability: ${error}`
              );
            }
          }
          // Convert availability times to UTC using the timezone
          const convertedAvailability = userObj.availability.map(
            (availability) => {
              return {
                ...availability,
                timeSlots: availability.timeSlots.map((slot) => {
                  // Parse the start and end times with the provided timezone
                  const startDateTime = moment.tz(
                    ` ${slot.start}`,
                    "HH:mm",
                    timezone
                  );
                  const endDateTime = moment.tz(
                    ` ${slot.end}`,
                    "HH:mm",
                    timezone
                  );

                  // Convert to UTC format
                  const startUTC = startDateTime.utc().format("HH:mm:ss[Z]");
                  const endUTC = endDateTime.utc().format("HH:mm:ss[Z]");

                  return {
                    ...slot,
                    start: startUTC,
                    end: endUTC,
                  };
                }),
              };
            }
          );

          userObj.availability = convertedAvailability;
        }
        user = await authService.createAdviser(userObj);

        // Emit event to send login credentials via email
        eventEmitter.emit(emitterEventNames.SEND_LOGIN_CREDENTIALS, {
          email: userObj.email,
          name: userObj.name,
          password: generatedPassword,
          userType: "Adviser",
        });

        break;

      default:
        return res.status(httpStatus.BAD_REQUEST).json({
          status: false,
          message:
            "Invalid user type. Allowed values are 'Users' or 'Adviser'.",
        });
    }

    return res.status(httpStatus.CREATED).json({
      status: true,
      message: `${req.headers.type.toString()} created successfully.`,
      data: user,
    });
  } catch (error) {
    if (error.code === "auth/email-already-exists") {
      throw new ApiError(
        httpStatus.CONFLICT,
        "Email already exists in Firebase."
      );
    }

    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, error);
  }
});

const expertiseChartData = catchAsync(async (req, res) => {
  const data = await Adviser.aggregate([
    { $match: { isDeleted: false, isVerified: true } }, // Exclude soft-deleted advisers

    { $unwind: "$specialization" }, // Unwind the array to count each specialization separately
    {
      $group: {
        _id: "$specialization",
        count: { $sum: 1 },
      },
    },
    { $sort: { count: -1 } },
  ]);

  return res.status(200).json({
    success: true,
    data,
  });
});

const getUserFinancialHealth = catchAsync(async (req, res) => {
  const userId = req.params.id;
  const financialHealthData = await userService.getFinancialData(userId);
  return res.status(httpStatus.OK).json({
    message: "financial health data fetched",
    data: financialHealthData, // Rounded to 2 decimal places
  });
});

const getUserSubscriptiondata = catchAsync(async (req, res) => {
  const userId = req.params.id;
  const userAppointments =
    await customerSubscriptionService.getCustomerSubscription({ user: userId });
  return res.status(httpStatus.OK).json({
    message: "User Subscription data fetched",
    data: userAppointments, // Rounded to 2 decimal places
  });
});

const averageSessionLength = catchAsync(async (req, res) => {
  const duration = await appointmentService.calculateAverageSessionDuration();
  return res.status(httpStatus.OK).json({
    message: "avg session data fetched",
    sessionLength: duration, // Rounded to 2 decimal places
  });
});

const avgBookingValue = catchAsync(async (req, res) => {
  const avgSessionCost = await appointmentService.calculateAverageSessionCost();
  return res.status(httpStatus.OK).json({
    message: "avg session cost fetched",
    sessionCost: avgSessionCost,
  });
});

const searchUser = catchAsync(async (req, res) => {
  const { keyword, role, page = 1, limit = 10 } = req.query;

  // Construct query dynamically
  let query = {}; // Exclude deleted users by default

  if (req.user.__t === userTypes.ADMIN) {
    if (role) query.__t = role; // Allow Admins to filter by role
  }

  if (keyword) {
    query.$or = [
      { name: { $regex: keyword, $options: "i" } }, // Case-insensitive name search
      { email: { $regex: keyword, $options: "i" } }, // Case-insensitive email search
      { phone: { $regex: keyword, $options: "i" } }, // Case-insensitive email search
    ];
  }

  // ✅ Call search service function
  const users = await userService.searchUser(query, page, Number(limit));

  return res.status(httpStatus.OK).json({
    message: "Users retrieved successfully",
    data: users,
  });
});

const addTestFunds = catchAsync(async (req, res) => {
  const transferMoney = await stripeService.addTestFunds();

  return res.status(200).json({ data: transferMoney });
});

const setCommissionPercentage = catchAsync(async (req, res) => {
  const { value } = req.body;

  if (value === undefined || isNaN(value)) {
    return res.status(httpStatus.BAD_REQUEST).json({
      message: "Commission percentage is required and must be a number.",
    });
  }

  const commissionPercentage = Number(value);

  // ✅ Save to AppSettings
  const updated = await appSettingsService.setSetting(
    "platformCommission",
    commissionPercentage
  );

  return res.status(httpStatus.OK).json({
    message: "Commission percentage updated successfully.",
    data: updated,
  });
});

const getCommissionPercentage = catchAsync(async (req, res) => {
  const commissionPercentage =
    await appSettingsService.getSetting("platformCommission");

  return res.status(httpStatus.OK).json({
    message: "Commission percentage fetched successfully.",
    percent: commissionPercentage,
  });
});

const getFees = catchAsync(async (req, res) => {
  const { filters, options } = getPaginateConfig(req.query);

  const fees = await revenueService.getAllFees(filters, options);

  return res.status(httpStatus.OK).json({
    message: "Fees fetched successfully.",
    data: fees,
  });
});

const getUserBookingsGraph = catchAsync(async (req, res) => {
  const { type = "monthly" } = req.query;

  if (type === "yearly") {
    const data = await appointmentService.getYearlyBookingStats();
    return res.status(httpStatus.OK).json({
      type: "yearly",
      data,
    });
  }

  const { year, monthlyBreakdown } =
    await appointmentService.getMonthlyBookingStats();
  return res.status(httpStatus.OK).json({
    type: "monthly",
    year,
    monthlyBreakdown,
  });
});

const getRefundStats = catchAsync(async (req, res) => {
  // We're calculating total refund percentage, so no filters needed
  const filters = {};

  // Get total count of all appointments
  const totalAppointments = await appointmentService.countAppointments(filters);

  if (totalAppointments === 0) {
    return res.status(httpStatus.OK).json({
      success: true,
      message: "No appointments found",
      data: {
        totalAppointments: 0,
        cancelledAppointments: 0,
        cancelledPercentage: 0,
      },
    });
  }

  // Get count of cancelled appointments
  const cancelledAppointments = await appointmentService.countAppointments({
    ...filters,
    status: "cancelled",
  });

  // Calculate cancellation percentage (which serves as our refund percentage)
  const cancelledPercentage = parseFloat(
    ((cancelledAppointments / totalAppointments) * 100).toFixed(2)
  );

  return res.status(httpStatus.OK).json({
    success: true,
    message: "Refund percentage retrieved successfully",
    data: {
      totalAppointments,
      cancelledAppointments,
      cancelledPercentage,
    },
  });
});

const sendAdviserInvitation = catchAsync(async (req, res) => {
  const { email, name } = req.body;

  if (!email) {
    throw new ApiError(httpStatus.BAD_REQUEST, "Email is required");
  }

  const doesUserExist = await Users.findOne({ email });

  if (doesUserExist) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,

      `${
        doesUserExist.__t === userTypes.ADVISER
          ? "Adviser"
          : doesUserExist.__t === userTypes.USER
            ? "User"
            : "Admin"
      } already exists with this email.`
    );
  }

  // Generate a unique registration link with a token
  const registrationToken = require("crypto").randomBytes(32).toString("hex");
  const registrationLink = `${req.protocol}://${req.get(
    "host"
  )}/register/adviser?token=${registrationToken}&email=${encodeURIComponent(
    email
  )}`;

  try {
    // Render email template
    const emailHtml = await ejs.renderFile(
      path.join(__dirname, "../views/emails/adviserInvitation.ejs"),
      {
        name: name || "",
        email,
        registrationLink,
      }
    );

    // Send email
    mailService.sendEmail({
      to: email,
      subject: "Join Money Owls as a Financial Adviser",
      html: emailHtml,
    });

    return res.status(httpStatus.OK).json({
      success: true,
      message: `Invitation sent successfully to ${email}`,
      data: {
        email,
        sentAt: new Date(),
      },
    });
  } catch (error) {
    console.error(`Error sending adviser invitation: ${error.message}`);
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, `${error.message}`);
  }
});

module.exports = {
  updateProfile,
  getAppointmentDetails,
  getCount,
  getAllUsers,
  // getAllAdvisers,
  getAdviserDocs,
  getGenderStats,
  getAgeStats,
  sendNotification,
  getSentNotification,
  approveAdviserDoc,
  rejectAdviserDoc,
  createAccount,
  expertiseChartData,
  getUserFinancialHealth,
  // getUserBookings,
  getUserSubscriptiondata,
  // getAdviserBookings,
  averageSessionLength,
  searchUser,
  addTestFunds,
  getProfile,
  setCommissionPercentage,
  getCommissionPercentage,
  getFees,
  avgBookingValue,
  getUserBookingsGraph,
  deleteSentNotifications,
  getRefundStats,
  sendAdviserInvitation,
};
