const express = require("express");
const { adviserAppointmentFeedbackController } = require("../../controllers");
const { firebaseAuth } = require("../../middlewares/firebaseAuth");
const validate = require("../../middlewares/validate");
const { feedbackValidation } = require("../../validations");

const router = express.Router();

// Get all feedback (admin only)
router.get(
  "/",
  firebase<PERSON><PERSON>("Admin"),
  adviserAppointmentFeedbackController.getAllFeedback
);

// Get adviser's feedback (adviser only)
router.get(
  "/adviser/me",
  firebase<PERSON><PERSON>("Adviser"),
  adviserAppointmentFeedbackController.getAdviserFeedback
);

// User responds to feedback (user only)
router.post(
  "/respond/:appointmentId",
  firebase<PERSON>uth("User"),
  validate(feedbackValidation.respondToFeedback),
  adviserAppointmentFeedbackController.respondToFeedback
);

// Create feedback for an appointment (adviser only)
router.post(
  "/:appointmentId",
  firebase<PERSON><PERSON>("Adviser"),
  // validate(feedbackValidation.createFeedback),
  adviserAppointmentFeedbackController.createFeedback
);

// Update adviser feedback (adviser only)
router.put(
  "/:appointmentId",
  firebaseAuth("Adviser"),
  validate(feedbackValidation.updateAdviserFeedback),
  adviserAppointmentFeedbackController.updateAdviserFeedback
);

// Get feedback for a specific appointment (adviser, user, or admin)
router.get(
  "/appointment/:id",
  firebaseAuth("Adviser,User,Admin"),
  validate(feedbackValidation.getAppointmentFeedback),
  adviserAppointmentFeedbackController.getAppointmentFeedback
);

module.exports = router;
