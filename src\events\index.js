const eventEmitter = require("./eventEmitter");
const logger = require("../config/logger");
const authEvents = require("./auth.event");
const appointmentEvents = require("./appointment.event");
const subscriptionEvents = require("./subscription.event");
const paymentEvents = require("./payments.events");
const adviserEvents = require("./adviser.event");
const appointmentFeedbackEvents = require("./appointmentFeedback.event");
const queryEvents = require("./query.event");
const refundEvents = require("./refund.event");
const agoraEvents = require("./agora.event");

authEvents(eventEmitter);
appointmentEvents(eventEmitter);
subscriptionEvents(eventEmitter);
paymentEvents(eventEmitter);
adviserEvents(eventEmitter);
appointmentFeedbackEvents(eventEmitter);
queryEvents(eventEmitter);
refundEvents(eventEmitter);
agoraEvents(eventEmitter);

logger.info("App events are now running! 🎉");
