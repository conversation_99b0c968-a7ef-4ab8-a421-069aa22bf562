const Joi = require("joi");
const { dbOptionsSchema, objectId } = require("./custom.validation");

const addQuery = {
  params: Joi.object({
    appointmentId: Joi.string().custom(objectId).required(),
  }),
  body: Joi.object({
    query: Joi.string().min(3).max(500).required(),
  }),
};

const getQueries = {
  query: Joi.object().keys({
    ...dbOptionsSchema,
    status: Joi.string().valid("resolved", "unresolved"),
  }),
};

const resolveQuery = {
  params: Joi.object({
    id: Joi.string().custom(objectId).required(),
  }),
  body: Joi.object().keys({
    message: Joi.string().min(3).max(500).required(),
  }),
};

module.exports = {
  addQuery,
  getQueries,
  resolveQuery,
};
