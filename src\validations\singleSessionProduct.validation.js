const Joi = require("joi");
const { objectId } = require("./custom.validation");

/**
 * Validation schema for updating a single session product
 */
const updateSession = {
  params: Joi.object().keys({
    id: Joi.string()
      .custom(objectId)
      .required()
      .messages({
        "string.empty": "Session ID is required",
        "any.required": "Session ID is required",
        "string.pattern.base": "Session ID must be a valid MongoDB ObjectId",
      }),
  }),
  body: Joi.object()
    .keys({
      name: Joi.string()
        .max(100)
        .messages({
          "string.base": "Name must be a string",
          "string.max": "Name cannot exceed 100 characters",
        }),
      duration: Joi.number()
        .integer()
        .positive()
        .messages({
          "number.base": "Duration must be a number",
          "number.integer": "Duration must be an integer",
          "number.positive": "Duration must be a positive number",
        }),
      price: Joi.number()
        .positive()
        .precision(2)
        .messages({
          "number.base": "Price must be a number",
          "number.positive": "Price must be a positive number",
          "number.precision": "Price cannot have more than 2 decimal places",
        }),
      isActive: Joi.boolean()
        .messages({
          "boolean.base": "isActive must be a boolean value",
        }),
      description: Joi.string()
        .allow("")
        .messages({
          "string.base": "Description must be a string",
        }),
    })
    .min(1)
    .messages({
      "object.min": "At least one field must be provided for update",
    }),
};

/**
 * Validation schema for creating a single session product
 */
const createSession = {
  body: Joi.object().keys({
    name: Joi.string()
      .max(100)
      .required()
      .messages({
        "string.base": "Name must be a string",
        "string.empty": "Name is required",
        "any.required": "Name is required",
        "string.max": "Name cannot exceed 100 characters",
      }),
    duration: Joi.number()
      .integer()
      .positive()
      .required()
      .messages({
        "number.base": "Duration must be a number",
        "number.empty": "Duration is required",
        "any.required": "Duration is required",
        "number.integer": "Duration must be an integer",
        "number.positive": "Duration must be a positive number",
      }),
    price: Joi.number()
      .positive()
      .precision(2)
      .required()
      .messages({
        "number.base": "Price must be a number",
        "number.empty": "Price is required",
        "any.required": "Price is required",
        "number.positive": "Price must be a positive number",
        "number.precision": "Price cannot have more than 2 decimal places",
      }),
    isActive: Joi.boolean()
      .default(true)
      .messages({
        "boolean.base": "isActive must be a boolean value",
      }),
    description: Joi.string()
      .allow("")
      .messages({
        "string.base": "Description must be a string",
      }),
  }),
};

/**
 * Validation schema for deleting a single session product
 */
const deleteSession = {
  params: Joi.object().keys({
    id: Joi.string()
      .custom(objectId)
      .required()
      .messages({
        "string.empty": "Session ID is required",
        "any.required": "Session ID is required",
        "string.pattern.base": "Session ID must be a valid MongoDB ObjectId",
      }),
  }),
};

module.exports = {
  updateSession,
  createSession,
  deleteSession,
};
