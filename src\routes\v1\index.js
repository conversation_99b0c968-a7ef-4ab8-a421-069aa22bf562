const express = require("express");
const userRoute = require("./user.route");
const authRoute = require("./auth.route");
const adminRoute = require("./admin.route");
const adviserRoute = require("./adviser.route");
const notificationRoute = require("./notification.route");
const queryRoute = require("./query.route");
const specializationRoute = require("./specialization.route");
const { authController } = require("../../controllers");
const appFeedBackRoute = require("./feedback.route");
const agoraRoute = require("./agora.route");
const chatRoute = require("./chat.route");
const faqRoute = require("./faq.route");
const appoinmentRoute = require("./appointment.route");
const paymentRoute = require("./payment.route");
const productsRoute = require("./products.route");
const stripeRoute = require("./stripe.route");
const customerSubscriptionRoute = require("./customerSubscription.route");
const revenueRoute = require("./revenue.route");
const payoutRoute = require("./payout.route");
const adviserFeedbackRoute = require("./adviserAppointmentFeedback.route");
const refundRoute = require("./refund.route");
const router = express.Router();

router.use("/auth", authRoute);
router.use("/user", userRoute);
router.use("/adviser", adviserRoute);
router.use("/admin", adminRoute);
router.use("/app", appFeedBackRoute);
router.use("/notifications", notificationRoute);
router.use("/query", queryRoute);
router.use("/specialization", specializationRoute);

router.get("/health", authController.health);
router.use("/agora", agoraRoute);
router.use("/chat", chatRoute);
router.use("/faq", faqRoute);
router.use("/appointment", appoinmentRoute);
router.use("/payments", paymentRoute);
router.use("/products", productsRoute);
router.use("/stripe", stripeRoute);
router.use("/subscription", customerSubscriptionRoute);
router.use("/revenue", revenueRoute);
router.use("/payouts", payoutRoute);
router.use("/feedback", adviserFeedbackRoute);
router.use("/refund", refundRoute);
module.exports = router;
