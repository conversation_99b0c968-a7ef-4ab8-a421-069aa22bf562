const { User, Adviser, Users, Admin } = require("../models");

async function createUser(user, referredBy) {
  // Ensure name is lowercase
  const userData = {
    ...user,
    name: user.name ? user.name.toLowerCase() : user.name,
    referredBy: referredBy ? referredBy._id : null,
  };

  const newUser = new User(userData);
  await newUser.save();
  return newUser;
}

async function getUserByFirebaseUId(id) {
  // console.log("iinside service id 1 = ", id);
  return Users.findOne({ firebaseUid: id });
}

async function createAdviser(user) {
  // Ensure name is lowercase
  const adviserData = {
    ...user,
    name: user.name ? user.name.toLowerCase() : user.name,
  };

  return Adviser.create(adviserData);
}

async function createAdmin(user) {
  // Ensure name is lowercase
  const adminData = {
    ...user,
    name: user.name ? user.name.toLowerCase() : user.name,
  };

  return Admin.create(adminData);
}

module.exports = {
  createUser,
  getUserByFirebaseUId,
  createAdviser,
  createAdmin,
};
