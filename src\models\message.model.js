const mongoose = require("mongoose");
const { paginate } = require("./plugins/paginate");

const attachmentSchema = new mongoose.Schema(
  {
    fileType: {
      type: String,
      enum: ["image", "video", "file", "audio"],
      required: true,
    },
    file: {
      url: { type: String, required: true },
      key: { type: String, required: true },
    },
  },
  { _id: false }
);

const messageSchema = new mongoose.Schema(
  {
    chat: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "ChatRoom",
      required: true,
    },
    sender: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    content: { type: String },
    attachments: [attachmentSchema],
    replyTo: { type: mongoose.Schema.Types.ObjectId, ref: "Message" }, // Reply to another message
    isDeletedForMe: {
      type: Boolean,
      default: false,
    },

    readBy: [
      { type: mongoose.Schema.Types.ObjectId, ref: "Users", default: [] },
    ],
  },
  { timestamps: true }
);

messageSchema.plugin(paginate);
messageSchema.index({ chat: 1 });
const Message = mongoose.model("Message", messageSchema);

module.exports = { Message };
