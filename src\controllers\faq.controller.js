const httpStatus = require("http-status");
const { Faq } = require("../models");
const ApiError = require("../utils/ApiError");
const catchAsync = require("../utils/catchAsync");

const createFaq = catchAsync(async (req, res) => {
  const { question, answer } = req.body;

  if (!question || !answer) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      "Question and Answer are required"
    );
  }

  const faq = new Faq({ question, answer });
  await faq.save();

  return res
    .status(httpStatus.CREATED)
    .json({ message: "FAQ created successfully", faq });
});

const getAllFaqs = catchAsync(async (req, res) => {
  const faqs = await Faq.find().sort({ createdAt: -1 });
  return res.status(httpStatus.OK).json({ faqs });
});

const getFaqById = catchAsync(async (req, res) => {
  const { id } = req.params;

  const faq = await Faq.findById(id);
  if (!faq) {
    throw new ApiError(httpStatus.NOT_FOUND, "FAQ not found");
  }

  return res.status(httpStatus.OK).json(faq);
});

const updateFaq = catchAsync(async (req, res) => {
  const { id } = req.params;
  const { question, answer } = req.body;
  if (!question || !answer) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      "Question and Answer are required"
    );
  }

  const updatedFaq = await Faq.findByIdAndUpdate(
    id,
    { question, answer, $inc: { __v: 1 } },
    { new: true }
  );
  if (!updatedFaq) {
    throw new ApiError(httpStatus.NOT_FOUND, "FAQ not found");
  }

  return res
    .status(httpStatus.OK)
    .json({ message: "FAQ updated successfully", updatedFaq });
});

const deleteFaq = catchAsync(async (req, res) => {
  const { id } = req.params;

  const deletedFaq = await Faq.findByIdAndDelete(id);
  if (!deletedFaq) {
    throw new ApiError(httpStatus.NOT_FOUND, "FAQ not found");
  }

  return res
    .status(httpStatus.OK)
    .json({ message: "FAQ deleted successfully" });
});

module.exports = {
  createFaq,
  getAllFaqs,
  getFaqById,
  updateFaq,
  deleteFaq,
};
