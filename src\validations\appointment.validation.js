const Joi = require("joi");
const { dbOptionsSchema, objectId } = require("./custom.validation");

const createAppointment = {
  params: Joi.object().keys({
    adviserId: Joi.string().custom(objectId).required().messages({
      "string.empty": "Adviser ID is required",
      "any.required": "Adviser ID is required",
      "string.pattern.base": "Adviser ID must be a valid MongoDB ObjectId",
    }),
  }),
  body: Joi.object().keys({
    date: Joi.string().required().messages({
      "string.empty": "Date is required",
      "any.required": "Date is required",
      "string.isoDate": "Date must be a valid ISO date format",
    }),
    timeSlot: Joi.object({
      start: Joi.string()

        .required()
        .messages({
          "string.empty": "Start time is required",
          "any.required": "Start time is required",
          "string.pattern.base":
            "Start time must be in HH:MM format (e.g. 12:30)",
        }),
      end: Joi.string()

        .required()
        .messages({
          "string.empty": "End time is required",
          "any.required": "End time is required",
          "string.pattern.base":
            "End time must be in HH:MM format (e.g. 13:00)",
        }),
    })
      .required()
      .messages({
        "any.required": "Time slot is required",
      }),
    mode: Joi.string()
      .valid("subscription", "payment", "free")
      .required()
      .messages({
        "string.empty": "Mode is required",
        "any.required": "Mode is required",
        "any.only": "Mode must be one of: subscription, payment, free",
      }),
  }),
  query: Joi.object().keys({
    followUp: Joi.string().valid("true", "false").optional(),
  }),
  headers: Joi.object({
    timezone: Joi.string().required().messages({
      "string.base": "Timezone must be a string",
    }),
  }).unknown(true),
};

const getAppointments = {
  query: Joi.object().keys({
    ...dbOptionsSchema,
    status: Joi.string()
      .valid("upcoming", "completed", "cancelled", "followup", "requested")
      .optional(),
    isFollowup: Joi.boolean().optional(),
    paymentStatus: Joi.string().valid("paid", "processing", "free").optional(),
    search: Joi.string().optional(),
    mode: Joi.string().valid("payment", "subscription", "free").optional(),
  }),
};

const getAppointmentDetails = {
  params: Joi.object().keys({
    id: Joi.string().custom(objectId).required().messages({
      "string.empty": "Appointment ID is required",
      "any.required": "Appointment ID is required",
      "string.pattern.base": "Appointment ID must be a valid MongoDB ObjectId",
    }),
  }),
};

const cancelAppointmentUser = {
  params: Joi.object().keys({
    appointmentId: Joi.string().custom(objectId).required().messages({
      "string.empty": "Appointment ID is required",
      "any.required": "Appointment ID is required",
      "string.pattern.base": "Appointment ID must be a valid MongoDB ObjectId",
    }),
  }),
};

const getAvailableSlots = {
  params: Joi.object({
    adviserId: Joi.string().custom(objectId).required().messages({
      "string.empty": "Adviser ID is required.",
    }),
  }),

  body: Joi.object({
    date: Joi.string().required().messages({
      "string.base": "Date must be a string.",
      "string.empty": "Date is required.",
    }),

    duration: Joi.number().integer().valid(30, 15, 60).required().messages({
      "number.base": "Duration must be a number.",
      "number.empty": "Duration is required.",
      "any.only": "Duration must be one of [30, 60].",
    }),
  }),

  headers: Joi.object({
    timezone: Joi.string().messages({
      "string.base": "Timezone must be a string.",
    }),
  }),
};

const rescheduleAppointment = {
  params: Joi.object().keys({
    appointmentId: Joi.string().custom(objectId).required().messages({
      "string.empty": "Appointment ID is required",
      "any.required": "Appointment ID is required",
      "string.pattern.base": "Appointment ID must be a valid MongoDB ObjectId",
    }),
  }),
  body: Joi.object().keys({
    date: Joi.string().isoDate().required().messages({
      "string.empty": "Date is required",
      "any.required": "Date is required",
      "string.isoDate": "Date must be a valid ISO date format",
    }),
    timeSlot: Joi.object({
      start: Joi.string()
        .pattern(/^([01]\d|2[0-3]):([0-5]\d)$/)
        .required()
        .messages({
          "string.empty": "Start time is required",
          "any.required": "Start time is required",
          "string.pattern.base":
            "Start time must be in HH:MM format (e.g. 12:30)",
        }),
      end: Joi.string()
        .pattern(/^([01]\d|2[0-3]):([0-5]\d)$/)
        .required()
        .messages({
          "string.empty": "End time is required",
          "any.required": "End time is required",
          "string.pattern.base":
            "End time must be in HH:MM format (e.g. 13:00)",
        }),
    })
      .required()
      .messages({
        "any.required": "Time slot is required",
      }),
  }),
  headers: Joi.object({
    timezone: Joi.string().optional().messages({
      "string.base": "Timezone must be a string",
    }),
  }).unknown(true),
};

const updateAppointment = {
  params: Joi.object().keys({
    appointmentId: Joi.string().custom(objectId).required().messages({
      "string.empty": "Appointment ID is required",
      "any.required": "Appointment ID is required",
      "string.pattern.base": "Appointment ID must be a valid MongoDB ObjectId",
    }),
  }),
  body: Joi.object().keys({
    appointmentData: Joi.object().required().messages({
      "any.required": "Appointment data is required",
    }),
  }),
};

const addNotes = {
  body: Joi.object().keys({
    note: Joi.string().required().messages({
      "string.empty": "Note is required",
      "any.required": "Note is required",
    }),
  }),
};

const acceptAppointment = {
  params: Joi.object().keys({
    id: Joi.string().custom(objectId).required().messages({
      "string.empty": "Appointment ID is required",
      "any.required": "Appointment ID is required",
      "string.pattern.base": "Appointment ID must be a valid MongoDB ObjectId",
    }),
  }),
};

const markAppointmentComplete = {
  params: Joi.object().keys({
    id: Joi.string().custom(objectId).required().messages({
      "string.empty": "Appointment ID is required",
      "any.required": "Appointment ID is required",
      "string.pattern.base": "Appointment ID must be a valid MongoDB ObjectId",
    }),
  }),
};

const toggleAppointmentReminder = {
  params: Joi.object().keys({
    id: Joi.string().custom(objectId).required().messages({
      "string.empty": "Appointment ID is required",
      "any.required": "Appointment ID is required",
      "string.pattern.base": "Appointment ID must be a valid MongoDB ObjectId",
    }),
  }),
};

const addWhiteboardScreenshot = {
  params: Joi.object().keys({
    appointmentId: Joi.string().custom(objectId).required().messages({
      "string.empty": "Appointment ID is required",
      "any.required": "Appointment ID is required",
      "string.pattern.base": "Appointment ID must be a valid MongoDB ObjectId",
    }),
  }),
  // No body validation as we're expecting a file upload
};

const getAppointmentPayoutStatus = {
  params: Joi.object().keys({
    appointmentId: Joi.string().custom(objectId).required().messages({
      "string.empty": "Appointment ID is required",
      "any.required": "Appointment ID is required",
      "string.pattern.base": "Appointment ID must be a valid MongoDB ObjectId",
    }),
  }),
};

module.exports = {
  createAppointment,
  getAppointments,
  rescheduleAppointment,
  getAppointmentDetails,
  getAvailableSlots,
  addNotes,
  cancelAppointmentUser,
  updateAppointment,
  acceptAppointment,
  markAppointmentComplete,
  toggleAppointmentReminder,
  addWhiteboardScreenshot,
  getAppointmentPayoutStatus,
};
